plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'org.greenrobot.greendao'
}


android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        applicationId "com.business_clean"

        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion

        versionCode 46
        versionName "1.8.0.14"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        ndk {
            // 设置支持的SO库架构（开发者可以根据需要，选择一个或多个平台的so）
            abiFilters "armeabi-v7a", "arm64-v8a", 'x86'
        }

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        //使用asXxx方法时必须，告知RxHttp你依赖的rxjava版本，可传入rxjava2、rxjava3
                        rxhttp_rxjava: 'rxjava3',
                ]
            }
        }
    }

    //每次数据库改版，必须修改数据库版本号
    greendao {
        schemaVersion 4 //数据库版本号
        daoPackage 'com.business_clean.data.database.greenDao.db'
        // 设置DaoMaster、DaoSession、Dao 包名
        targetGenDir 'src/main/java'//设置DaoMaster、DaoSession、Dao目录,请注意，这里路径用/不要用.
        generateTests false //设置为true以自动生成单元测试。
        targetGenDirTests 'src/main/java' //应存储生成的单元测试的基本目录。默认为 src / androidTest / java。
    }


    signingConfigs {
        release {
            keyAlias 'clean'
            keyPassword 'XiongMao2016'
            storeFile file('./business_clean.jks')
            storePassword 'XiongMao2016'
        }
    }


    buildTypes {
        release {
            debuggable = false
            minifyEnabled true
            shrinkResources true
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        debug {
            debuggable = true
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        profile {
            initWith debug
        }
    }

    // 自定义打包apk名称
    android.applicationVariants.configureEach {
        variant ->
            variant.outputs.all {
                outputFileName = "xiongmao-clean-v${variant.versionName}.apk"
            }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }


    buildFeatures {
        //这2个为非必选，想用哪个就保留那个 用的话一定要加上项目中的 ViewBinding & DataBinding 混淆规则
        dataBinding = true
        viewBinding = true
    }


    compileOptions {
        sourceCompatibility = 11
        targetCompatibility = 11
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    lintOptions {
        disable 'InvalidPackage'
        disable "ResourceType"
        abortOnError false
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
}

// flutter 的引入 aar 的方式
String storageUrl = System.env.FLUTTER_STORAGE_BASE_URL ?: "https://storage.googleapis.com"
repositories {
    jcenter()
    google()
    mavenCentral()
    flatDir {
        dirs 'libs'
    }
    maven {//必须保证flutter module 项目保持同级
        url '../../../BusinessCleanFlutterPro/xiongmao_clean_flutter_module/build/host/outputs/repo'
    }
    maven {
        url "$storageUrl/download.flutter.io"
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'], exclude: [])


    //项目核心框架
    implementation project(path: ':helper')
    //相册
    implementation project(path: ':selector')
    //图片编辑的库
    implementation project(path: ':drawboard')
    //相机本地库，引入进来
    implementation project(path: ':cameraview')

    //高德地图集成T
    implementation 'com.amap.api:3dmap-location-search:latest.integration'

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    //test
    testImplementation 'junit:junit:4.13.1'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    implementation 'com.android.support:multidex:1.0.3'
//    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.4'
    //androidx UI
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
    implementation 'androidx.preference:preference-ktx:1.1.1'
    //dialog
    implementation "com.afollestad.material-dialogs:lifecycle:3.3.0"
    implementation "com.afollestad.material-dialogs:core:3.3.0"
    implementation "com.afollestad.material-dialogs:color:3.3.0"
    implementation "com.afollestad.material-dialogs:datetime:3.3.0"
    implementation "com.afollestad.material-dialogs:bottomsheets:3.3.0"

    //rxhttp
    kapt 'com.github.liujingxing.rxhttp:rxhttp-compiler:2.8.3'

    //微信开源项目，替代SP
    implementation 'com.tencent:mmkv:1.0.22'
    //轮播图  https://github.com/zhpanvip/BannerViewPager
//    implementation 'com.github.zhpanvip:BannerViewPager:3.1.5'
    //recycler 列表组件
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    //管理界面状态库
    implementation 'com.kingja.loadsir:loadsir:1.3.8'
    //glide
    implementation 'com.github.bumptech.glide:glide:4.11.0'

    //防崩溃
    implementation 'cat.ereza:customactivityoncrash:2.3.0'
    // Xpopup https://github.com/junixapp/XPopup 就这个版本，升级会有冲突
    implementation 'com.github.li-xiaojun:XPopup:2.9.4'
    //阿里的json
    implementation 'com.alibaba:fastjson:1.1.52.android'
    //权限
    implementation 'com.github.getActivity:XXPermissions:18.63'
    //时间选择器
    implementation 'com.contrarywind:Android-PickerView:4.1.9'
    //七牛云
    implementation 'com.qiniu:qiniu-android-sdk:8.2.0'
    //相机库
//    implementation 'com.otaliastudios:cameraview:2.7.2'
    //图片 配合相机
    implementation "jp.wasabeef:glide-transformations:3.3.0"
    //视频合成
    implementation 'com.googlecode.mp4parser:isoparser:1.1.22'
    //增加微信分享
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.0'
    //AgentWeb
    implementation 'io.github.justson:agentweb-core:v5.1.1-androidx'
    //升级
    implementation 'com.github.yjfnypeu:UpdatePlugin:3.1.3'
    //日历
    implementation 'com.haibin:calendarview:3.7.1'
    //增加一个裁剪
    implementation 'io.github.lucksiege:ucrop:v3.11.2'
    // 图片压缩 (按需引入)
    implementation 'io.github.lucksiege:compress:v3.11.2'
    //播放器 懒得写了，直接引入吧
//    implementation 'com.github.CarGuo.GSYVideoPlayer:GSYVideoPlayer:v8.1.7-jitpack'
    implementation 'com.github.CarGuo.GSYVideoPlayer:gsyvideoplayer:v10.0.0'

    //本地数据库
    implementation 'org.greenrobot:greendao:3.3.0'
    //底部导航栏
    implementation 'com.github.chaychan:BottomBarLayout:3.0.0'
    // 步骤视图 https://github.com/shuhart/StepView
    implementation 'com.github.shuhart:stepview:1.5.1'

    //bugly   其中latest.release指代最新版本号，也可以指定明确的版本号，例如2.1.5
    implementation 'com.tencent.bugly:crashreport:latest.release'
    implementation 'com.tencent.bugly:nativecrashreport:latest.release'

    //增加腾讯的X5
    implementation 'com.tencent.tbs.tbssdk:sdk:43967'

    //Flutter的内容
    debugImplementation('com.business.clean.xiongmao_clean_flutter_module:flutter_debug:1.0') {
        exclude group: 'io.github.lucksiege', module: 'pictureselector'
        exclude group: 'io.github.lucksiege', module: 'compress'
        exclude group: 'io.github.lucksiege', module: 'ucrop'
    }
    profileImplementation('com.business.clean.xiongmao_clean_flutter_module:flutter_profile:1.0') {
        exclude group: 'io.github.lucksiege', module: 'pictureselector'
        exclude group: 'io.github.lucksiege', module: 'compress'
        exclude group: 'io.github.lucksiege', module: 'ucrop'
    }
    releaseImplementation('com.business.clean.xiongmao_clean_flutter_module:flutter_release:1.0') {
        exclude group: 'io.github.lucksiege', module: 'pictureselector'
        exclude group: 'io.github.lucksiege', module: 'compress'
        exclude group: 'io.github.lucksiege', module: 'ucrop'
    }
    //使用 flutter boost 必须引入这个
    debugImplementation 'com.idlefish.flutterboost:flutter_boost_debug:1.0'
    profileImplementation 'com.idlefish.flutterboost:flutter_boost_profile:1.0'
    releaseImplementation 'com.idlefish.flutterboost:flutter_boost_release:1.0'

}