<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.business_clean">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <!-- 指纹权限 -->
    <uses-permission
        android:name="android.permission.MANAGE_FINGERPRINT"
        tools:ignore="ProtectedPermissions" />
    <!-- 指纹管理权限 -->
    <uses-permission android:name="cn.org.ifaa.permission.USE_IFAA_MANAGER" />
    <!-- ⼈脸权限 -->
    <uses-permission android:name="android.permission.USE_FACERECOGNITION" />
    <uses-permission android:name="oppo.permission.USE_FACE" />
    <!-- 个别⼚商⼈脸权限 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 检查wifi网络状态 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <!-- 切换网络通道 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <queries package="${applicationId}">
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
        </intent>
    </queries>

    <application
        android:name=".app.App"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/logo_512"
        android:label="@string/app_name"
        android:maxAspectRatio="2.4"
        android:networkSecurityConfig="@xml/network_security_config"
        android:preserveLegacyExternalStorage="true"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:roundIcon="@mipmap/logo_512"
        android:supportsRtl="true"
        android:theme="@style/Theme.Business_clean"
        tools:replace="android:networkSecurityConfig">


        <activity
            android:name=".ui.activity.project.InsureCompanyActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.camera.WaterUploadRecordActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.CheckPermissionActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.me.MyPhotosActivity"
            android:exported="false" />

        <activity
            android:name=".ui.activity.insure.ElePolicyActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.insure.ClaimsProcessActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.insure.ClaimsProcessDetailActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.custom.ContractPreviewActivity"
            android:exported="false" />

        <activity
            android:name=".ui.activity.clean.CleanReportActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.todo.WorkTaskDetailActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.balance.CorporateRechargeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activity.custom.ContractSignWebActivity"
            android:screenOrientation="portrait" />

        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" /> <!-- 适配华为（huawei）刘海屏 -->
        <meta-data
            android:name="android.notch_support"
            android:value="true" /> <!-- 适配小米（xiaomi）刘海屏 -->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />

        <activity
            android:name=".ui.activity.roster.RosterFilterActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.login.TestActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.company.SelectCompanyActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.BaseH5Activity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.address.PreviewAddressActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.PreviewPhotoActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activity.personnel.PersonnelDetailActivity"
            android:exported="false" />

        <meta-data
            android:name="ScopedStorage"
            android:value="true" />

        <service
            android:name="com.amap.api.location.APSService"
            android:foregroundServiceType="location" />

        <activity
            android:name=".ui.activity.StartActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activity.camera.CameraSettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activity.camera.WatermarkCameraActivity"
            android:screenOrientation="portrait"
            android:theme="@style/NoTitleFullscreen" />
        <activity
            android:name=".ui.activity.attendance.MyAttendanceActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activity.address.AddAddressActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activity.custom.CommonContactManagementActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activity.permissions.PermissionsActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activity.me.MyCenterActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activity.login.LoginActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activity.main.MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activity.SplashActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activity.ErrorActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activity.camera.EditPhotoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activity.todo.TodoDetailActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activity.personnel.SignHandActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name=".ui.activity.format.FormatActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".app.flutter.XmFlutterBoostActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
            android:hardwareAccelerated="true"
            android:theme="@style/FlutterTheme"
            android:windowSoftInputMode="adjustResize" />
        <!-- Arms 配置 -->
        <meta-data
            android:name="design_width_in_dp"
            android:value="375" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="667" />

        <activity android:name=".app.fdd.FddWebActivity" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 联通电信授权页 -->
        <!-- 如果不需要使用窗口模式，不要使用authsdk_activity_dialog主题，会出现异常动画 -->
        <!--
如果需要使用authsdk_activity_dialog主题，则screenOrientation一定不能指定明确的方向，
            比如portrait、sensorPortrait，在8.0的系统上不允许窗口模式指定orientation，会发生Crash，需要指定为behind，
            然后在授权页的前一个页面指定具体的orientation
        -->
        <!-- 使用弹窗模式必须添加。 -->
        <activity
            android:name="com.mobile.auth.gatewayauth.LoginAuthActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:theme="@style/authsdk_activity_dialog" /> <!-- 二次弹窗界面 -->
        <activity
            android:name="com.mobile.auth.gatewayauth.PrivacyDialogActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@style/authsdk_activity_dialog" /> <!-- 协议页面webview -->
        <activity
            android:name="com.mobile.auth.gatewayauth.activity.AuthWebVeiwActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="behind" />
        <activity
            android:name=".wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTop" /> <!-- <activity -->
        <!-- android:name=".wxapi.WXEntryActivity" -->
        <!-- android:exported="true" -->
        <!-- android:launchMode="singleTop" /> -->
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.alipay.sdk.app.H5PayActivity"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.alipay.sdk.app.H5AuthActivity"
            android:configChanges="orientation|keyboardHidden|navigation"
            android:exported="false"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".ui.activity.contract.ChooseNativeWorkFileActivity"
            android:screenOrientation="portrait" />
    </application>

</manifest>