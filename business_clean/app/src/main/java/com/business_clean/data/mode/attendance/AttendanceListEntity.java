package com.business_clean.data.mode.attendance;

import java.io.Serializable;

public class AttendanceListEntity implements Serializable {
    private String uuid;
    private String stat_uuid;
    private String user_uuid;
    private String user_name;
    private String job_name;
    private String avatar;
    private String sex;
    private String sex_name;
    private String group_name;
    private String class_name;
    private String attendance_status;
    private String attendance_status_name;

    private String attendance_days;
    private String actual_attendance_days;
    private String is_absenteeism;
    private String is_attendance;
    private String is_holiday;
    private String is_qk;
    private String is_overtime;
    private String is_rest;

    private String work_start_time;
    private String work_end_time;

    public void setWork_end_time(String work_end_time) {
        this.work_end_time = work_end_time;
    }

    public void setWork_start_time(String work_start_time) {
        this.work_start_time = work_start_time;
    }

    public String getWork_end_time() {
        return work_end_time;
    }

    public String getWork_start_time() {
        return work_start_time;
    }

    public void setClass_name(String class_name) {
        this.class_name = class_name;
    }

    public String getClass_name() {
        return class_name;
    }

    public void setSex_name(String sex_name) {
        this.sex_name = sex_name;
    }

    public String getSex_name() {
        return sex_name;
    }

    public void setStat_uuid(String stat_uuid) {
        this.stat_uuid = stat_uuid;
    }

    public String getStat_uuid() {
        return stat_uuid;
    }

    public void setIs_rest(String is_rest) {
        this.is_rest = is_rest;
    }

    public String getIs_rest() {
        return is_rest;
    }

    public String getIs_attendance() {
        return is_attendance;
    }

    public void setIs_attendance(String is_attendance) {
        this.is_attendance = is_attendance;
    }

    public String getIs_holiday() {
        return is_holiday;
    }

    public void setIs_holiday(String is_holiday) {
        this.is_holiday = is_holiday;
    }

    public String getIs_qk() {
        return is_qk;
    }

    public void setIs_qk(String is_qk) {
        this.is_qk = is_qk;
    }

    public String getIs_overtime() {
        return is_overtime;
    }

    public void setIs_overtime(String is_overtime) {
        this.is_overtime = is_overtime;
    }

    public void setIs_absenteeism(String is_absenteeism) {
        this.is_absenteeism = is_absenteeism;
    }

    public String getIs_absenteeism() {
        return is_absenteeism;
    }

    public void setActual_attendance_days(String actual_attendance_days) {
        this.actual_attendance_days = actual_attendance_days;
    }

    public void setAttendance_days(String attendance_days) {
        this.attendance_days = attendance_days;
    }

    public String getActual_attendance_days() {
        return actual_attendance_days;
    }

    public String getAttendance_days() {
        return attendance_days;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSex() {
        return sex;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUser_uuid() {
        return user_uuid;
    }

    public void setUser_uuid(String user_uuid) {
        this.user_uuid = user_uuid;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getJob_name() {
        return job_name;
    }

    public void setJob_name(String job_name) {
        this.job_name = job_name;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getGroup_name() {
        return group_name;
    }

    public void setGroup_name(String group_name) {
        this.group_name = group_name;
    }

    public String getAttendance_status() {
        return attendance_status;
    }

    public void setAttendance_status(String attendance_status) {
        this.attendance_status = attendance_status;
    }

    public String getAttendance_status_name() {
        return attendance_status_name;
    }

    public void setAttendance_status_name(String attendance_status_name) {
        this.attendance_status_name = attendance_status_name;
    }
}
