package com.business_clean.data.mode.address;

import java.io.Serializable;

public class CustomPoiEntity implements Serializable {
    private double mRank;
    private String mId;
    private String mName;
    private String mTags;
    private String mAddr;

    private double lat;

    private double lnt;


    public CustomPoiEntity(double mRank, String mId, String mName, String mTags, String mAddr, double lat, double lnt) {
        this.mRank = mRank;
        this.mId = mId;
        this.mName = mName;
        this.mTags = mTags;
        this.mAddr = mAddr;
        this.lat = lat;
        this.lnt = lnt;
    }

    public double getRank() {
        return mRank;
    }

    public void setRank(double mRank) {
        this.mRank = mRank;
    }

    public String getId() {
        return mId;
    }

    public void setId(String mId) {
        this.mId = mId;
    }

    public String getName() {
        return mName;
    }

    public void setName(String mName) {
        this.mName = mName;
    }

    public String getTags() {
        return mTags;
    }

    public void setTags(String mTags) {
        this.mTags = mTags;
    }

    public String getAddr() {
        return mAddr;
    }

    public void setAddr(String mAddr) {
        this.mAddr = mAddr;
    }

    public double getLat() {
        return lat;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public double getLnt() {
        return lnt;
    }

    public void setLnt(double lnt) {
        this.lnt = lnt;
    }
}
