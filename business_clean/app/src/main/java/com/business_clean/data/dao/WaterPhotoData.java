package com.business_clean.data.dao;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Unique;
import org.greenrobot.greendao.annotation.Generated;

@Entity
public class WaterPhotoData {
    @Id(autoincrement = true)//自增 id
    Long id;
    int act_type;//当前拍照的类型，是领班打卡还是日常工作
    int message_type;//拍照的类型，是照片还是视频
    String task_uuid;//任务id 只针对任务才会有这个参数
    String project_uuid;//项目id 当前项目是那个
    String video_cover;//视频的封面图 自己生成的
    String video_path;//视频的路径本地的路径
    String photo_water_path;//图片的本地路径
    String photo_origin_path;//图片原图的本地路径
    String photo_thumb_path;//图片的缩略图
    double lat;//经纬度
    double lnt;//经纬度
    String address;//当前的地址
    long now_current_time_millis;//当前时间戳/1000
    String security_code;//防伪码（就是当前时间错）
    String user_uuid_list;//集体打卡  需要的字段

    int upload_status = 0;//图片上传照片 0: 等待中 1: 上传中 2: 失败 3：成功

    String requestMsg;//请求回来的msg 情况

    @Generated(hash = 720724901)
    public WaterPhotoData(Long id, int act_type, int message_type, String task_uuid,
            String project_uuid, String video_cover, String video_path,
            String photo_water_path, String photo_origin_path,
            String photo_thumb_path, double lat, double lnt, String address,
            long now_current_time_millis, String security_code,
            String user_uuid_list, int upload_status, String requestMsg) {
        this.id = id;
        this.act_type = act_type;
        this.message_type = message_type;
        this.task_uuid = task_uuid;
        this.project_uuid = project_uuid;
        this.video_cover = video_cover;
        this.video_path = video_path;
        this.photo_water_path = photo_water_path;
        this.photo_origin_path = photo_origin_path;
        this.photo_thumb_path = photo_thumb_path;
        this.lat = lat;
        this.lnt = lnt;
        this.address = address;
        this.now_current_time_millis = now_current_time_millis;
        this.security_code = security_code;
        this.user_uuid_list = user_uuid_list;
        this.upload_status = upload_status;
        this.requestMsg = requestMsg;
    }

    @Generated(hash = 1970443893)
    public WaterPhotoData() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public int getAct_type() {
        return this.act_type;
    }

    public void setAct_type(int act_type) {
        this.act_type = act_type;
    }

    public int getMessage_type() {
        return this.message_type;
    }

    public void setMessage_type(int message_type) {
        this.message_type = message_type;
    }

    public String getTask_uuid() {
        return this.task_uuid;
    }

    public void setTask_uuid(String task_uuid) {
        this.task_uuid = task_uuid;
    }

    public String getProject_uuid() {
        return this.project_uuid;
    }

    public void setProject_uuid(String project_uuid) {
        this.project_uuid = project_uuid;
    }

    public String getVideo_cover() {
        return this.video_cover;
    }

    public void setVideo_cover(String video_cover) {
        this.video_cover = video_cover;
    }

    public String getVideo_path() {
        return this.video_path;
    }

    public void setVideo_path(String video_path) {
        this.video_path = video_path;
    }

    public String getPhoto_water_path() {
        return this.photo_water_path;
    }

    public void setPhoto_water_path(String photo_water_path) {
        this.photo_water_path = photo_water_path;
    }

    public String getPhoto_origin_path() {
        return this.photo_origin_path;
    }

    public void setPhoto_origin_path(String photo_origin_path) {
        this.photo_origin_path = photo_origin_path;
    }

    public String getPhoto_thumb_path() {
        return this.photo_thumb_path;
    }

    public void setPhoto_thumb_path(String photo_thumb_path) {
        this.photo_thumb_path = photo_thumb_path;
    }

    public double getLat() {
        return this.lat;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public double getLnt() {
        return this.lnt;
    }

    public void setLnt(double lnt) {
        this.lnt = lnt;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public long getNow_current_time_millis() {
        return this.now_current_time_millis;
    }

    public void setNow_current_time_millis(long now_current_time_millis) {
        this.now_current_time_millis = now_current_time_millis;
    }

    public String getSecurity_code() {
        return this.security_code;
    }

    public void setSecurity_code(String security_code) {
        this.security_code = security_code;
    }

    public String getUser_uuid_list() {
        return this.user_uuid_list;
    }

    public void setUser_uuid_list(String user_uuid_list) {
        this.user_uuid_list = user_uuid_list;
    }

    public int getUpload_status() {
        return this.upload_status;
    }

    public void setUpload_status(int upload_status) {
        this.upload_status = upload_status;
    }

    public String getRequestMsg() {
        return this.requestMsg;
    }

    public void setRequestMsg(String requestMsg) {
        this.requestMsg = requestMsg;
    }

   
}
