package com.business_clean.data.database.greenDao.db;

import java.util.Map;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.AbstractDaoSession;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.identityscope.IdentityScopeType;
import org.greenrobot.greendao.internal.DaoConfig;

import com.business_clean.data.dao.WaterPhotoData;

import com.business_clean.data.database.greenDao.db.WaterPhotoDataDao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.

/**
 * {@inheritDoc}
 * 
 * @see org.greenrobot.greendao.AbstractDaoSession
 */
public class DaoSession extends AbstractDaoSession {

    private final DaoConfig waterPhotoDataDaoConfig;

    private final WaterPhotoDataDao waterPhotoDataDao;

    public DaoSession(Database db, IdentityScopeType type, Map<Class<? extends AbstractDao<?, ?>>, DaoConfig>
            daoConfigMap) {
        super(db);

        waterPhotoDataDaoConfig = daoConfigMap.get(WaterPhotoDataDao.class).clone();
        waterPhotoDataDaoConfig.initIdentityScope(type);

        waterPhotoDataDao = new WaterPhotoDataDao(waterPhotoDataDaoConfig, this);

        registerDao(WaterPhotoData.class, waterPhotoDataDao);
    }
    
    public void clear() {
        waterPhotoDataDaoConfig.clearIdentityScope();
    }

    public WaterPhotoDataDao getWaterPhotoDataDao() {
        return waterPhotoDataDao;
    }

}
