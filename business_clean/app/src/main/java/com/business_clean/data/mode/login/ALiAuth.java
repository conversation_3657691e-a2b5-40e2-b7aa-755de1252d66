package com.business_clean.data.mode.login;

import java.io.Serializable;

public class ALi<PERSON>uth implements Serializable {
    private String access_token;
    private String expire_time;
    
    private String auth_uuid;

    public void setAuth_uuid(String auth_uuid) {
        this.auth_uuid = auth_uuid;
    }

    public String getAuth_uuid() {
        return auth_uuid;
    }

    public String getAccess_token() {
        return access_token;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public String getExpire_time() {
        return expire_time;
    }

    public void setExpire_time(String expire_time) {
        this.expire_time = expire_time;
    }

    @Override
    public String toString() {
        return "ALiAuth{" +
                "access_token='" + access_token + '\'' +
                ", expire_time='" + expire_time + '\'' +
                '}';
    }
}
