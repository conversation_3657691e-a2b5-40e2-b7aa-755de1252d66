package com.business_clean.data.mode.circle;

import java.io.Serializable;
import java.util.List;

public class ChatMessageCompactLIst implements Serializable {
    private List<ChatMessageCompact> list;

    private String total;

    private String latest_record_uuid;

    public void setTotal(String total) {
        this.total = total;
    }

    public String getTotal() {
        return total;
    }

    public void setLatest_record_uuid(String latest_record_uuid) {
        this.latest_record_uuid = latest_record_uuid;
    }

    public String getLatest_record_uuid() {
        return latest_record_uuid;
    }

    public void setList(List<ChatMessageCompact> list) {
        this.list = list;
    }

    public List<ChatMessageCompact> getList() {
        return list;
    }
}
