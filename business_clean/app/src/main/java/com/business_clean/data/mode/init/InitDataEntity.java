package com.business_clean.data.mode.init;

import com.business_clean.data.initconfig.BankInfoEntity;
import com.business_clean.data.initconfig.SuperiorsLevelEntity;

import java.io.Serializable;
import java.util.List;

public class InitDataEntity implements Serializable {
    private List<BaseIDNameEntity> dimission_reason_list;

    private ClassTime in_class_time_list;

    private OutTime out_class_time_list;

    //身份
    private List<BaseIDNameEntity> exigency_user_relation_list;

    private List<BankInfoEntity> bank_list;

    private List<SuperiorsLevelEntity> superiors_level_list;

    public List<SuperiorsLevelEntity> getSuperiors_level_list() {
        return superiors_level_list;
    }

    public void setSuperiors_level_list(List<SuperiorsLevelEntity> superiors_level_list) {
        this.superiors_level_list = superiors_level_list;
    }

    public List<BaseIDNameEntity> getExigency_user_relation_list() {
        return exigency_user_relation_list;
    }

    public void setExigency_user_relation_list(List<BaseIDNameEntity> exigency_user_relation_list) {
        this.exigency_user_relation_list = exigency_user_relation_list;
    }

    public List<BankInfoEntity> getBank_list() {
        return bank_list;
    }

    public void setBank_list(List<BankInfoEntity> bank_list) {
        this.bank_list = bank_list;
    }

    public ClassTime getIn_class_time_list() {
        return in_class_time_list;
    }


    public void setDimission_reason_list(List<BaseIDNameEntity> dimission_reason_list) {
        this.dimission_reason_list = dimission_reason_list;
    }

    public List<BaseIDNameEntity> getDimission_reason_list() {
        return dimission_reason_list;
    }

    public void setIn_class_time_list(ClassTime in_class_time_list) {
        this.in_class_time_list = in_class_time_list;
    }

    public OutTime getOut_class_time_list() {
        return out_class_time_list;
    }

    public void setOut_class_time_list(OutTime out_class_time_list) {
        this.out_class_time_list = out_class_time_list;
    }
}
