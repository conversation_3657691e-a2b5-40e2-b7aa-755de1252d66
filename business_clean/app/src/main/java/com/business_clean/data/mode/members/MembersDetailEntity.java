package com.business_clean.data.mode.members;

import android.text.TextUtils;

import java.io.Serializable;
import java.util.List;

public class MembersDetailEntity implements Serializable {
    private String uuid;
    private String user_name;
    private String avatar;
    private String salary;
    private String insurance_type;
    private String sex;
    private String sex_name;
    private String age;
    private String role_name;
    private String application_no;
    private String project_name;
    private String project_short_name;
    private String project_cat_name;
    private String is_has_credit_wind;
    private String superiors_user_name;
    private String superiors_user_uuid;

    private String application_type;
    private IdentityEntity identity;
    private WorkInfoEntity work_info;

    private InsureEntity salary_insurance;

    private BankEntity bank;

    private ContactEntity contact;

    private HealthyEntity healthy_pic;

    private ContractEntity contract;

    private List<String> left_pic_list;
    private List<IdCardPicEntity> no_crime_pic_list;

    private List<CreditListEntity> credit_list;

    private String search_credit_time;
    private String left_date;
    private String left_reason_name;
    private String left_reason_remark;

    public void setSex_name(String sex_name) {
        this.sex_name = sex_name;
    }

    public String getSex_name() {
        return sex_name;
    }

    public void setSuperiors_user_name(String superiors_user_name) {
        this.superiors_user_name = superiors_user_name;
    }

    public void setSuperiors_user_uuid(String superiors_user_uuid) {
        this.superiors_user_uuid = superiors_user_uuid;
    }

    public String getSuperiors_user_name() {
        return superiors_user_name;
    }

    public String getSuperiors_user_uuid() {
        return superiors_user_uuid;
    }

    public void setProject_short_name(String project_short_name) {
        this.project_short_name = project_short_name;
    }

    public String getProject_short_name() {
        if (!TextUtils.isEmpty(project_short_name)) {
            return project_short_name;
        }
        return project_name;
    }

    public void setInsurance_type(String insurance_type) {
        this.insurance_type = insurance_type;
    }

    public InsureEntity getSalary_insurance() {
        return salary_insurance;
    }

    public void setSalary_insurance(InsureEntity salary_insurance) {
        this.salary_insurance = salary_insurance;
    }

    public void setApplication_type(String application_type) {
        this.application_type = application_type;
    }

    public String getApplication_type() {
        return application_type;
    }

    public String getIs_has_credit_wind() {
        return is_has_credit_wind;
    }

    public void setIs_has_credit_wind(String is_has_credit_wind) {
        this.is_has_credit_wind = is_has_credit_wind;
    }

    public String getSearch_credit_time() {
        return search_credit_time;
    }

    public void setSearch_credit_time(String search_credit_time) {
        this.search_credit_time = search_credit_time;
    }

    public void setContract(ContractEntity contract) {
        this.contract = contract;
    }

    public ContractEntity getContract() {
        return contract;
    }

    public void setCredit_list(List<CreditListEntity> credit_list) {
        this.credit_list = credit_list;
    }

    public List<CreditListEntity> getCredit_list() {
        return credit_list;
    }

    public void setNo_crime_pic_list(List<IdCardPicEntity> no_crime_pic_list) {
        this.no_crime_pic_list = no_crime_pic_list;
    }

    public List<IdCardPicEntity> getNo_crime_pic_list() {
        return no_crime_pic_list;
    }

    public List<String> getLeft_pic_list() {
        return left_pic_list;
    }

    public void setLeft_pic_list(List<String> left_pic_list) {
        this.left_pic_list = left_pic_list;
    }

    public String getLeft_date() {
        return left_date;
    }

    public void setLeft_date(String left_date) {
        this.left_date = left_date;
    }

    public String getLeft_reason_name() {
        return left_reason_name;
    }

    public void setLeft_reason_name(String left_reason_name) {
        this.left_reason_name = left_reason_name;
    }

    public String getLeft_reason_remark() {
        return left_reason_remark;
    }

    public void setLeft_reason_remark(String left_reason_remark) {
        this.left_reason_remark = left_reason_remark;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getRole_name() {
        return role_name;
    }

    public void setRole_name(String role_name) {
        this.role_name = role_name;
    }

    public String getProject_name() {
        return project_name;
    }

    public void setProject_name(String project_name) {
        this.project_name = project_name;
    }

    public String getProject_cat_name() {
        return project_cat_name;
    }

    public void setProject_cat_name(String project_cat_name) {
        this.project_cat_name = project_cat_name;
    }

    public void setWork_info(WorkInfoEntity work_info) {
        this.work_info = work_info;
    }

    public WorkInfoEntity getWork_info() {
        return work_info;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getSalary() {
        return salary;
    }

    public String getInsurance_type() {
        return insurance_type;
    }

    public void setSalary(String salary) {
        this.salary = salary;
    }

    public String getApplication_no() {
        return application_no;
    }

    public void setApplication_no(String application_no) {
        this.application_no = application_no;
    }

    public IdentityEntity getIdentity() {
        return identity;
    }

    public void setIdentity(IdentityEntity identity) {
        this.identity = identity;
    }

    public BankEntity getBank() {
        return bank;
    }

    public void setBank(BankEntity bank) {
        this.bank = bank;
    }

    public ContactEntity getContact() {
        return contact;
    }

    public void setContact(ContactEntity contact) {
        this.contact = contact;
    }

    public HealthyEntity getHealthy_pic() {
        return healthy_pic;
    }

    public void setHealthy_pic(HealthyEntity healthy_pic) {
        this.healthy_pic = healthy_pic;
    }

    @Override
    public String toString() {
        return "MembersDetailEntity{" +
                "uuid='" + uuid + '\'' +
                ", user_name='" + user_name + '\'' +
                ", avatar='" + avatar + '\'' +
                ", salary='" + salary + '\'' +
                ", sex='" + sex + '\'' +
                ", age='" + age + '\'' +
                ", role_name='" + role_name + '\'' +
                ", application_no='" + application_no + '\'' +
                ", project_name='" + project_name + '\'' +
                ", project_cat_name='" + project_cat_name + '\'' +
                ", identity=" + identity +
                ", work_info=" + work_info +
                ", bank=" + bank +
                ", contact=" + contact +
                ", healthy_pic=" + healthy_pic +
                ", contract=" + contract +
                ", left_pic_list=" + left_pic_list +
                ", no_crime_pic_list=" + no_crime_pic_list +
                ", credit_list=" + credit_list +
                ", left_date='" + left_date + '\'' +
                ", left_reason_name='" + left_reason_name + '\'' +
                ", left_reason_remark='" + left_reason_remark + '\'' +
                '}';
    }
}
