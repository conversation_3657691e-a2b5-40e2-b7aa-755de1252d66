package com.business_clean.data.dao;

import android.content.Context;

import com.business_clean.data.database.greenDao.db.DaoSession;

import org.greenrobot.greendao.query.QueryBuilder;

import java.util.List;

public class WatermarkPhotoDatabaseManager {
    private static final boolean DEBUG = true;

    private static WatermarkPhotoDatabaseManager instance;
    private DaoManager manager;
    private DaoSession daoSession;

    private WatermarkPhotoDatabaseManager(Context context) {
        manager = DaoManager.getInstance();
        manager.init(context);
        daoSession = manager.getDaoSession();
        manager.setDebug(DEBUG);
    }

    public static WatermarkPhotoDatabaseManager getInstance(Context context) {
        if (instance == null) {
            synchronized (WatermarkPhotoDatabaseManager.class) {
                if (instance == null) {
                    instance = new WatermarkPhotoDatabaseManager(context.getApplicationContext());
                }
            }
        }
        return instance;
    }


    /**
     * 添加数据，如果有重复则覆盖
     */
    public void insertOrReplaceWaterPhoto(WaterPhotoData WaterPhoto) {
        manager.getDaoSession().insertOrReplace(WaterPhoto);
    }

    /**
     * 添加数据
     */
    public void insertWaterPhoto(WaterPhotoData WaterPhoto) {
        manager.getDaoSession().insert(WaterPhoto);
    }

    /**
     * 添加多条数据，需要开辟新的线程
     */
    public void insertMultipleWaterPhoto(final List<WaterPhotoData> WaterPhotos) {
        manager.getDaoSession().runInTx(new Runnable() {
            @Override
            public void run() {
                for (WaterPhotoData WaterPhoto : WaterPhotos) {
                    manager.getDaoSession().insertOrReplace(WaterPhoto);
                }
            }
        });
    }


    /**
     * 删除数据
     */
    public void deleteWaterPhoto(WaterPhotoData WaterPhoto) {
        manager.getDaoSession().delete(WaterPhoto);
    }

    /**
     * 删除全部数据
     */
    public void deleteAll(Class cls) {
        manager.getDaoSession().deleteAll(cls);
    }

    /**
     * 更新数据
     */
    public void updateWaterPhoto(WaterPhotoData WaterPhoto) {
        manager.getDaoSession().update(WaterPhoto);
    }

    /**
     * 按照主键返回单条数据
     */
    public WaterPhotoData listOneWaterPhoto(long key) {
        return manager.getDaoSession().load(WaterPhotoData.class, key);
    }

    /**
     * 根据指定条件查询数据
     */
    public List<WaterPhotoData> queryWaterPhoto() {
        //查询构建器
        QueryBuilder<WaterPhotoData> builder = manager.getDaoSession().queryBuilder(WaterPhotoData.class);
        return builder.list();
    }

    /**
     * 查询全部数据
     */
    public List<WaterPhotoData> queryAll() {
        return manager.getDaoSession().loadAll(WaterPhotoData.class);
    }
}
