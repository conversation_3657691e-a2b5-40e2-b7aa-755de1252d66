package com.business_clean.data.mode.camera;

import com.chad.library.adapter.base.entity.JSectionEntity;

public class WaterUploadSection extends JSectionEntity {
    private boolean isHeader;
    private Object object;

    public WaterUploadSection(boolean isHeader, Object object) {
        this.isHeader = isHeader;
        this.object = object;
    }

    public Object getObject() {
        return object;
    }

    @Override
    public boolean isHeader() {
        return isHeader;
    }
}
