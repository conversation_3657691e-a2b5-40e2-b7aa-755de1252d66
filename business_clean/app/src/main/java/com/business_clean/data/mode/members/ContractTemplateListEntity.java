package com.business_clean.data.mode.members;

import java.io.Serializable;

public class ContractTemplateListEntity implements Serializable {
    private String id;
    private String uuid;
    private String main_label_code;
    private String sub_label_code;
    private String company_id;
    private String template_title;
    private String template_url;
    private String is_self;
    private String is_delete;
    private String create_time;
    private String update_time;

    private ContractTemplateListSettingEntity setting;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getMain_label_code() {
        return main_label_code;
    }

    public void setMain_label_code(String main_label_code) {
        this.main_label_code = main_label_code;
    }

    public String getSub_label_code() {
        return sub_label_code;
    }

    public void setSub_label_code(String sub_label_code) {
        this.sub_label_code = sub_label_code;
    }

    public String getCompany_id() {
        return company_id;
    }

    public void setCompany_id(String company_id) {
        this.company_id = company_id;
    }

    public String getTemplate_title() {
        return template_title;
    }

    public void setTemplate_title(String template_title) {
        this.template_title = template_title;
    }

    public String getTemplate_url() {
        return template_url;
    }

    public void setTemplate_url(String template_url) {
        this.template_url = template_url;
    }

    public String getIs_self() {
        return is_self;
    }

    public void setIs_self(String is_self) {
        this.is_self = is_self;
    }

    public String getIs_delete() {
        return is_delete;
    }

    public void setIs_delete(String is_delete) {
        this.is_delete = is_delete;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public ContractTemplateListSettingEntity getSetting() {
        return setting;
    }

    public void setSetting(ContractTemplateListSettingEntity setting) {
        this.setting = setting;
    }
}
