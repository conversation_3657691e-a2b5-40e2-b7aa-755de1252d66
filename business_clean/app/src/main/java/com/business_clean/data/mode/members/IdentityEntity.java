package com.business_clean.data.mode.members;

import java.io.Serializable;
import java.util.List;

public class IdentityEntity implements Serializable {

    private String user_name;
    private String id_number;
    private String birthday;
    private String province_id;
    private String province_name;
    private String city_id;
    private String city_name;
    private String address;
    private String id_card_start_date;
    private String id_card_end_date;
    private String marriage_status;
    private String marriage_status_name;
    private String nation_id;
    private String nation_name;
    private String id_start_date;
    private String id_end_date;
    private String is_id_long;

    private List<IdCardPicEntity> pic_list;

    public String getId_start_date() {
        return id_start_date;
    }

    public void setId_start_date(String id_start_date) {
        this.id_start_date = id_start_date;
    }

    public String getId_end_date() {
        return id_end_date;
    }

    public void setId_end_date(String id_end_date) {
        this.id_end_date = id_end_date;
    }

    public String getIs_id_long() {
        return is_id_long;
    }

    public void setIs_id_long(String is_id_long) {
        this.is_id_long = is_id_long;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getId_number() {
        return id_number;
    }

    public void setId_number(String id_number) {
        this.id_number = id_number;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getProvince_id() {
        return province_id;
    }

    public void setProvince_id(String province_id) {
        this.province_id = province_id;
    }

    public String getProvince_name() {
        return province_name;
    }

    public void setProvince_name(String province_name) {
        this.province_name = province_name;
    }

    public String getCity_id() {
        return city_id;
    }

    public void setCity_id(String city_id) {
        this.city_id = city_id;
    }

    public String getCity_name() {
        return city_name;
    }

    public void setCity_name(String city_name) {
        this.city_name = city_name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getId_card_start_date() {
        return id_card_start_date;
    }

    public void setId_card_start_date(String id_card_start_date) {
        this.id_card_start_date = id_card_start_date;
    }

    public String getId_card_end_date() {
        return id_card_end_date;
    }

    public void setId_card_end_date(String id_card_end_date) {
        this.id_card_end_date = id_card_end_date;
    }

    public String getMarriage_status() {
        return marriage_status;
    }

    public void setMarriage_status(String marriage_status) {
        this.marriage_status = marriage_status;
    }

    public String getMarriage_status_name() {
        return marriage_status_name;
    }

    public void setMarriage_status_name(String marriage_status_name) {
        this.marriage_status_name = marriage_status_name;
    }

    public String getNation_id() {
        return nation_id;
    }

    public void setNation_id(String nation_id) {
        this.nation_id = nation_id;
    }

    public String getNation_name() {
        return nation_name;
    }

    public void setNation_name(String nation_name) {
        this.nation_name = nation_name;
    }

    public List<IdCardPicEntity> getPic_list() {
        return pic_list;
    }

    public void setPic_list(List<IdCardPicEntity> pic_list) {
        this.pic_list = pic_list;
    }
}
