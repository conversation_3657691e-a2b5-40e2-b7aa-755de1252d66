package com.business_clean.data.mode.classes;

import java.io.Serializable;
import java.util.List;

public class ClassesDetailEntity implements Serializable {
    private String uuid;
    private String project_uuid;
    private String class_name;
    private String avg_line_time;
    private String avg_line_is_today;
    private String is_no_clock;

    private String class_type;
    private String is_default_rest;

    private String work_time_long;

    private List<ClassSectionData> segment_list;


    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getProject_uuid() {
        return project_uuid;
    }

    public void setProject_uuid(String project_uuid) {
        this.project_uuid = project_uuid;
    }

    public String getClass_name() {
        return class_name;
    }

    public void setClass_name(String class_name) {
        this.class_name = class_name;
    }

    public String getAvg_line_time() {
        return avg_line_time;
    }

    public void setAvg_line_time(String avg_line_time) {
        this.avg_line_time = avg_line_time;
    }

    public String getAvg_line_is_today() {
        return avg_line_is_today;
    }

    public void setAvg_line_is_today(String avg_line_is_today) {
        this.avg_line_is_today = avg_line_is_today;
    }

    public String getIs_no_clock() {
        return is_no_clock;
    }

    public void setIs_no_clock(String is_no_clock) {
        this.is_no_clock = is_no_clock;
    }

    public String getClass_type() {
        return class_type;
    }

    public void setClass_type(String class_type) {
        this.class_type = class_type;
    }

    public String getIs_default_rest() {
        return is_default_rest;
    }

    public void setIs_default_rest(String is_default_rest) {
        this.is_default_rest = is_default_rest;
    }

    public String getWork_time_long() {
        return work_time_long;
    }

    public void setWork_time_long(String work_time_long) {
        this.work_time_long = work_time_long;
    }

    public List<ClassSectionData> getSegment_list() {
        return segment_list;
    }

    public void setSegment_list(List<ClassSectionData> segment_list) {
        this.segment_list = segment_list;
    }
}
