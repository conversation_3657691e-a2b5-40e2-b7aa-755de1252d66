package com.business_clean.data.mode.custom;

import com.business_clean.data.mode.project.ProjectMangerList;

import java.util.List;

public class CustomMangerDetailEntity {

    private String page;
    private String size;
    private String total;
    private List<ProjectMangerList> list;

    public void setList(List<ProjectMangerList> list) {
        this.list = list;
    }

    public List<ProjectMangerList> getList() {
        return list;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }
}
