package com.business_clean.data.mode.camera;

import java.io.Serializable;
import java.util.List;

public class TeamClassUserEntity implements Serializable {
    private int page;
    private int size;
    private int total;

    private List<TeamClassUserListEntity> list;


    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<TeamClassUserListEntity> getList() {
        return list;
    }

    public void setList(List<TeamClassUserListEntity> list) {
        this.list = list;
    }
}
