package com.business_clean.data.mode.group;

import com.business_clean.data.mode.address.AddressListEntity;

import java.io.Serializable;
import java.util.List;

public class GroupMangerListEntity implements Serializable {

    private String uuid;
    private String group_name;
    private String work_day_desc;
    private String in_class_desc;
    private String attendance_method_name;
    private List<String> address_list;

    public void setAttendance_method_name(String attendance_method_name) {
        this.attendance_method_name = attendance_method_name;
    }

    public String getAttendance_method_name() {
        return attendance_method_name;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getGroup_name() {
        return group_name;
    }

    public void setGroup_name(String group_name) {
        this.group_name = group_name;
    }

    public String getWork_day_desc() {
        return work_day_desc;
    }

    public void setWork_day_desc(String work_day_desc) {
        this.work_day_desc = work_day_desc;
    }

    public String getIn_class_desc() {
        return in_class_desc;
    }

    public void setIn_class_desc(String in_class_desc) {
        this.in_class_desc = in_class_desc;
    }

    public List<String> getAddress_list() {
        return address_list;
    }

    public void setAddress_list(List<String> address_list) {
        this.address_list = address_list;
    }
}
