package com.business_clean.data.mode.login;

import java.io.Serializable;

public class UserEntity implements Serializable {
    private String uuid;
    private String user_name;
    private String mobile;
    private String avatar;
    private String is_admin;
    private String is_head_office;
    private String role_id;
    private String role_name;
    private String job_uuid;
    private String job_name;

    private String is_save_to_phone;
    private String is_save_watermark;
    private String is_save_origin_pic;

    private String is_mark;

    private String is_super_admin;


    public void setIs_super_admin(String is_super_admin) {
        this.is_super_admin = is_super_admin;
    }

    public String getIs_super_admin() {
        return is_super_admin;
    }

    public void setIs_mark(String is_mark) {
        this.is_mark = is_mark;
    }

    public String getIs_mark() {
        return is_mark;
    }

    public String getIs_save_to_phone() {
        return is_save_to_phone;
    }

    public void setIs_save_to_phone(String is_save_to_phone) {
        this.is_save_to_phone = is_save_to_phone;
    }

    public String getIs_save_watermark() {
        return is_save_watermark;
    }

    public void setIs_save_watermark(String is_save_watermark) {
        this.is_save_watermark = is_save_watermark;
    }

    public String getIs_save_origin_pic() {
        return is_save_origin_pic;
    }

    public void setIs_save_origin_pic(String is_save_origin_pic) {
        this.is_save_origin_pic = is_save_origin_pic;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getIs_admin() {
        return is_admin;
    }

    public void setIs_admin(String is_admin) {
        this.is_admin = is_admin;
    }

    public String getIs_head_office() {
        return is_head_office;
    }

    public void setIs_head_office(String is_head_office) {
        this.is_head_office = is_head_office;
    }

    public String getRole_id() {
        return role_id;
    }

    public void setRole_id(String role_id) {
        this.role_id = role_id;
    }

    public String getRole_name() {
        return role_name;
    }

    public void setRole_name(String role_name) {
        this.role_name = role_name;
    }

    public String getJob_uuid() {
        return job_uuid;
    }

    public void setJob_uuid(String job_uuid) {
        this.job_uuid = job_uuid;
    }

    public String getJob_name() {
        return job_name;
    }

    public void setJob_name(String job_name) {
        this.job_name = job_name;
    }
}
