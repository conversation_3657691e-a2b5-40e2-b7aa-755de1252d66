package com.business_clean.data.mode.attendance;

import java.io.Serializable;

public class MyAttendanceDetailListItemEntity implements Serializable {
    private MyAttendanceDetailClockEntity clock_info;

    private String title;
    private String status;
    private String status_name;

    private String operate_name;


    public void setOperate_name(String operate_name) {
        this.operate_name = operate_name;
    }

    public String getOperate_name() {
        return operate_name;
    }

    public MyAttendanceDetailClockEntity getClock_info() {
        return clock_info;
    }

    public void setClock_info(MyAttendanceDetailClockEntity clock_info) {
        this.clock_info = clock_info;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus_name() {
        return status_name;
    }

    public void setStatus_name(String status_name) {
        this.status_name = status_name;
    }
}
