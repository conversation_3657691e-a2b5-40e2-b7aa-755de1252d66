package com.business_clean.data.mode.circle;

import android.text.TextUtils;

import com.chad.library.adapter.base.entity.MultiItemEntity;

import java.io.Serializable;
import java.util.List;


public class ChatMessageCompact implements Serializable {
    private String user_name;
    private String user_uuid;
    private String job_name;
    private String avatar;
    private String create_time;
    private List<ChatMessage> list;


    public void setUser_uuid(String user_uuid) {
        this.user_uuid = user_uuid;
    }

    public String getUser_uuid() {
        return user_uuid;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getJob_name() {
        return job_name;
    }

    public void setJob_name(String job_name) {
        this.job_name = job_name;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public List<ChatMessage> getList() {
        return list;
    }

    public void setList(List<ChatMessage> list) {
        this.list = list;
    }
}
