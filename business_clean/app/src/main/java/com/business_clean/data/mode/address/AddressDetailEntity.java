package com.business_clean.data.mode.address;

import java.io.Serializable;
import java.util.List;

public class AddressDetailEntity implements Serializable {
    private String uuid;
    private String project_uuid;
    private String group_name;
    private String project_class_uuid;
    private String project_class_name;
    private String in_class_desc;
    private String attendance_method;
    private String attendance_method_name;
    private List<String> work_day_list;
    private List<AddressListEntity> address_list;

    private List<LeaderListEntity> leader_list;


    public void setAttendance_method_name(String attendance_method_name) {
        this.attendance_method_name = attendance_method_name;
    }

    public void setAttendance_method(String attendance_method) {
        this.attendance_method = attendance_method;
    }

    public String getAttendance_method_name() {
        return attendance_method_name;
    }

    public String getAttendance_method() {
        return attendance_method;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getProject_uuid() {
        return project_uuid;
    }

    public void setProject_uuid(String project_uuid) {
        this.project_uuid = project_uuid;
    }

    public String getGroup_name() {
        return group_name;
    }

    public void setGroup_name(String group_name) {
        this.group_name = group_name;
    }

    public String getProject_class_uuid() {
        return project_class_uuid;
    }

    public void setProject_class_uuid(String project_class_uuid) {
        this.project_class_uuid = project_class_uuid;
    }

    public String getProject_class_name() {
        return project_class_name;
    }

    public void setProject_class_name(String project_class_name) {
        this.project_class_name = project_class_name;
    }

    public String getIn_class_desc() {
        return in_class_desc;
    }

    public void setIn_class_desc(String in_class_desc) {
        this.in_class_desc = in_class_desc;
    }

    public List<String> getWork_day_list() {
        return work_day_list;
    }

    public void setWork_day_list(List<String> work_day_list) {
        this.work_day_list = work_day_list;
    }

    public List<AddressListEntity> getAddress_list() {
        return address_list;
    }

    public void setAddress_list(List<AddressListEntity> address_list) {
        this.address_list = address_list;
    }

    public List<LeaderListEntity> getLeader_list() {
        return leader_list;
    }

    public void setLeader_list(List<LeaderListEntity> leader_list) {
        this.leader_list = leader_list;
    }
}
