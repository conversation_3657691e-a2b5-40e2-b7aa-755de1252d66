package com.business_clean.data.mode.login;

import com.business_clean.data.mode.project.ProjectMangerList;

import java.io.Serializable;
import java.util.List;

public class UserInfo implements Serializable {

    private CompanyEntity company;

    private List<CompanyEntity> company_list;

    private ProjectMangerList project;

    private UserEntity user;

    private String token;

    private String expire_time;

    private String login_user_uuid;

    public void setLogin_user_uuid(String login_user_uuid) {
        this.login_user_uuid = login_user_uuid;
    }

    public String getLogin_user_uuid() {
        return login_user_uuid;
    }

    public CompanyEntity getCompany() {
        return company;
    }


    public void setCompany_list(List<CompanyEntity> company_list) {
        this.company_list = company_list;
    }

    public List<CompanyEntity> getCompany_list() {
        return company_list;
    }

    public void setCompany(CompanyEntity company) {
        this.company = company;
    }

    public ProjectMangerList getProject() {
        return project;
    }

    public void setProject(ProjectMangerList project) {
        this.project = project;
    }

    public UserEntity getUser() {
        return user;
    }

    public void setUser(UserEntity user) {
        this.user = user;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getExpire_time() {
        return expire_time;
    }

    public void setExpire_time(String expire_time) {
        this.expire_time = expire_time;
    }

    @Override
    public String toString() {
        return "UserInfo{" +
                "company=" + company +
                ", company_list=" + company_list +
                ", project=" + project +
                ", user=" + user +
                ", token='" + token + '\'' +
                ", expire_time='" + expire_time + '\'' +
                ", login_user_uuid='" + login_user_uuid + '\'' +
                '}';
    }
}
