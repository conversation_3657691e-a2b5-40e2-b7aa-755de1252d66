package com.business_clean.data.mode.classes;

import java.io.Serializable;

public class ClassSectionData implements Serializable {
    private String uuid;
    private String work_start_time;
    private String work_end_time;

    private String start_time_is_today;
    private String end_time_is_today;
    private String rest_start_time;
    private String rest_end_time;
    private String rest_start_time_is_today;
    private String rest_end_time_is_today;
    private String in_class_start_type;
    private String in_class_end_type;
    private String out_class_start_type;
    private String out_class_end_type;


    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public String getWork_start_time() {
        return work_start_time;
    }

    public void setWork_start_time(String work_start_time) {
        this.work_start_time = work_start_time;
    }

    public String getWork_end_time() {
        return work_end_time;
    }

    public void setWork_end_time(String work_end_time) {
        this.work_end_time = work_end_time;
    }

    public String getStart_time_is_today() {
        return start_time_is_today;
    }

    public void setStart_time_is_today(String start_time_is_today) {
        this.start_time_is_today = start_time_is_today;
    }

    public String getEnd_time_is_today() {
        return end_time_is_today;
    }

    public void setEnd_time_is_today(String end_time_is_today) {
        this.end_time_is_today = end_time_is_today;
    }

    public String getRest_start_time() {
        return rest_start_time;
    }

    public void setRest_start_time(String rest_start_time) {
        this.rest_start_time = rest_start_time;
    }

    public String getRest_end_time() {
        return rest_end_time;
    }

    public void setRest_end_time(String rest_end_time) {
        this.rest_end_time = rest_end_time;
    }

    public String getRest_start_time_is_today() {
        return rest_start_time_is_today;
    }

    public void setRest_start_time_is_today(String rest_start_time_is_today) {
        this.rest_start_time_is_today = rest_start_time_is_today;
    }

    public String getRest_end_time_is_today() {
        return rest_end_time_is_today;
    }

    public void setRest_end_time_is_today(String rest_end_time_is_today) {
        this.rest_end_time_is_today = rest_end_time_is_today;
    }

    public String getIn_class_start_type() {
        return in_class_start_type;
    }

    public void setIn_class_start_type(String in_class_start_type) {
        this.in_class_start_type = in_class_start_type;
    }

    public String getIn_class_end_type() {
        return in_class_end_type;
    }

    public void setIn_class_end_type(String in_class_end_type) {
        this.in_class_end_type = in_class_end_type;
    }

    public String getOut_class_start_type() {
        return out_class_start_type;
    }

    public void setOut_class_start_type(String out_class_start_type) {
        this.out_class_start_type = out_class_start_type;
    }

    public String getOut_class_end_type() {
        return out_class_end_type;
    }

    public void setOut_class_end_type(String out_class_end_type) {
        this.out_class_end_type = out_class_end_type;
    }


    @Override
    public String toString() {
        return "ClassSectionData{" +
                "uuid='" + uuid + '\'' +
                ", work_start_time='" + work_start_time + '\'' +
                ", work_end_time='" + work_end_time + '\'' +
                ", start_time_is_today='" + start_time_is_today + '\'' +
                ", end_time_is_today='" + end_time_is_today + '\'' +
                ", rest_start_time='" + rest_start_time + '\'' +
                ", rest_end_time='" + rest_end_time + '\'' +
                ", rest_start_time_is_today='" + rest_start_time_is_today + '\'' +
                ", rest_end_time_is_today='" + rest_end_time_is_today + '\'' +
                ", in_class_start_type='" + in_class_start_type + '\'' +
                ", in_class_end_type='" + in_class_end_type + '\'' +
                ", out_class_start_type='" + out_class_start_type + '\'' +
                ", out_class_end_type='" + out_class_end_type + '\'' +
                '}';
    }
}
