package com.business_clean.data.mode.attendance;

import java.io.Serializable;
import java.util.List;

public class MyAttendanceDetailEntity implements Serializable {

    private String class_name;
    private String class_uuid;
    private String class_type;//班次类型 1固定班次 2自由班次
    private String schedule_uuid;
    private String day_type;
    private String project_name;
    private String project_uuid;

    public void setProject_name(String project_name) {
        this.project_name = project_name;
    }

    public void setProject_uuid(String project_uuid) {
        this.project_uuid = project_uuid;
    }

    public String getProject_name() {
        return project_name;
    }

    public String getProject_uuid() {
        return project_uuid;
    }

    public void setClass_type(String class_type) {
        this.class_type = class_type;
    }

    public String getClass_type() {
        return class_type;
    }

    private List<MyAttendanceDetailClockEntity> list;

    private List<MyAttendanceDetailListItemHolidayEntity> holiday_list;
    private List<MyAttendanceDetailListItemOverTimeEntity> overtime_list;
    private List<MyAttendanceDetailListItemOperateLogEntity> operate_log_list;

    public String getClass_name() {
        return class_name;
    }


    public void setSchedule_uuid(String schedule_uuid) {
        this.schedule_uuid = schedule_uuid;
    }

    public String getSchedule_uuid() {
        return schedule_uuid;
    }

    public void setDay_type(String day_type) {
        this.day_type = day_type;
    }

    public String getDay_type() {
        return day_type;
    }

    public void setClass_uuid(String class_uuid) {
        this.class_uuid = class_uuid;
    }

    public String getClass_uuid() {
        return class_uuid;
    }

    public void setClass_name(String class_name) {
        this.class_name = class_name;
    }

    public List<MyAttendanceDetailClockEntity> getList() {
        return list;
    }

    public void setList(List<MyAttendanceDetailClockEntity> list) {
        this.list = list;
    }


    public List<MyAttendanceDetailListItemHolidayEntity> getHoliday_list() {
        return holiday_list;
    }

    public void setHoliday_list(List<MyAttendanceDetailListItemHolidayEntity> holiday_list) {
        this.holiday_list = holiday_list;
    }

    public List<MyAttendanceDetailListItemOverTimeEntity> getOvertime_list() {
        return overtime_list;
    }

    public void setOvertime_list(List<MyAttendanceDetailListItemOverTimeEntity> overtime_list) {
        this.overtime_list = overtime_list;
    }


    public void setOperate_log_list(List<MyAttendanceDetailListItemOperateLogEntity> operate_log_list) {
        this.operate_log_list = operate_log_list;
    }

    public List<MyAttendanceDetailListItemOperateLogEntity> getOperate_log_list() {
        return operate_log_list;
    }
}
