package com.business_clean.data.database.greenDao.db;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.business_clean.data.dao.WaterPhotoData;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "WATER_PHOTO_DATA".
*/
public class WaterPhotoDataDao extends AbstractDao<WaterPhotoData, Long> {

    public static final String TABLENAME = "WATER_PHOTO_DATA";

    /**
     * Properties of entity WaterPhotoData.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property Act_type = new Property(1, int.class, "act_type", false, "ACT_TYPE");
        public final static Property Message_type = new Property(2, int.class, "message_type", false, "MESSAGE_TYPE");
        public final static Property Task_uuid = new Property(3, String.class, "task_uuid", false, "TASK_UUID");
        public final static Property Project_uuid = new Property(4, String.class, "project_uuid", false, "PROJECT_UUID");
        public final static Property Video_cover = new Property(5, String.class, "video_cover", false, "VIDEO_COVER");
        public final static Property Video_path = new Property(6, String.class, "video_path", false, "VIDEO_PATH");
        public final static Property Photo_water_path = new Property(7, String.class, "photo_water_path", false, "PHOTO_WATER_PATH");
        public final static Property Photo_origin_path = new Property(8, String.class, "photo_origin_path", false, "PHOTO_ORIGIN_PATH");
        public final static Property Photo_thumb_path = new Property(9, String.class, "photo_thumb_path", false, "PHOTO_THUMB_PATH");
        public final static Property Lat = new Property(10, double.class, "lat", false, "LAT");
        public final static Property Lnt = new Property(11, double.class, "lnt", false, "LNT");
        public final static Property Address = new Property(12, String.class, "address", false, "ADDRESS");
        public final static Property Now_current_time_millis = new Property(13, long.class, "now_current_time_millis", false, "NOW_CURRENT_TIME_MILLIS");
        public final static Property Security_code = new Property(14, String.class, "security_code", false, "SECURITY_CODE");
        public final static Property User_uuid_list = new Property(15, String.class, "user_uuid_list", false, "USER_UUID_LIST");
        public final static Property Upload_status = new Property(16, int.class, "upload_status", false, "UPLOAD_STATUS");
        public final static Property RequestMsg = new Property(17, String.class, "requestMsg", false, "REQUEST_MSG");
    }


    public WaterPhotoDataDao(DaoConfig config) {
        super(config);
    }
    
    public WaterPhotoDataDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"WATER_PHOTO_DATA\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"ACT_TYPE\" INTEGER NOT NULL ," + // 1: act_type
                "\"MESSAGE_TYPE\" INTEGER NOT NULL ," + // 2: message_type
                "\"TASK_UUID\" TEXT," + // 3: task_uuid
                "\"PROJECT_UUID\" TEXT," + // 4: project_uuid
                "\"VIDEO_COVER\" TEXT," + // 5: video_cover
                "\"VIDEO_PATH\" TEXT," + // 6: video_path
                "\"PHOTO_WATER_PATH\" TEXT," + // 7: photo_water_path
                "\"PHOTO_ORIGIN_PATH\" TEXT," + // 8: photo_origin_path
                "\"PHOTO_THUMB_PATH\" TEXT," + // 9: photo_thumb_path
                "\"LAT\" REAL NOT NULL ," + // 10: lat
                "\"LNT\" REAL NOT NULL ," + // 11: lnt
                "\"ADDRESS\" TEXT," + // 12: address
                "\"NOW_CURRENT_TIME_MILLIS\" INTEGER NOT NULL ," + // 13: now_current_time_millis
                "\"SECURITY_CODE\" TEXT," + // 14: security_code
                "\"USER_UUID_LIST\" TEXT," + // 15: user_uuid_list
                "\"UPLOAD_STATUS\" INTEGER NOT NULL ," + // 16: upload_status
                "\"REQUEST_MSG\" TEXT);"); // 17: requestMsg
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"WATER_PHOTO_DATA\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, WaterPhotoData entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
        stmt.bindLong(2, entity.getAct_type());
        stmt.bindLong(3, entity.getMessage_type());
 
        String task_uuid = entity.getTask_uuid();
        if (task_uuid != null) {
            stmt.bindString(4, task_uuid);
        }
 
        String project_uuid = entity.getProject_uuid();
        if (project_uuid != null) {
            stmt.bindString(5, project_uuid);
        }
 
        String video_cover = entity.getVideo_cover();
        if (video_cover != null) {
            stmt.bindString(6, video_cover);
        }
 
        String video_path = entity.getVideo_path();
        if (video_path != null) {
            stmt.bindString(7, video_path);
        }
 
        String photo_water_path = entity.getPhoto_water_path();
        if (photo_water_path != null) {
            stmt.bindString(8, photo_water_path);
        }
 
        String photo_origin_path = entity.getPhoto_origin_path();
        if (photo_origin_path != null) {
            stmt.bindString(9, photo_origin_path);
        }
 
        String photo_thumb_path = entity.getPhoto_thumb_path();
        if (photo_thumb_path != null) {
            stmt.bindString(10, photo_thumb_path);
        }
        stmt.bindDouble(11, entity.getLat());
        stmt.bindDouble(12, entity.getLnt());
 
        String address = entity.getAddress();
        if (address != null) {
            stmt.bindString(13, address);
        }
        stmt.bindLong(14, entity.getNow_current_time_millis());
 
        String security_code = entity.getSecurity_code();
        if (security_code != null) {
            stmt.bindString(15, security_code);
        }
 
        String user_uuid_list = entity.getUser_uuid_list();
        if (user_uuid_list != null) {
            stmt.bindString(16, user_uuid_list);
        }
        stmt.bindLong(17, entity.getUpload_status());
 
        String requestMsg = entity.getRequestMsg();
        if (requestMsg != null) {
            stmt.bindString(18, requestMsg);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, WaterPhotoData entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
        stmt.bindLong(2, entity.getAct_type());
        stmt.bindLong(3, entity.getMessage_type());
 
        String task_uuid = entity.getTask_uuid();
        if (task_uuid != null) {
            stmt.bindString(4, task_uuid);
        }
 
        String project_uuid = entity.getProject_uuid();
        if (project_uuid != null) {
            stmt.bindString(5, project_uuid);
        }
 
        String video_cover = entity.getVideo_cover();
        if (video_cover != null) {
            stmt.bindString(6, video_cover);
        }
 
        String video_path = entity.getVideo_path();
        if (video_path != null) {
            stmt.bindString(7, video_path);
        }
 
        String photo_water_path = entity.getPhoto_water_path();
        if (photo_water_path != null) {
            stmt.bindString(8, photo_water_path);
        }
 
        String photo_origin_path = entity.getPhoto_origin_path();
        if (photo_origin_path != null) {
            stmt.bindString(9, photo_origin_path);
        }
 
        String photo_thumb_path = entity.getPhoto_thumb_path();
        if (photo_thumb_path != null) {
            stmt.bindString(10, photo_thumb_path);
        }
        stmt.bindDouble(11, entity.getLat());
        stmt.bindDouble(12, entity.getLnt());
 
        String address = entity.getAddress();
        if (address != null) {
            stmt.bindString(13, address);
        }
        stmt.bindLong(14, entity.getNow_current_time_millis());
 
        String security_code = entity.getSecurity_code();
        if (security_code != null) {
            stmt.bindString(15, security_code);
        }
 
        String user_uuid_list = entity.getUser_uuid_list();
        if (user_uuid_list != null) {
            stmt.bindString(16, user_uuid_list);
        }
        stmt.bindLong(17, entity.getUpload_status());
 
        String requestMsg = entity.getRequestMsg();
        if (requestMsg != null) {
            stmt.bindString(18, requestMsg);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public WaterPhotoData readEntity(Cursor cursor, int offset) {
        WaterPhotoData entity = new WaterPhotoData( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.getInt(offset + 1), // act_type
            cursor.getInt(offset + 2), // message_type
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // task_uuid
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // project_uuid
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // video_cover
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // video_path
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // photo_water_path
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // photo_origin_path
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // photo_thumb_path
            cursor.getDouble(offset + 10), // lat
            cursor.getDouble(offset + 11), // lnt
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // address
            cursor.getLong(offset + 13), // now_current_time_millis
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // security_code
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // user_uuid_list
            cursor.getInt(offset + 16), // upload_status
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17) // requestMsg
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, WaterPhotoData entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setAct_type(cursor.getInt(offset + 1));
        entity.setMessage_type(cursor.getInt(offset + 2));
        entity.setTask_uuid(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setProject_uuid(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setVideo_cover(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setVideo_path(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setPhoto_water_path(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setPhoto_origin_path(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setPhoto_thumb_path(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setLat(cursor.getDouble(offset + 10));
        entity.setLnt(cursor.getDouble(offset + 11));
        entity.setAddress(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setNow_current_time_millis(cursor.getLong(offset + 13));
        entity.setSecurity_code(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setUser_uuid_list(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setUpload_status(cursor.getInt(offset + 16));
        entity.setRequestMsg(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(WaterPhotoData entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(WaterPhotoData entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(WaterPhotoData entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
