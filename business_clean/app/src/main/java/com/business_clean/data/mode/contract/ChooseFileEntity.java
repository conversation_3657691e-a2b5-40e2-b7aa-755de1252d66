package com.business_clean.data.mode.contract;

import java.io.Serializable;

public class ChooseFileEntity implements Serializable {
    private String fileName;
    private String filePath;

    public ChooseFileEntity(String fileName, String filePath) {
        this.fileName = fileName;
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
