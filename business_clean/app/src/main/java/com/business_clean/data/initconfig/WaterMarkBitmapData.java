package com.business_clean.data.initconfig;

import android.graphics.Bitmap;

import java.io.Serializable;

public class WaterMarkBitmapData implements Serializable {
    private Bitmap originalBitmap;//原图
    private Bitmap waterBitmap;//水印图
    private Bitmap keyBitmap;//防伪码的图

    private double lat;
    private double lnt;

    private String Address;

    public double getLat() {
        return lat;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public double getLnt() {
        return lnt;
    }

    public void setLnt(double lnt) {
        this.lnt = lnt;
    }

    public String getAddress() {
        return Address;
    }

    public void setAddress(String address) {
        Address = address;
    }

    public Bitmap getOriginalBitmap() {
        return originalBitmap;
    }

    public void setOriginalBitmap(Bitmap originalBitmap) {
        this.originalBitmap = originalBitmap;
    }

    public Bitmap getWaterBitmap() {
        return waterBitmap;
    }

    public void setWaterBitmap(Bitmap waterBitmap) {
        this.waterBitmap = waterBitmap;
    }

    public Bitmap getKeyBitmap() {
        return keyBitmap;
    }

    public void setKeyBitmap(Bitmap keyBitmap) {
        this.keyBitmap = keyBitmap;
    }
}
