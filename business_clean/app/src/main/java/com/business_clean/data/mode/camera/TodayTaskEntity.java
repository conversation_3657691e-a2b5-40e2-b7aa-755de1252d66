package com.business_clean.data.mode.camera;

import java.io.Serializable;
import java.util.List;

public class TodayTaskEntity implements Serializable {

    private List<TodayTaskEntityList> todo_task_list;
    private List<TodayTaskEntityList> finished_task_list;

    public List<TodayTaskEntityList> getTodo_task_list() {
        return todo_task_list;
    }

    public void setTodo_task_list(List<TodayTaskEntityList> todo_task_list) {
        this.todo_task_list = todo_task_list;
    }

    public List<TodayTaskEntityList> getFinished_task_list() {
        return finished_task_list;
    }

    public void setFinished_task_list(List<TodayTaskEntityList> finished_task_list) {
        this.finished_task_list = finished_task_list;
    }
}
