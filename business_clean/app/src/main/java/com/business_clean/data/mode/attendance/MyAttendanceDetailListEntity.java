package com.business_clean.data.mode.attendance;

import java.io.Serializable;
import java.util.List;

public class MyAttendanceDetailListEntity implements Serializable {

    private MyAttendanceDetailListItemEntity in_class_info;
    private MyAttendanceDetailListItemEntity out_class_info;

    public MyAttendanceDetailListItemEntity getIn_class_info() {
        return in_class_info;
    }

    public void setIn_class_info(MyAttendanceDetailListItemEntity in_class_info) {
        this.in_class_info = in_class_info;
    }

    public MyAttendanceDetailListItemEntity getOut_class_info() {
        return out_class_info;
    }

    public void setOut_class_info(MyAttendanceDetailListItemEntity out_class_info) {
        this.out_class_info = out_class_info;
    }
}
