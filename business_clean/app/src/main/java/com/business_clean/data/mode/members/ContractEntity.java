package com.business_clean.data.mode.members;

import java.io.Serializable;
import java.util.List;

public class ContractEntity implements Serializable {

    private String start_time;
    private String end_time;
    private String contract_type;
    private String contract_type_name;
    private String contract_sign_type;
    private String contract_sign_type_name;
    private List<IdCardPicEntity>  pic_list;


    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public String getContract_type() {
        return contract_type;
    }

    public void setContract_type(String contract_type) {
        this.contract_type = contract_type;
    }

    public String getContract_type_name() {
        return contract_type_name;
    }

    public void setContract_type_name(String contract_type_name) {
        this.contract_type_name = contract_type_name;
    }

    public String getContract_sign_type() {
        return contract_sign_type;
    }

    public void setContract_sign_type(String contract_sign_type) {
        this.contract_sign_type = contract_sign_type;
    }

    public String getContract_sign_type_name() {
        return contract_sign_type_name;
    }

    public void setContract_sign_type_name(String contract_sign_type_name) {
        this.contract_sign_type_name = contract_sign_type_name;
    }

    public List<IdCardPicEntity> getPic_list() {
        return pic_list;
    }

    public void setPic_list(List<IdCardPicEntity> pic_list) {
        this.pic_list = pic_list;
    }
}
