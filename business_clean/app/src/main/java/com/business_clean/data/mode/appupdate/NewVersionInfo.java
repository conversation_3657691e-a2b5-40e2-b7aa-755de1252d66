package com.business_clean.data.mode.appupdate;

import java.util.List;

public class NewVersionInfo {
    private String id;
    private String title;
    private String desc;
    private String version;
    private String url;
    private String create_time;
    private String upgrade_demand;
    private String osUrl;

    private List<String> brand_id_list;
    private CurrSoConf curr_so_conf;


    public void setOsUrl(String osUrl) {
        this.osUrl = osUrl;
    }

    public List<String> getBrand_id_list() {
        return brand_id_list;
    }

    public void setBrand_id_list(List<String> brand_id_list) {
        this.brand_id_list = brand_id_list;
    }

    public String getUpgrade_demand() {
        return upgrade_demand;
    }

    public void setUpgrade_demand(String upgrade_demand) {
        this.upgrade_demand = upgrade_demand;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getVersion() {
        return version;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrl() {
        return url;
    }

    public void setCreateTime(String createTime) {
        this.create_time = createTime;
    }

    public String getCreateTime() {
        return create_time;
    }

    public String getOsUrl() {
        if (curr_so_conf != null) {
            return curr_so_conf.so_url;
        }
        return osUrl;
    }

    public void setCurr_so_conf(CurrSoConf curr_so_conf) {
        this.curr_so_conf = curr_so_conf;
        if (curr_so_conf != null) {
            osUrl = curr_so_conf.so_url;
        }
    }

    public CurrSoConf getCurr_so_conf() {
        return curr_so_conf;
    }

    public class CurrSoConf {
        private String so_url;
        private String curr_version;

        public String getSo_url() {
            return so_url;
        }

        public void setSo_url(String so_url) {
            this.so_url = so_url;
        }

        public String getCurr_version() {
            return curr_version;
        }

        public void setCurr_version(String curr_version) {
            this.curr_version = curr_version;
        }
    }
}
