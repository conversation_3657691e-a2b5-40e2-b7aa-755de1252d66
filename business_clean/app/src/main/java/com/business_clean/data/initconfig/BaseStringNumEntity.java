package com.business_clean.data.initconfig;

import java.io.Serializable;

public class BaseStringNumEntity implements Serializable {
    private String name;
    private String num;
    private String bgColor;
    private String textColor;

    private boolean isHide;

    public void setHide(boolean hide) {
        isHide = hide;
    }

    public boolean isHide() {
        return isHide;
    }

    public BaseStringNumEntity(String name, String num, String bgColor, String textColor) {
        this.name = name;
        this.num = num;
        this.bgColor = bgColor;
        this.textColor = textColor;
    }


    public BaseStringNumEntity(String name, String num, String bgColor, String textColor, boolean hideNum) {
        this.name = name;
        this.num = num;
        this.bgColor = bgColor;
        this.textColor = textColor;
        this.isHide = hideNum;
    }

    public String getBgColor() {
        return bgColor;
    }

    public void setBgColor(String bgColor) {
        this.bgColor = bgColor;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }
}
