package com.business_clean.data.mode.members;

import android.text.TextUtils;

import java.io.Serializable;
import java.util.List;

public class WorkInfoEntity implements Serializable {

    private String work_start_time;
    private String work_end_time;
    private String role_id;
    private String contract_type;
    private String contract_type_name;
    private String project_uuid;
    private String project_name;
    private String project_short_name;
    private String role_name;
    private String group_uuid;
    private String group_name;
    private String job_uuid;
    private String job_name;
    private String department_uuid;
    private String department_name;
    private String contract_company_uuid;
    private String contract_company_name;
    private String is_head_office;

    private List<IdCardPicEntity> pic_list;


    public void setProject_short_name(String project_short_name) {
        this.project_short_name = project_short_name;
    }

    public String getDepartment_uuid() {
        return department_uuid;
    }

    public void setDepartment_uuid(String department_uuid) {
        this.department_uuid = department_uuid;
    }

    public String getDepartment_name() {
        return department_name;
    }

    public void setDepartment_name(String department_name) {
        this.department_name = department_name;
    }

    public String getContract_company_uuid() {
        return contract_company_uuid;
    }

    public void setContract_company_uuid(String contract_company_uuid) {
        this.contract_company_uuid = contract_company_uuid;
    }

    public String getContract_company_name() {
        return contract_company_name;
    }

    public void setContract_company_name(String contract_company_name) {
        this.contract_company_name = contract_company_name;
    }

    public String getIs_head_office() {
        return is_head_office;
    }

    public void setIs_head_office(String is_head_office) {
        this.is_head_office = is_head_office;
    }

    public String getWork_start_time() {
        return work_start_time;
    }

    public void setWork_start_time(String work_start_time) {
        this.work_start_time = work_start_time;
    }

    public String getWork_end_time() {
        return work_end_time;
    }

    public void setWork_end_time(String work_end_time) {
        this.work_end_time = work_end_time;
    }

    public String getRole_id() {
        return role_id;
    }

    public void setRole_id(String role_id) {
        this.role_id = role_id;
    }

    public String getContract_type() {
        return contract_type;
    }

    public void setContract_type(String contract_type) {
        this.contract_type = contract_type;
    }

    public String getContract_type_name() {
        return contract_type_name;
    }

    public void setContract_type_name(String contract_type_name) {
        this.contract_type_name = contract_type_name;
    }

    public String getProject_uuid() {
        return project_uuid;
    }

    public void setProject_uuid(String project_uuid) {
        this.project_uuid = project_uuid;
    }

    public String getProject_name() {
        return project_name;
    }

    public String getProject_short_name() {
        if (!TextUtils.isEmpty(project_short_name)) {
            return project_short_name;
        }
        return project_name;
    }

    public void setProject_name(String project_name) {
        this.project_name = project_name;
    }

    public String getRole_name() {
        return role_name;
    }

    public void setRole_name(String role_name) {
        this.role_name = role_name;
    }

    public String getGroup_uuid() {
        return group_uuid;
    }

    public void setGroup_uuid(String group_uuid) {
        this.group_uuid = group_uuid;
    }

    public String getGroup_name() {
        return group_name;
    }

    public void setGroup_name(String group_name) {
        this.group_name = group_name;
    }

    public String getJob_uuid() {
        return job_uuid;
    }

    public void setJob_uuid(String job_uuid) {
        this.job_uuid = job_uuid;
    }

    public String getJob_name() {
        return job_name;
    }

    public void setJob_name(String job_name) {
        this.job_name = job_name;
    }

    public List<IdCardPicEntity> getPic_list() {
        return pic_list;
    }

    public void setPic_list(List<IdCardPicEntity> pic_list) {
        this.pic_list = pic_list;
    }
}
