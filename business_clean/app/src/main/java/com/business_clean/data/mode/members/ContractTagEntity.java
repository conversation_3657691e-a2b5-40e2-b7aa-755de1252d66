package com.business_clean.data.mode.members;

import java.io.Serializable;
import java.util.List;

public class ContractTagEntity implements Serializable {
    private String id;
    private String pid;
    private String code;
    private String label_name;
    private String is_delete;
    private String create_time;
    private String update_time;

    private List<ContractTagEntity> sub_label_list;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLabel_name() {
        return label_name;
    }

    public void setLabel_name(String label_name) {
        this.label_name = label_name;
    }

    public String getIs_delete() {
        return is_delete;
    }

    public void setIs_delete(String is_delete) {
        this.is_delete = is_delete;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public List<ContractTagEntity> getSub_label_list() {
        return sub_label_list;
    }

    public void setSub_label_list(List<ContractTagEntity> sub_label_list) {
        this.sub_label_list = sub_label_list;
    }

    @Override
    public String toString() {
        return "ContractTagEntity{" +
                "code='" + code + '\'' +
                ", label_name='" + label_name + '\'' +
                '}';
    }
}
