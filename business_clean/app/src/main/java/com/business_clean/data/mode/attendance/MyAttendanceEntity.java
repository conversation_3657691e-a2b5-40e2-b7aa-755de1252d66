package com.business_clean.data.mode.attendance;

import java.io.Serializable;
import java.util.List;

public class MyAttendanceEntity implements Serializable {
    private List<MyAttendanceItemEntity> list;

    private String work_days;
    private String actual_work_days;
    private String xx_days;
    private String xj_days;
    private String jb_hours;
    private String cd_time_long;
    private String zt_time_long;
    private String kg_days;
    private String qk_times;

    private String user_uuid;
    private String user_name;
    private String avatar;
    private String project_uuid;
    private String project_name;

    public String getUser_uuid() {
        return user_uuid;
    }

    public void setUser_uuid(String user_uuid) {
        this.user_uuid = user_uuid;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getProject_uuid() {
        return project_uuid;
    }

    public void setProject_uuid(String project_uuid) {
        this.project_uuid = project_uuid;
    }

    public String getProject_name() {
        return project_name;
    }

    public void setProject_name(String project_name) {
        this.project_name = project_name;
    }

    public List<MyAttendanceItemEntity> getList() {
        return list;
    }

    public void setList(List<MyAttendanceItemEntity> list) {
        this.list = list;
    }

    public String getWork_days() {
        return work_days;
    }

    public void setWork_days(String work_days) {
        this.work_days = work_days;
    }

    public String getActual_work_days() {
        return actual_work_days;
    }

    public void setActual_work_days(String actual_work_days) {
        this.actual_work_days = actual_work_days;
    }

    public String getXx_days() {
        return xx_days;
    }

    public void setXx_days(String xx_days) {
        this.xx_days = xx_days;
    }

    public String getXj_days() {
        return xj_days;
    }

    public void setXj_days(String xj_days) {
        this.xj_days = xj_days;
    }

    public String getJb_hours() {
        return jb_hours;
    }

    public void setJb_hours(String jb_hours) {
        this.jb_hours = jb_hours;
    }

    public String getCd_time_long() {
        return cd_time_long;
    }

    public void setCd_time_long(String cd_time_long) {
        this.cd_time_long = cd_time_long;
    }

    public String getZt_time_long() {
        return zt_time_long;
    }

    public void setZt_time_long(String zt_time_long) {
        this.zt_time_long = zt_time_long;
    }

    public String getKg_days() {
        return kg_days;
    }

    public void setKg_days(String kg_days) {
        this.kg_days = kg_days;
    }

    public String getQk_times() {
        return qk_times;
    }

    public void setQk_times(String qk_times) {
        this.qk_times = qk_times;
    }
}
