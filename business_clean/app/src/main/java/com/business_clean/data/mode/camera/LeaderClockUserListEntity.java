package com.business_clean.data.mode.camera;

import java.io.Serializable;

public class LeaderClockUserListEntity implements Serializable {
    private String uuid;
    private String user_name;
    private String project_name;
    private String job_name;
    private String avatar;
    private String status;
    private String status_name;

    private boolean isAdd = false;//是否是新增的人员

    public void setAdd(boolean add) {
        isAdd = add;
    }

    public boolean isAdd() {
        return isAdd;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setStatus_name(String status_name) {
        this.status_name = status_name;
    }

    public String getStatus() {
        return status;
    }

    public String getStatus_name() {
        return status_name;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getProject_name() {
        return project_name;
    }

    public void setProject_name(String project_name) {
        this.project_name = project_name;
    }

    public String getJob_name() {
        return job_name;
    }

    public void setJob_name(String job_name) {
        this.job_name = job_name;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
}
