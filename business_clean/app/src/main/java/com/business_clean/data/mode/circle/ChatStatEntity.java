package com.business_clean.data.mode.circle;

import java.io.Serializable;

public class ChatStatEntity implements Serializable {
    private String total;
    private String all_total;
    private String normal_total;
    private String leader_total;
    private String work_total;
    private String cleaning_total;
    private String inspect_total;
    private String training_total;
    private String word_order_total;

    private String clock_in_total;
    private String other_total;


    public void setOther_total(String other_total) {
        this.other_total = other_total;
    }

    public String getOther_total() {
        return other_total;
    }

    public void setClock_in_total(String clock_in_total) {
        this.clock_in_total = clock_in_total;
    }

    public String getClock_in_total() {
        return clock_in_total;
    }

    public String getInspect_total() {
        return inspect_total;
    }

    public void setInspect_total(String inspect_total) {
        this.inspect_total = inspect_total;
    }

    public String getTraining_total() {
        return training_total;
    }

    public void setTraining_total(String training_total) {
        this.training_total = training_total;
    }

    public String getWord_order_total() {
        return word_order_total;
    }

    public void setWord_order_total(String word_order_total) {
        this.word_order_total = word_order_total;
    }

    public String getCleaning_total() {
        return cleaning_total;
    }

    public void setCleaning_total(String cleaning_total) {
        this.cleaning_total = cleaning_total;
    }

    public void setWork_total(String work_total) {
        this.work_total = work_total;
    }

    public String getWork_total() {
        return work_total;
    }

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public String getAll_total() {
        return all_total;
    }

    public void setAll_total(String all_total) {
        this.all_total = all_total;
    }

    public String getNormal_total() {
        return normal_total;
    }

    public void setNormal_total(String normal_total) {
        this.normal_total = normal_total;
    }

    public String getLeader_total() {
        return leader_total;
    }

    public void setLeader_total(String leader_total) {
        this.leader_total = leader_total;
    }
}
