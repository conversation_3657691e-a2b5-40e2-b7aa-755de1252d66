package com.business_clean.data.mode.camera;

import java.io.Serializable;

/**
 * {
 *     "status": 0,
 *     "result": {
 *         "location": {
 *             "lng": 116.31831126747139,
 *             "lat": 40.04751648273044
 *         },
 *         "formatted_address": "北京市海淀区上地东路21号",
 *         "edz": {
 *             "name": ""
 *         },
 *         "business": "上地,西二旗,马连洼",
 *         "business_info": [
 *             {
 *                 "name": "上地",
 *                 "location": {
 *                     "lng": 116.30883746458304,
 *                     "lat": 40.04833322491141
 *                 },
 *                 "adcode": 110108,
 *                 "distance": 0,
 *                 "direction": "内"
 *             },
 *             {
 *                 "name": "西二旗",
 *                 "location": {
 *                     "lng": 116.31790109768707,
 *                     "lat": 40.05932951618241
 *                 },
 *                 "adcode": 110108,
 *                 "distance": 303,
 *                 "direction": "南"
 *             },
 *             {
 *                 "name": "马连洼",
 *                 "location": {
 *                     "lng": 116.28412355288418,
 *                     "lat": 40.0302794613644
 *                 },
 *                 "adcode": 110108,
 *                 "distance": 914,
 *                 "direction": "东北"
 *             }
 *         ],
 *         "addressComponent": {
 *             "country": "中国",
 *             "country_code": 0,
 *             "country_code_iso": "CHN",
 *             "country_code_iso2": "CN",
 *             "province": "北京市",
 *             "city": "北京市",
 *             "city_level": 2,
 *             "district": "海淀区",
 *             "town": "上地街道",
 *             "town_code": "110108022",
 *             "distance": "72",
 *             "direction": "东南",
 *             "adcode": "110108",
 *             "street": "上地东路",
 *             "street_number": "21号"
 *         },
 *         "pois": [
 *
 *         ],
 *         "roads": [
 *
 *         ],
 *         "poiRegions": [
 *
 *         ],
 *         "sematic_description": "",
 *         "formatted_address_poi": "",
 *         "cityCode": 131
 *     }
 * }
 */
public class BaiduGpsAddressInfo implements Serializable {
    private String formatted_address;
    private String business;

    public String getFormatted_address() {
        return formatted_address;
    }

    public void setFormatted_address(String formatted_address) {
        this.formatted_address = formatted_address;
    }

    public String getBusiness() {
        return business;
    }

    public void setBusiness(String business) {
        this.business = business;
    }
}
