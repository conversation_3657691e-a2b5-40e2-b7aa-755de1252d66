package com.business_clean.data.mode.appupdate

import java.io.Serializable
import java.util.*


/**
 * 弹窗配置升级的信息
 */
class DialogAlertNoticeInfo : Serializable {
    var title //主标题
            : String? = null
    var describe //副标题
            : String? = null
    var content //内容
            : String? = null
    var img_url: Any? = null
    var close_button: String? = null
    var confirm_button: String? = null
    var url: String? = null

    var number: String? = null
    var application_address_info: String? = null //应用地址json信息：外部url：{"url":"www.baidu.com"}  小程序：{"gh":"原始id","path":"index/path"} 内部地址：{"url":"insurance/list"} 二级应用：{"2":3,"1":2,"0":1}
    var application_address_type: String? = null //应用地址类型：1-外部url 2-小程序 3-app内部应用 4-二级应用

    var sub_title_color //自己构建的字段 副标题的颜色
            = 0
    var content_bg_color = 0 //自己构建的字段 内容的背景颜色
    var isDismissOnBackPressed = true //自己构建的字段 按返回键是否消失
    var isDismissOnTouchOutside = true // 自己构建的字段点击外部消失

    var right_weight = 1   //按照左边按钮长度为1来看，右边按钮的比重。   默认是1，则是左右即是1：1     如果是3，则是1：3
}