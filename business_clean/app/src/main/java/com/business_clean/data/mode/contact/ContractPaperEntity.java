package com.business_clean.data.mode.contact;

import com.business_clean.data.mode.members.IdCardPicEntity;

import java.io.Serializable;
import java.util.List;

public class ContractPaperEntity implements Serializable {
    private String uuid;
    private String start_time;
    private String end_time;
    private String label_code;
    private String contract_sign_type;

    private String remark;
    private String left_reason;
    private String left_reason_name;
    private List<String> contract_pic_list;


    public void setLeft_reason(String left_reason) {
        this.left_reason = left_reason;
    }

    public void setLeft_reason_name(String left_reason_name) {
        this.left_reason_name = left_reason_name;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLeft_reason() {
        return left_reason;
    }

    public String getLeft_reason_name() {
        return left_reason_name;
    }

    public String getRemark() {
        return remark;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public String getStart_time() {
        return start_time;
    }

    public String getLabel_code() {
        return label_code;
    }

    public void setLabel_code(String label_code) {
        this.label_code = label_code;
    }

    public String getContract_sign_type() {
        return contract_sign_type;
    }

    public void setContract_sign_type(String contract_sign_type) {
        this.contract_sign_type = contract_sign_type;
    }

    public List<String> getContract_pic_list() {
        return contract_pic_list;
    }

    public void setContract_pic_list(List<String> contract_pic_list) {
        this.contract_pic_list = contract_pic_list;
    }
}
