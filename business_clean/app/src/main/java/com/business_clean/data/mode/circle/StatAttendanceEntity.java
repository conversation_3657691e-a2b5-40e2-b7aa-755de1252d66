package com.business_clean.data.mode.circle;

import java.io.Serializable;

public class StatAttendanceEntity implements Serializable {

    private String all_total;
    private String actual_attendance_total;
    private String abnormal_total;
    private String overtimes_total;


    public void setOvertimes_total(String overtimes_total) {
        this.overtimes_total = overtimes_total;
    }

    public String getOvertimes_total() {
        return overtimes_total;
    }

    public String getAll_total() {
        return all_total;
    }

    public void setAll_total(String all_total) {
        this.all_total = all_total;
    }

    public String getActual_attendance_total() {
        return actual_attendance_total;
    }

    public void setActual_attendance_total(String actual_attendance_total) {
        this.actual_attendance_total = actual_attendance_total;
    }

    public String getAbnormal_total() {
        return abnormal_total;
    }

    public void setAbnormal_total(String abnormal_total) {
        this.abnormal_total = abnormal_total;
    }
}
