package com.business_clean.data.mode.attendance;

import java.io.Serializable;

public class MyAttendanceDetailListItemOperateLogEntity implements Serializable {
    private String uuid;
    private String title;
    private String sb_status;
    private String sb_status_name;
    private String cd_time_long;
    private String xb_status;
    private String xb_status_name;
    private String zt_time_long;


    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public String getSb_status() {
        return sb_status;
    }

    public void setSb_status(String sb_status) {
        this.sb_status = sb_status;
    }

    public String getSb_status_name() {
        return sb_status_name;
    }

    public void setSb_status_name(String sb_status_name) {
        this.sb_status_name = sb_status_name;
    }

    public String getCd_time_long() {
        return cd_time_long;
    }

    public void setCd_time_long(String cd_time_long) {
        this.cd_time_long = cd_time_long;
    }

    public String getXb_status() {
        return xb_status;
    }

    public void setXb_status(String xb_status) {
        this.xb_status = xb_status;
    }

    public String getXb_status_name() {
        return xb_status_name;
    }

    public void setXb_status_name(String xb_status_name) {
        this.xb_status_name = xb_status_name;
    }

    public String getZt_time_long() {
        return zt_time_long;
    }

    public void setZt_time_long(String zt_time_long) {
        this.zt_time_long = zt_time_long;
    }
}
