package com.business_clean.data.mode.attendance;

import java.io.Serializable;

public class MyAttendanceDetailListItemHolidayEntity implements Serializable {
    private String introduction;
    private String uuid;
    private String start_date;
    private String start_type;
    private String start_type_name;
    private String end_date;
    private String end_type;
    private String end_type_name;
    private String holiday_type;
    private String holiday_type_name;
    private String reason;
    private String time_long;

    public String getTime_long() {
        return time_long;
    }

    public void setTime_long(String time_long) {
        this.time_long = time_long;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getStart_type() {
        return start_type;
    }

    public void setStart_type(String start_type) {
        this.start_type = start_type;
    }

    public String getStart_type_name() {
        return start_type_name;
    }

    public void setStart_type_name(String start_type_name) {
        this.start_type_name = start_type_name;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public String getEnd_type() {
        return end_type;
    }

    public void setEnd_type(String end_type) {
        this.end_type = end_type;
    }

    public String getEnd_type_name() {
        return end_type_name;
    }

    public void setEnd_type_name(String end_type_name) {
        this.end_type_name = end_type_name;
    }

    public String getHoliday_type() {
        return holiday_type;
    }

    public void setHoliday_type(String holiday_type) {
        this.holiday_type = holiday_type;
    }

    public String getHoliday_type_name() {
        return holiday_type_name;
    }

    public void setHoliday_type_name(String holiday_type_name) {
        this.holiday_type_name = holiday_type_name;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReason() {
        return reason;
    }
}
