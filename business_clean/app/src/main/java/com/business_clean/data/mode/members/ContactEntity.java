package com.business_clean.data.mode.members;

import java.io.Serializable;

public class ContactEntity implements Serializable {
    private String mobile;
    private String exigency_user_name;
    private String exigency_user_mobile;
    private String exigency_user_relation;
    private String exigency_user_relation_name;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getExigency_user_name() {
        return exigency_user_name;
    }

    public void setExigency_user_name(String exigency_user_name) {
        this.exigency_user_name = exigency_user_name;
    }

    public String getExigency_user_mobile() {
        return exigency_user_mobile;
    }

    public void setExigency_user_mobile(String exigency_user_mobile) {
        this.exigency_user_mobile = exigency_user_mobile;
    }

    public String getExigency_user_relation() {
        return exigency_user_relation;
    }

    public void setExigency_user_relation(String exigency_user_relation) {
        this.exigency_user_relation = exigency_user_relation;
    }

    public String getExigency_user_relation_name() {
        return exigency_user_relation_name;
    }

    public void setExigency_user_relation_name(String exigency_user_relation_name) {
        this.exigency_user_relation_name = exigency_user_relation_name;
    }


    @Override
    public String toString() {
        return "ContactEntity{" +
                "mobile='" + mobile + '\'' +
                ", exigency_user_name='" + exigency_user_name + '\'' +
                ", exigency_user_mobile='" + exigency_user_mobile + '\'' +
                ", exigency_user_relation='" + exigency_user_relation + '\'' +
                ", exigency_user_relation_name='" + exigency_user_relation_name + '\'' +
                '}';
    }
}
