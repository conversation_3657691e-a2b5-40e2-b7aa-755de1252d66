package com.business_clean.data.mode.classes;

import java.io.Serializable;

public class ClassesListEntity implements Serializable {
    private String uuid;
    private String class_name;
    private String class_time;
    private String out_class_desc;
    private String in_class_desc;
    private String is_no_clock;
    private String class_type;
    private String is_default_rest;
    private String class_type_name;
    private String is_no_clock_name;
    private String is_default_rest_name;
    private String avg_line_time;
    private String avg_line_is_today;


    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getClass_name() {
        return class_name;
    }

    public void setClass_name(String class_name) {
        this.class_name = class_name;
    }

    public String getClass_time() {
        return class_time;
    }

    public void setClass_time(String class_time) {
        this.class_time = class_time;
    }

    public String getOut_class_desc() {
        return out_class_desc;
    }

    public void setOut_class_desc(String out_class_desc) {
        this.out_class_desc = out_class_desc;
    }

    public String getIn_class_desc() {
        return in_class_desc;
    }

    public void setIn_class_desc(String in_class_desc) {
        this.in_class_desc = in_class_desc;
    }

    public String getIs_no_clock() {
        return is_no_clock;
    }

    public void setIs_no_clock(String is_no_clock) {
        this.is_no_clock = is_no_clock;
    }

    public String getClass_type() {
        return class_type;
    }

    public void setClass_type(String class_type) {
        this.class_type = class_type;
    }

    public String getIs_default_rest() {
        return is_default_rest;
    }

    public void setIs_default_rest(String is_default_rest) {
        this.is_default_rest = is_default_rest;
    }

    public String getClass_type_name() {
        return class_type_name;
    }

    public void setClass_type_name(String class_type_name) {
        this.class_type_name = class_type_name;
    }

    public String getIs_no_clock_name() {
        return is_no_clock_name;
    }

    public void setIs_no_clock_name(String is_no_clock_name) {
        this.is_no_clock_name = is_no_clock_name;
    }

    public String getIs_default_rest_name() {
        return is_default_rest_name;
    }

    public void setIs_default_rest_name(String is_default_rest_name) {
        this.is_default_rest_name = is_default_rest_name;
    }

    public String getAvg_line_time() {
        return avg_line_time;
    }

    public void setAvg_line_time(String avg_line_time) {
        this.avg_line_time = avg_line_time;
    }

    public String getAvg_line_is_today() {
        return avg_line_is_today;
    }

    public void setAvg_line_is_today(String avg_line_is_today) {
        this.avg_line_is_today = avg_line_is_today;
    }
}
