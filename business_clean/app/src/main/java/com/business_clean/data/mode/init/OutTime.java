package com.business_clean.data.mode.init;

import java.io.Serializable;
import java.util.List;

public class OutTime implements Serializable {

    private List<BaseIDNameEntity> before_list;
    private List<BaseIDNameEntity> after_list;


    public void setAfter_list(List<BaseIDNameEntity> after_list) {
        this.after_list = after_list;
    }

    public void setBefore_list(List<BaseIDNameEntity> before_list) {
        this.before_list = before_list;
    }

    public List<BaseIDNameEntity> getBefore_list() {
        return before_list;
    }

    public List<BaseIDNameEntity> getAfter_list() {
        return after_list;
    }

    @Override
    public String toString() {
        return "OutTime{" +
                "before_list=" + before_list +
                ", after_list=" + after_list +
                '}';
    }
}
