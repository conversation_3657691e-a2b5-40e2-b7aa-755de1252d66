package com.business_clean.data.mode.members;

import java.io.Serializable;

public class OCRIDCardInfoEntity implements Serializable {
    private String image_status;
    private String image_status_name;
    private String direction;
    private String address;
    private String birthday;
    private String name;
    private String id_number;
    private String hometown;
    private String hometown_city;
    private String sex;
    private String nation;
    private String chinese_zodiac;
    private String zodiac;

    public String getImage_status() {
        return image_status;
    }

    public void setImage_status(String image_status) {
        this.image_status = image_status;
    }

    public String getImage_status_name() {
        return image_status_name;
    }

    public void setImage_status_name(String image_status_name) {
        this.image_status_name = image_status_name;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId_number() {
        return id_number;
    }

    public void setId_number(String id_number) {
        this.id_number = id_number;
    }

    public String getHometown() {
        return hometown;
    }

    public void setHometown(String hometown) {
        this.hometown = hometown;
    }

    public String getHometown_city() {
        return hometown_city;
    }

    public void setHometown_city(String hometown_city) {
        this.hometown_city = hometown_city;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getChinese_zodiac() {
        return chinese_zodiac;
    }

    public void setChinese_zodiac(String chinese_zodiac) {
        this.chinese_zodiac = chinese_zodiac;
    }

    public String getZodiac() {
        return zodiac;
    }

    public void setZodiac(String zodiac) {
        this.zodiac = zodiac;
    }
}
