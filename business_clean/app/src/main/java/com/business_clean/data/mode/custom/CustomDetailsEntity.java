package com.business_clean.data.mode.custom;

import java.io.Serializable;

public class CustomDetailsEntity implements Serializable {
    private String uuid;
    private String custom_full_name;
    private String province_id;
    private String province_name;
    private String city_id;
    private String city_name;
    private String area_id;
    private String area_name;
    private String address;
    private String contact_person;
    private String contact_mobile;
    private String remark;
    private String custom_name;

    private String manager_user_name;

    private String manager_user_uuid;

    private String department_name;
    private String department_uuid;

    public void setDepartment_name(String department_name) {
        this.department_name = department_name;
    }

    public void setDepartment_uuid(String department_uuid) {
        this.department_uuid = department_uuid;
    }

    public String getDepartment_name() {
        return department_name;
    }

    public String getDepartment_uuid() {
        return department_uuid;
    }

    public void setManager_user_name(String manager_user_name) {
        this.manager_user_name = manager_user_name;
    }

    public String getManager_user_name() {
        return manager_user_name;
    }

    public void setManager_user_uuid(String manager_user_uuid) {
        this.manager_user_uuid = manager_user_uuid;
    }

    public String getManager_user_uuid() {
        return manager_user_uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getCustom_full_name() {
        return custom_full_name;
    }

    public void setCustom_full_name(String custom_full_name) {
        this.custom_full_name = custom_full_name;
    }

    public String getProvince_id() {
        return province_id;
    }

    public void setProvince_id(String province_id) {
        this.province_id = province_id;
    }

    public String getProvince_name() {
        return province_name;
    }

    public void setProvince_name(String province_name) {
        this.province_name = province_name;
    }

    public String getCity_id() {
        return city_id;
    }

    public void setCity_id(String city_id) {
        this.city_id = city_id;
    }

    public String getCity_name() {
        return city_name;
    }

    public void setCity_name(String city_name) {
        this.city_name = city_name;
    }

    public String getArea_id() {
        return area_id;
    }

    public void setArea_id(String area_id) {
        this.area_id = area_id;
    }

    public String getArea_name() {
        return area_name;
    }

    public void setArea_name(String area_name) {
        this.area_name = area_name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getContact_person() {
        return contact_person;
    }

    public void setContact_person(String contact_person) {
        this.contact_person = contact_person;
    }

    public String getContact_mobile() {
        return contact_mobile;
    }

    public void setContact_mobile(String contact_mobile) {
        this.contact_mobile = contact_mobile;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCustom_name() {
        return custom_name;
    }

    public void setCustom_name(String custom_name) {
        this.custom_name = custom_name;
    }
}
