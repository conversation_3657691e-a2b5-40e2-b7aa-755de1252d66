package com.business_clean.data.mode.circle;

import android.text.TextUtils;

import com.chad.library.adapter.base.entity.MultiItemEntity;


public class ChatMessage implements MultiItemEntity {
    private String uuid;
    private String user_name;
    private String job_name;
    private String avatar;
    private String is_self;
    private String act_type;
    private String act_type_name;
    private String message_type;
    private String media_url;
    private String origin_media_url;
    private String video_cover_url;
    private String message_content;
    private String create_time;

    private String create_date;

    private String pic_thumb;

    public void setCreate_date(String create_date) {
        this.create_date = create_date;
    }

    public String getCreate_date() {
        return create_date;
    }

    public void setPic_thumb(String pic_thumb) {
        this.pic_thumb = pic_thumb;
    }

    public String getPic_thumb() {
        return pic_thumb;
    }



    public void setOrigin_media_url(String origin_media_url) {
        this.origin_media_url = origin_media_url;
    }

    public String getOrigin_media_url() {
        return origin_media_url;
    }

    public void setAct_type_name(String act_type_name) {
        this.act_type_name = act_type_name;
    }

    public String getAct_type_name() {
        return act_type_name;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUser_name() {
        return user_name;
    }

    public void setUser_name(String user_name) {
        this.user_name = user_name;
    }

    public String getJob_name() {
        return job_name;
    }

    public void setJob_name(String job_name) {
        this.job_name = job_name;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getIs_self() {
        return is_self;
    }

    public void setIs_self(String is_self) {
        this.is_self = is_self;
    }

    public String getAct_type() {
        return act_type;
    }

    public void setAct_type(String act_type) {
        this.act_type = act_type;
    }

    public String getMessage_type() {
        return message_type;
    }

    public void setMessage_type(String message_type) {
        this.message_type = message_type;
    }

    public String getMedia_url() {
        return media_url;
    }

    public void setMedia_url(String media_url) {
        this.media_url = media_url;
    }

    public String getVideo_cover_url() {
        return video_cover_url;
    }

    public void setVideo_cover_url(String video_cover_url) {
        this.video_cover_url = video_cover_url;
    }

    public String getMessage_content() {
        return message_content;
    }

    public void setMessage_content(String message_content) {
        this.message_content = message_content;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    @Override
    public int getItemType() {
//        if (!TextUtils.isEmpty(is_self)) {
//            return Integer.parseInt(is_self);
//        }
        return 0;
    }
}
