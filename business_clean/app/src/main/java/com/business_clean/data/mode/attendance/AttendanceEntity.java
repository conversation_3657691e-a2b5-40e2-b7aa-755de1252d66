package com.business_clean.data.mode.attendance;

import java.io.Serializable;
import java.util.List;

public class AttendanceEntity implements Serializable {

    private List<AttendanceListEntity> list;

    private String all_total;
    private String should_attendance_total;
    private String actual_attendance_total;
    private String holiday_total;
    private String overtime_total;
    private String absenteeism_total;
    private String qk_total;

    private String rest_total;
    private String entry_total;
    private String left_total;
    private String page;

    public void setEntry_total(String entry_total) {
        this.entry_total = entry_total;
    }

    public void setLeft_total(String left_total) {
        this.left_total = left_total;
    }

    public String getEntry_total() {
        return entry_total;
    }

    public String getLeft_total() {
        return left_total;
    }

    public void setRest_total(String rest_total) {
        this.rest_total = rest_total;
    }

    public String getRest_total() {
        return rest_total;
    }

    public List<AttendanceListEntity> getList() {
        return list;
    }

    public void setList(List<AttendanceListEntity> list) {
        this.list = list;
    }

    public String getAll_total() {
        return all_total;
    }

    public void setAll_total(String all_total) {
        this.all_total = all_total;
    }

    public String getShould_attendance_total() {
        return should_attendance_total;
    }

    public void setShould_attendance_total(String should_attendance_total) {
        this.should_attendance_total = should_attendance_total;
    }

    public String getActual_attendance_total() {
        return actual_attendance_total;
    }

    public void setActual_attendance_total(String actual_attendance_total) {
        this.actual_attendance_total = actual_attendance_total;
    }

    public void setHoliday_total(String holiday_total) {
        this.holiday_total = holiday_total;
    }

    public String getHoliday_total() {
        return holiday_total;
    }

    public String getOvertime_total() {
        return overtime_total;
    }

    public void setOvertime_total(String overtime_total) {
        this.overtime_total = overtime_total;
    }

    public String getAbsenteeism_total() {
        return absenteeism_total;
    }

    public void setAbsenteeism_total(String absenteeism_total) {
        this.absenteeism_total = absenteeism_total;
    }

    public String getQk_total() {
        return qk_total;
    }

    public void setQk_total(String qk_total) {
        this.qk_total = qk_total;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }
}
