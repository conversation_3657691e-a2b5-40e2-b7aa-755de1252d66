package com.business_clean.data.mode.members;

import java.io.Serializable;

public class EntryConditionEntity implements Serializable {
    private boolean before_entry;
    private boolean is_draft;
    private String uuid;

    public EntryConditionEntity(boolean before_entry, boolean is_draft, String uuid) {
        this.before_entry = before_entry;
        this.is_draft = is_draft;
        this.uuid = uuid;
    }

    public boolean isBefore_entry() {
        return before_entry;
    }

    public void setBefore_entry(boolean before_entry) {
        this.before_entry = before_entry;
    }

    public boolean isIs_draft() {
        return is_draft;
    }

    public void setIs_draft(boolean is_draft) {
        this.is_draft = is_draft;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
}
