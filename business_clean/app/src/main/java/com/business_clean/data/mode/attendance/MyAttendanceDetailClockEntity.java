package com.business_clean.data.mode.attendance;

import java.io.Serializable;

public class MyAttendanceDetailClockEntity implements Serializable {
    private String uuid;
    private String in_title;
    private String in_status;
    private String in_status_name;
    private String in_clock_time;
    private String in_should_clock_time;
    private String in_actual_clock_time;
    private String in_is_clock;
    private String in_clock_type_name;
    private String in_media_url;
    private String in_pic_thumb;
    private String in_video_cover_url;
    private String in_origin_media_url;
    private String in_message_type;
    private String in_media_type;
    private String in_operate_name;
    private String out_title;
    private String out_status;
    private String out_status_name;
    private String out_clock_time;
    private String out_is_current_day;
    private String out_should_clock_time;
    private String out_actual_clock_time;
    private String out_is_clock;
    private String out_clock_type_name;
    private String out_media_url;
    private String out_pic_thumb;
    private String out_video_cover_url;
    private String out_origin_media_url;
    private String out_message_type;
    private String out_media_type;
    private String out_operate_name;
    private String in_is_current_day;

    public void setIn_is_current_day(String in_is_current_day) {
        this.in_is_current_day = in_is_current_day;
    }

    public String getIn_is_current_day() {
        return in_is_current_day;
    }

    public void setOut_is_current_day(String out_is_current_day) {
        this.out_is_current_day = out_is_current_day;
    }

    public String getOut_is_current_day() {
        return out_is_current_day;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public String getIn_title() {
        return in_title;
    }

    public void setIn_title(String in_title) {
        this.in_title = in_title;
    }

    public String getIn_status() {
        return in_status;
    }

    public void setIn_status(String in_status) {
        this.in_status = in_status;
    }

    public String getIn_status_name() {
        return in_status_name;
    }

    public void setIn_status_name(String in_status_name) {
        this.in_status_name = in_status_name;
    }

    public String getIn_clock_time() {
        return in_clock_time;
    }

    public void setIn_clock_time(String in_clock_time) {
        this.in_clock_time = in_clock_time;
    }

    public String getIn_should_clock_time() {
        return in_should_clock_time;
    }

    public void setIn_should_clock_time(String in_should_clock_time) {
        this.in_should_clock_time = in_should_clock_time;
    }

    public String getIn_actual_clock_time() {
        return in_actual_clock_time;
    }

    public void setIn_actual_clock_time(String in_actual_clock_time) {
        this.in_actual_clock_time = in_actual_clock_time;
    }

    public String getIn_is_clock() {
        return in_is_clock;
    }

    public void setIn_is_clock(String in_is_clock) {
        this.in_is_clock = in_is_clock;
    }

    public String getIn_clock_type_name() {
        return in_clock_type_name;
    }

    public void setIn_clock_type_name(String in_clock_type_name) {
        this.in_clock_type_name = in_clock_type_name;
    }

    public String getIn_media_url() {
        return in_media_url;
    }

    public void setIn_media_url(String in_media_url) {
        this.in_media_url = in_media_url;
    }

    public String getIn_pic_thumb() {
        return in_pic_thumb;
    }

    public void setIn_pic_thumb(String in_pic_thumb) {
        this.in_pic_thumb = in_pic_thumb;
    }

    public String getIn_video_cover_url() {
        return in_video_cover_url;
    }

    public void setIn_video_cover_url(String in_video_cover_url) {
        this.in_video_cover_url = in_video_cover_url;
    }

    public String getIn_origin_media_url() {
        return in_origin_media_url;
    }

    public void setIn_origin_media_url(String in_origin_media_url) {
        this.in_origin_media_url = in_origin_media_url;
    }

    public String getIn_message_type() {
        return in_message_type;
    }

    public void setIn_message_type(String in_message_type) {
        this.in_message_type = in_message_type;
    }

    public String getIn_media_type() {
        return in_media_type;
    }

    public void setIn_media_type(String in_media_type) {
        this.in_media_type = in_media_type;
    }

    public String getIn_operate_name() {
        return in_operate_name;
    }

    public void setIn_operate_name(String in_operate_name) {
        this.in_operate_name = in_operate_name;
    }

    public String getOut_title() {
        return out_title;
    }

    public void setOut_title(String out_title) {
        this.out_title = out_title;
    }

    public String getOut_status() {
        return out_status;
    }

    public void setOut_status(String out_status) {
        this.out_status = out_status;
    }

    public String getOut_status_name() {
        return out_status_name;
    }

    public void setOut_status_name(String out_status_name) {
        this.out_status_name = out_status_name;
    }

    public String getOut_clock_time() {
        return out_clock_time;
    }

    public void setOut_clock_time(String out_clock_time) {
        this.out_clock_time = out_clock_time;
    }

    public String getOut_should_clock_time() {
        return out_should_clock_time;
    }

    public void setOut_should_clock_time(String out_should_clock_time) {
        this.out_should_clock_time = out_should_clock_time;
    }

    public String getOut_actual_clock_time() {
        return out_actual_clock_time;
    }

    public void setOut_actual_clock_time(String out_actual_clock_time) {
        this.out_actual_clock_time = out_actual_clock_time;
    }

    public String getOut_is_clock() {
        return out_is_clock;
    }

    public void setOut_is_clock(String out_is_clock) {
        this.out_is_clock = out_is_clock;
    }

    public String getOut_clock_type_name() {
        return out_clock_type_name;
    }

    public void setOut_clock_type_name(String out_clock_type_name) {
        this.out_clock_type_name = out_clock_type_name;
    }

    public String getOut_media_url() {
        return out_media_url;
    }

    public void setOut_media_url(String out_media_url) {
        this.out_media_url = out_media_url;
    }

    public String getOut_pic_thumb() {
        return out_pic_thumb;
    }

    public void setOut_pic_thumb(String out_pic_thumb) {
        this.out_pic_thumb = out_pic_thumb;
    }

    public String getOut_video_cover_url() {
        return out_video_cover_url;
    }

    public void setOut_video_cover_url(String out_video_cover_url) {
        this.out_video_cover_url = out_video_cover_url;
    }

    public String getOut_origin_media_url() {
        return out_origin_media_url;
    }

    public void setOut_origin_media_url(String out_origin_media_url) {
        this.out_origin_media_url = out_origin_media_url;
    }

    public String getOut_message_type() {
        return out_message_type;
    }

    public void setOut_message_type(String out_message_type) {
        this.out_message_type = out_message_type;
    }

    public String getOut_media_type() {
        return out_media_type;
    }

    public void setOut_media_type(String out_media_type) {
        this.out_media_type = out_media_type;
    }

    public String getOut_operate_name() {
        return out_operate_name;
    }

    public void setOut_operate_name(String out_operate_name) {
        this.out_operate_name = out_operate_name;
    }
}
