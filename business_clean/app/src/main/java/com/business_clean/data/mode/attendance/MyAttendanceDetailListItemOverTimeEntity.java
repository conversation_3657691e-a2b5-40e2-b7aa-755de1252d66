package com.business_clean.data.mode.attendance;

import java.io.Serializable;

public class MyAttendanceDetailListItemOverTimeEntity implements Serializable {
    private String uuid;
    private String start_time;
    private String end_time;
    private String overtime_long;
    private String overtime_date;
    private String overtime_type;
    private String overtime_type_name;
    private String reason;


    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public String getOvertime_long() {
        return overtime_long;
    }

    public void setOvertime_long(String overtime_long) {
        this.overtime_long = overtime_long;
    }

    public String getOvertime_date() {
        return overtime_date;
    }

    public void setOvertime_date(String overtime_date) {
        this.overtime_date = overtime_date;
    }

    public String getOvertime_type() {
        return overtime_type;
    }

    public void setOvertime_type(String overtime_type) {
        this.overtime_type = overtime_type;
    }

    public String getOvertime_type_name() {
        return overtime_type_name;
    }

    public void setOvertime_type_name(String overtime_type_name) {
        this.overtime_type_name = overtime_type_name;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReason() {
        return reason;
    }
}
