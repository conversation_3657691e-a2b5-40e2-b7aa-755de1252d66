package com.business_clean.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.business_clean.app.network.NetUrl
import com.business_clean.data.mode.contact.CompanyContactEntity
import com.business_clean.data.mode.contact.ContactListEntity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

/**
 * 添加总部联系人，常用联系人
 */
class ProjectPersonnelViewModel : BaseViewModel() {

    var companyContactDetail = MutableLiveData<CompanyContactEntity>()

    var saveContactUpdate = MutableLiveData<Any>()

    var contactDetail = MutableLiveData<ContactListEntity>()


    /**
     * 总部人员详情
     */
    fun requestCompanyContactDetail(uuid: String) {
        rxHttpRequest {
            onRequest = {
                companyContactDetail.value = RxHttp.get(NetUrl.GET_COMPANY_CONTACTA_DETAIL)
                    .add("uuid", uuid)
                    .toResponse<CompanyContactEntity>().await()
            }
        }
    }

    /**
     * 添加总部成员
     */
    fun requestSaveCompanyContact(hashMap: HashMap<String, String>) {
        rxHttpRequest {
            onRequest = {
                saveContactUpdate.value = RxHttp.get(NetUrl.SAVE_COMPANY_CONTACTA)
                    .addAll(hashMap)
                    .toResponse<Any>().await()
            }
        }
    }

    /**
     * 添加常用联系人
     */
    fun requestSaveContact(hashMap: HashMap<String, String>) {
        rxHttpRequest {
            onRequest = {
                saveContactUpdate.value = RxHttp.get(NetUrl.SAVE_CONTACTA)
                    .addAll(hashMap)
                    .toResponse<Any>().await()
            }
        }
    }

    /**
     * 常用联系人详情
     */
    fun requestContactDetail(uuid: String) {
        rxHttpRequest {
            onRequest = {
                contactDetail.value = RxHttp.get(NetUrl.GET_CONTACTA_DETAIL)
                    .add("uuid", uuid)
                    .toResponse<ContactListEntity>().await()
            }
        }
    }


}