package com.business_clean.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.business_clean.app.App
import com.business_clean.app.config.Constant
import com.business_clean.app.network.NetUrl
import com.business_clean.data.mode.address.AddressEntity
import com.business_clean.data.mode.address.AddressListEntity
import com.business_clean.data.mode.address.RegeoEntity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import me.hgj.mvvmhelper.net.LoadingType
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class AddressViewModel : BaseViewModel() {

    var addressManager = MutableLiveData<AddressEntity>()

    var addressDetailManager = MutableLiveData<AddressListEntity>()

    var saveAddress = MutableLiveData<Any>()

    var delsStatus = MutableLiveData<Any>()

    var regeo = MutableLiveData<RegeoEntity>()

    /**
     * 获取全部地址
     */
    fun requestAddressAll(page: Int, project_uuid: String) {
        rxHttpRequest {
            onRequest = {
                addressManager.value = RxHttp.get(NetUrl.GET_ADDRESS_ALL)
                    .add("page", page)
                    .add("size", Constant.PAGE_SIZE)
                    .add("project_uuid", project_uuid)
                    .add("map_code", "gao_de")
                    .toResponse<AddressEntity>().await()
            }
        }
    }


    /**
     * 获取地址详情
     */
    fun requestAddressDetail(uuid: String) {
        rxHttpRequest {
            onRequest = {
                addressDetailManager.value = RxHttp.get(NetUrl.GET_ADDRESS_DETIALI)
                    .add("uuid", uuid)
                    .add("map_code", "gao_de")
                    .add("project_uuid", App.getAppViewModelInstance().userInfo.value?.project?.uuid)
                    .toResponse<AddressListEntity>().await()
            }
        }
    }

    /**
     * 新建地址
     */
    fun requestSaveAddress(hashMap: HashMap<String, String>) {
        rxHttpRequest {
            onRequest = {
                saveAddress.value = RxHttp.get(NetUrl.SAVE_ADDRESS)
                    .addAll(hashMap)
                    .toResponse<Any>().await()
            }
        }
    }

    /**
     * 删除地址
     */
    fun delAddress(uuid: String, project_uuid: String) {
        rxHttpRequest {
            onRequest = {
                delsStatus.value = RxHttp.get(NetUrl.DEL_ADDRESS)
                    .add("uuid", uuid)
                    .add("project_uuid", project_uuid)
                    .toResponse<Any>().await()
            }
        }
    }

    /**
     * 逆地理编码
     */
    fun getRegeo(lat: Double, lnt: Double) {
        rxHttpRequest {
            onRequest = {
                regeo.value = RxHttp.get(NetUrl.GET_ADDRESS_REGEO)
                    .add("lnt", lnt)
                    .add("lat", lat)
                    .add("map_code", "gao_de")
                    .toResponse<RegeoEntity>().await()
            }
            loadingType = LoadingType.LOADING_NULL
        }
    }


}