package com.business_clean.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.business_clean.app.network.NetUrl
import com.business_clean.data.mode.permission.PermissionList
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class PermissionsViewModel : BaseViewModel() {

    var permission = MutableLiveData<PermissionList>()


    /**
     * 获取所有的权限角色
     */
    fun requestPermissionsll(type: String) {
        rxHttpRequest {
            onRequest = {
                permission.value = RxHttp.get(NetUrl.GET_PERMISSIONS)
                    .add("type", "" + type)//类型0全部 1总部员工角色 2项目成员角色
                    .toResponse<PermissionList>().await()
            }
        }
    }
}