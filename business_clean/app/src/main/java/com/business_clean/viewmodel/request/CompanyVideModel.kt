package com.business_clean.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.business_clean.app.network.NetUrl
import com.business_clean.data.mode.login.CodeEntity
import com.business_clean.data.mode.login.UserInfo
import com.business_clean.data.mode.project.WorkRulesEntity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse


class CompanyVideModel : BaseViewModel() {

    var status = MutableLiveData<Any>()

    /**
     * 删除
     */
    fun deleteProject(project_uuid: String) {
        rxHttpRequest {
            onRequest = {
                status.value = RxHttp.get(NetUrl.COMPANY_PROJECT_DELETE)
                    .add("project_uuid", project_uuid)
                    .toResponse<CodeEntity>().await()
            }
        }
    }


}