package com.business_clean.viewmodel.request

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import com.business_clean.app.App
import com.business_clean.app.config.Constant
import com.business_clean.app.network.NetUrl
import com.business_clean.data.initconfig.MapInfo
import com.business_clean.data.initconfig.TimestampEntity
import com.business_clean.data.mode.address.AddressEntity
import com.business_clean.data.mode.address.RegeoEntity
import com.business_clean.data.mode.camera.CameraConfigData
import com.business_clean.data.mode.camera.LeaderClockEntity
import com.business_clean.data.mode.camera.TeamClassEntity
import com.business_clean.data.mode.camera.TeamClassListEntity
import com.business_clean.data.mode.camera.TeamClassUserEntity
import com.business_clean.data.mode.camera.TodayTaskEntity
import com.business_clean.data.mode.login.UserInfo
import com.business_clean.data.mode.project.ClockRadiusEntity
import com.business_clean.data.mode.project.WorkRulesEntity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import me.hgj.mvvmhelper.net.LoadingType
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

//拍照相关内容
class CameraViewModel : BaseViewModel() {


    var settingState = MutableLiveData<Any>()

    var leaderClockEntity = MutableLiveData<LeaderClockEntity>()

    var addressManager = MutableLiveData<AddressEntity>()

    var check = MutableLiveData<Any>()

    var todayTaskEntity = MutableLiveData<TodayTaskEntity>()

    var projectOneEntity = MutableLiveData<ClockRadiusEntity>()


    var userOne = MutableLiveData<UserInfo>()


    var timestampEntity = MutableLiveData<TimestampEntity>()

    var errorEntity = MutableLiveData<Any>()


    //获取用工规则
    var workRulesEntity = MutableLiveData<WorkRulesEntity>()

    //获取所有的班次列表
    var classTeamEntity = MutableLiveData<TeamClassEntity>()

    //获取所有的班次用户列表
    var classTeamUserEntity = MutableLiveData<TeamClassListEntity>()


    ///逆地理编码
    var regeo = MutableLiveData<RegeoEntity>()


    ///map的info
    var mapInfo = MutableLiveData<MapInfo>()

    ///相机的配置
    var cameraConfigData = MutableLiveData<CameraConfigData>()

    /**
     * 员工的设置
     */
    fun requestPhotoSetting(hashMap: HashMap<String, String>) {
        rxHttpRequest {
            onRequest = {
                settingState.value = RxHttp.get(NetUrl.CAMERA_SETTING)
                    .addAll(hashMap)
                    .toResponse<Any>().await()
            }
        }
    }


    /**
     * 更新用户信息
     */
    fun requestUserInfoData() {
        rxHttpRequest {
            onRequest = {
                userOne.value = RxHttp.get(NetUrl.GET_USER_INFO)
                    .toResponse<UserInfo>().await()
            }
        }
    }


    /**
     * 获取默认出勤配置
     */
    fun requestAttendance() {
        rxHttpRequest {
            onRequest = {
                leaderClockEntity.value = RxHttp.get(NetUrl.CAMERA_ATTENDANCE_INFO)
                    .add("project_uuid", App.getAppViewModelInstance().projectInfo.value?.uuid)
                    .toResponse<LeaderClockEntity>().await()
            }
        }
    }


    /**
     * 获取当前项目的打卡地点
     */
    fun requestAddressAll(projectUuid: String?) {
        rxHttpRequest {
            onRequest = {
                addressManager.value = RxHttp.get(NetUrl.GET_ADDRESS_ALL)
                    .add("map_code", "gao_de")
                    .add("page", "1")
                    .add("size", Constant.PAGE_SIZE)
                    .add("project_uuid", if (TextUtils.isEmpty(projectUuid)) App.getAppViewModelInstance().userInfo.value?.project?.uuid else projectUuid)
                    .toResponse<AddressEntity>().await()
            }
            loadingType = LoadingType.LOADING_NULL
        }
    }


    /**
     * 查询当前地址和时间是否可打卡
     */
    fun requestCheckClockAddress(lnt: Double, lat: Double) {
        rxHttpRequest {
            onRequest = {
                check.value = RxHttp.get(NetUrl.CHECK_CLOCK_ADDRESS)
                    .add("lnt", "$lnt")
                    .add("lat", "$lat")
                    .add("map_code", "gao_de")
                    .toResponse<Any>().await()
            }
            loadingType = LoadingType.LOADING_NULL
        }
    }


    /**
     * 获取今日待办任务
     */
    fun getToDayTask(task_type: String, projectUuid: String?) {
        rxHttpRequest {
            onRequest = {
                todayTaskEntity.value = RxHttp.get(NetUrl.GET_TODAY_TASK)
                    .add("project_uuid", if (TextUtils.isEmpty(projectUuid)) App.getAppViewModelInstance().userInfo.value?.project?.uuid else projectUuid)
                    .add("task_type", "$task_type")
                    .toResponse<TodayTaskEntity>().await()
            }
        }
    }


    /**
     * 获取当前班次、地点
     */
    fun getClockRadius() {
        rxHttpRequest {
            onRequest = {
                projectOneEntity.value = RxHttp.get(NetUrl.GET_CLOCK_RADIUS)
                    .toResponse<ClockRadiusEntity>().await()
            }
        }
    }


    /**
     * 拉取服务器时间
     */
    fun requestTime() {
        rxHttpRequest {
            onRequest = {
                timestampEntity.value = RxHttp.get(NetUrl.GET_TIME_STAMP)
                    .connectTimeout(3000)
                    .writeTimeout(3000)
                    .readTimeout(3000)
                    .toResponse<TimestampEntity>().await()
            }
            onError = {
                errorEntity.value = true
            }
        }
    }

    /**
     * 上报参数
     */
    fun requestTimeUpload(hashMap: HashMap<String, String>) {
        rxHttpRequest {
            onRequest = {
                RxHttp.postForm("http://monitor.jiazhengye.cn/clean/errorlog")
                    .addAll(hashMap)
                    .toResponse<TimestampEntity>().await()
            }
            onError = {
                errorEntity.value = true
            }
            loadingType = LoadingType.LOADING_NULL
        }
    }


    /**
     * 获取用工规则的配置
     */
    fun requestWorkRules() {
        rxHttpRequest {
            onRequest = {
                workRulesEntity.value = RxHttp.get(NetUrl.GET_WORK_POST_RULES)
                    .toResponse<WorkRulesEntity>().await()
            }
        }
    }

    /**
     * 获取集体打卡的所有班次
     */
    fun requestTeamClassList(projectUuid: String?) {
        rxHttpRequest {
            onRequest = {
                classTeamEntity.value = RxHttp.get(NetUrl.GET_TEAM_CLASS_LIST)
                    .add("project_uuid", if (TextUtils.isEmpty(projectUuid)) App.getAppViewModelInstance().userInfo.value?.project?.uuid else projectUuid)
                    .toResponse<TeamClassEntity>().await()
            }
        }
    }


    /**
     * 获取集体打卡的用户
     */
    fun requestTeamClassUserList(class_uuid: String, page: Int, classEntity: TeamClassListEntity) {
        rxHttpRequest {
            onRequest = {
                val classTeamUserEntityResult = RxHttp.get(NetUrl.GET_TEAM_CLASS_USER_LIST)
                    .add("page", "$page")
                    .add("size", Constant.PAGE_SIZE)
                    .add("class_uuid", class_uuid)
                    .toResponse<TeamClassUserEntity>().await()
                //如果是可打卡，默认全部改成true
                if ("1" == classEntity.is_clock_in) {
                    classTeamUserEntityResult.list.forEach {
                        it.isSelected = true
                    }
                }
                if (page == 1) {
                    classEntity.classUserEntity = classTeamUserEntityResult
                } else {
                    classEntity.classUserEntity.list.addAll(classTeamUserEntityResult.list)
                }
                classTeamUserEntity.value = classEntity
            }
        }
    }


    /**
     * 逆地理编码
     */
    fun getRegeo(lat: Double, lnt: Double) {
        rxHttpRequest {
            onRequest = {
                regeo.value = RxHttp.get(NetUrl.GET_ADDRESS_REGEO)
                    .add("lnt", lnt)
                    .add("lat", lat)
                    .add("map_code", "gao_de")
                    .toResponse<RegeoEntity>().await()
            }
            loadingType = LoadingType.LOADING_NULL
        }
    }


    /**
     * 动态拉取高德地图服务的Key
     */
    fun requestAMapInfo() {
        rxHttpRequest {
            onRequest = {
                mapInfo.value = RxHttp
                    .get(NetUrl.GET_A_MAP_KEY)
                    .add("map_code", "gao_de")
                    .connectTimeout(3000)
                    .writeTimeout(3000)
                    .readTimeout(3000)
                    .toResponse<MapInfo>().await()
            }
            onError = {
                errorEntity.value = true
            }
        }
    }


    /**
     * 获取当前用户的相机的配置
     */
    fun getUserCameraOne() {
        rxHttpRequest {
            onRequest = {
                cameraConfigData.value = RxHttp
                    .get(NetUrl.GET_USER_ONE_CAMERA)
                    .toResponse<CameraConfigData>().await()
            }
        }
    }
}