package com.business_clean.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.business_clean.app.network.NetUrl
import com.business_clean.data.mode.format.FormatEntity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class FormatViewModel : BaseViewModel() {

    var formatEntity = MutableLiveData<FormatEntity>()

    /**
     * 获取全部列表
     */
    fun getFormatAll() {
        rxHttpRequest {
            onRequest = {
                formatEntity.value = RxHttp.get(NetUrl.GET_CAT_ALL)
                    .toResponse<FormatEntity>().await()
            }
        }
    }
}