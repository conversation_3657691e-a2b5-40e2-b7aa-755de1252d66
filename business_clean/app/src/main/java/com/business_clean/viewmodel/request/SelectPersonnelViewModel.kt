package com.business_clean.viewmodel.request

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import com.business_clean.app.App
import com.business_clean.app.ext.CommonUtils
import com.business_clean.app.network.NetUrl
import com.business_clean.data.mode.BaseDownLoadEntity
import com.business_clean.data.mode.project.ProjectManager
import com.business_clean.data.mode.roster.RosterEntity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import me.hgj.mvvmhelper.net.LoadingType
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class SelectPersonnelViewModel : BaseViewModel() {

    var staffManager = MutableLiveData<RosterEntity>()


    var downLoadEntity = MutableLiveData<BaseDownLoadEntity>()

    var allProjectManager = MutableLiveData<ProjectManager>()


    //获取档案列表
    fun getStaffList(
        keyword: String?,
        page: Int, status: Int, role_id: Int, is_head_office: Int, contract_start_date: String?, contract_end_date: String?,
        project_uuid: String? = App.getAppViewModelInstance().projectInfo.value?.uuid, isAll: Boolean, isRosterList: Boolean
    ) {
        rxHttpRequest {
            onRequest = {
                val hashMap = HashMap<String, String>()
                hashMap["keyword"] = "$keyword"
                hashMap["page"] = "$page"
                hashMap["size"] = "50"
                hashMap["status"] = "$status"//状态 0全部 1在职 2离职
                hashMap["role_id"] = if (role_id == 100) "" else "$role_id"//1管理员 2项目负责人 3人事 4领班 5保洁 6大区经理
                hashMap["is_head_office"] = "$is_head_office"//是否总部员工0全部 1是 2否
                if (!TextUtils.isEmpty(contract_start_date)) {
                    hashMap["contract_start_date"] = "$contract_start_date"//合约开始 -- 合约开始日期范围 上周  lastWeek 本周thisWeek 本月thisMonth 下月nextMonth 自定义 日期格式2023-08-21#2024-08-21
                }
                if (!TextUtils.isEmpty(contract_end_date)) {
                    hashMap["contract_end_date"] = "$contract_end_date"//合约结束
                }
                if (!isAll) {
                    if (is_head_office != 1) {
                        if (!TextUtils.isEmpty(project_uuid)) {
                            hashMap["project_uuid"] = "$project_uuid"
                        } else {
                            hashMap["project_uuid"] = "${App.getAppViewModelInstance().projectInfo.value?.uuid}"
                        }
                    }
                }
                //新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
                hashMap["is_head_office_project"] = if (CommonUtils.checkRoleHeadOffice()) "1" else "2"
                val url = if (isRosterList) NetUrl.GET_ROSTER_PERSON_LIST else NetUrl.GET_ROSTER_LIST
                staffManager.value = RxHttp.get(url)
                    .addAll(hashMap)
                    .toResponse<RosterEntity>().await()
            }
            loadingType = LoadingType.LOADING_NULL
        }
    }


    //获取休息人员列表
    fun requestCanOvertimeHolidayList(
        keyword: String?,
        page: Int, start_date: String?, end_date: String?,
    ) {
        rxHttpRequest {
            val hashMap = HashMap<String, String>()
            hashMap["keyword"] = "$keyword"
            hashMap["page"] = "$page"
            hashMap["size"] = "50"
            hashMap["start_date"] = "$start_date"
            hashMap["end_date"] = "$end_date"

            onRequest = {
                staffManager.value = RxHttp.get(NetUrl.GET_CAN_OVERTIME_HOLIDAY_LIST)
                    .addAll(hashMap)
                    .toResponse<RosterEntity>().await()
            }
            loadingType = LoadingType.LOADING_NULL
        }
    }



    /**
     * 获取用户的所有项目列表
     */
    fun getProjectMangerList(user_status: String) {
        rxHttpRequest {
            onRequest = {
                allProjectManager.value = RxHttp.get(NetUrl.GET_DEPART_GET_PROJECT_ALL)
                    .add("user_status", "$user_status")//user_status 1在职 2离职
                    .toResponse<ProjectManager>().await()
            }
        }
    }
}