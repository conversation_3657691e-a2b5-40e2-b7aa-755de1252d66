package com.business_clean.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.business_clean.app.App
import com.business_clean.app.config.Constant
import com.business_clean.app.network.NetUrl
import com.business_clean.data.mode.address.AddressEntity
import com.business_clean.data.mode.address.AddressListEntity
import com.business_clean.data.mode.contract.ContractShareInfoEntity
import com.business_clean.data.mode.members.IDCardMemberEntity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

class ContractViewModel : BaseViewModel() {

    var shareContractInfo = MutableLiveData<ContractShareInfoEntity>()

    //人员的快速离职
    var depart = MutableLiveData<Any>()

    var updateContractStatus = MutableLiveData<IDCardMemberEntity>()


    /**
     * 获取合同分享的地址
     */
    fun requestContractShareInfo(customer_id: String, contract_uuid: String) {
        rxHttpRequest {
            onRequest = {
                shareContractInfo.value = RxHttp.get(NetUrl.CREATE_CONTRACT_SIGN_INFO)
                    .add("customer_id", customer_id)
                    .add("contract_uuid", contract_uuid)
                    .toResponse<ContractShareInfoEntity>().await()
            }
        }
    }

    fun requestMembersDepart(hashMap: HashMap<String, String>) {
        rxHttpRequest {
            onRequest = {
                depart.value = RxHttp.get(NetUrl.DEPOART_MEMBERS)
                    .addAll(hashMap)
                    .toResponse<Any>().await()
            }
        }
    }

    /**
     * 编辑成员
     * edit 来区分是否是真的编辑 true 是已经存在的员工 false 是编辑的员工
     * 3 是档案想请，1、2都是草稿
     */
    fun requestUpdateMembers(hashMap: HashMap<String, String>, channel: String, type: Int) {
        rxHttpRequest {
            onRequest = {
                updateContractStatus.value = RxHttp.get(if (type == 3) NetUrl.UPDATE_MEMBERS_UPDATE else NetUrl.UPDATE_MEMBERS)
                    .addAll(hashMap)
                    .toResponse<IDCardMemberEntity>().await()
            }
        }
    }


}