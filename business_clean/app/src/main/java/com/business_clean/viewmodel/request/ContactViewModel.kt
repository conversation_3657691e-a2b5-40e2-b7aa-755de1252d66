package com.business_clean.viewmodel.request

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import com.business_clean.app.App
import com.business_clean.app.config.Constant.PAGE_SIZE
import com.business_clean.app.network.NetUrl
import com.business_clean.data.mode.contact.ContactListEntity
import com.business_clean.data.mode.contact.ContactMangerEntity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

/**
 * 常用联系人
 */
class ContactViewModel : BaseViewModel() {

    //全部联系人
    var contactAllManger = MutableLiveData<ContactMangerEntity>()

    var contactDetail = MutableLiveData<ContactListEntity>()

    var saveContactUpdate = MutableLiveData<Any>()

    var delContactUpdate = MutableLiveData<Any>()

    /**
     * 获取全部 常用联系人
     */
    fun getContactAll(page: Int, project_uuid: String? = null) {
        rxHttpRequest {
            onRequest = {
                contactAllManger.value = RxHttp.get(NetUrl.GET_CONTACTA_ALL)
                    .add("page", page)
                    .add("size", PAGE_SIZE)
                    .add("project_uuid", if (!TextUtils.isEmpty(project_uuid)) project_uuid else App.getAppViewModelInstance().userInfo.value?.project?.uuid)
                    .toResponse<ContactMangerEntity>().await()
            }
        }
    }

    /**
     * 添加常用联系人
     */
    fun saveContact(hashMap: HashMap<String, String>) {
        rxHttpRequest {
            onRequest = {
                saveContactUpdate.value = RxHttp.get(NetUrl.SAVE_CONTACTA)
                    .addAll(hashMap)
                    .toResponse<ContactMangerEntity>().await()
            }
        }
    }

    /**
     * 删除常用联系人
     */
    fun requestDelContact(hashMap: HashMap<String, String>) {
        rxHttpRequest {
            onRequest = {
                delContactUpdate.value = RxHttp.get(NetUrl.DEL_CONTACTC)
                    .addAll(hashMap)
                    .toResponse<ContactMangerEntity>().await()
            }
        }
    }

    /**
     * 常用联系人详情
     */
    fun getContactDetail(hashMap: HashMap<String, String>) {
        rxHttpRequest {
            onRequest = {
                contactDetail.value = RxHttp.get(NetUrl.DEL_CONTACTC)
                    .addAll(hashMap)
                    .toResponse<ContactListEntity>().await()
            }
        }
    }
}