package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.config.Constant;
import com.lxj.xpopup.core.BottomPopupView;
import com.otaliastudios.cameraview.controls.Flash;

public class CameraSettingDialog extends BottomPopupView {


    private CameraSettingDialogListener listener;

    private TextView tvCancel;

    private TextView ivFlash, ivChange, ivSaveAlbum, ivUser;

    private int flash;

    public CameraSettingDialog(@NonNull Context context, int flash, CameraSettingDialogListener listener) {
        super(context);
        this.flash = flash;
        this.listener = listener;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_camera_setting;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        ivFlash = findViewById(R.id.iv_dialog_camera_setting_flash);
        ivChange = findViewById(R.id.iv_dialog_camera_setting_change);
        ivSaveAlbum = findViewById(R.id.iv_dialog_camera_save_album);
//        ivSaveOriginal = findViewById(R.id.iv_dialog_camera_save_original);
        ivUser = findViewById(R.id.iv_dialog_camera_user);
        tvCancel = findViewById(R.id.tv_dialog_camera_cancel);

        switch (flash) {
            case 1:
                ivFlash.setText("切换闪光灯状态(始终开启)");
                break;
            case 2:
                ivFlash.setText("切换闪光灯状态(自动)");
                break;
            case 0:
                ivFlash.setText("切换闪光灯状态(未开启)");
                break;
        }

        ivSaveAlbum.setText("保存到手机相册(" + (!Constant.CAMERA_SAVE_PHOTO_VIDEO ? "开启" : "关闭") + ")");
//        ivSaveOriginal.setText("保存原始照片(" + (!Constant.CAMERA_SAVE_ORIGINAL_PHOTO ? "开启" : "关闭") + ")");

//        ivSaveOriginal.setVisibility(Constant.CAMERA_SAVE_PHOTO_VIDEO ? View.VISIBLE : View.GONE);

        if (App.getAppViewModelInstance().getUserInfo().getValue() != null && App.getAppViewModelInstance().getUserInfo().getValue().getUser() != null) {
            ivUser.setText("个人中心(" + App.getAppViewModelInstance().getUserInfo().getValue().getUser().getUser_name() + ")");
        } else {
            ivUser.setText("个人中心");
        }

        if (tvCancel != null) {
            tvCancel.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                }
            });
        }
        onBindViewClick();
    }

    private void onBindViewClick() {
        ivFlash.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.clickChange(1);
                dismiss();
            }
        });

        ivChange.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.clickChange(2);
                dismiss();
            }
        });

        ivSaveAlbum.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.clickChange(3);
                dismiss();
            }
        });

//        ivSaveOriginal.setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                listener.clickChange(4);
//                dismiss();
//            }
//        });


        ivUser.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.clickChange(5);
                dismiss();
            }
        });


    }


    public interface CameraSettingDialogListener {
        void clickChange(int how);
    }

}
