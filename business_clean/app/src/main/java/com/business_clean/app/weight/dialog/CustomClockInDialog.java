package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.os.CountDownTimer;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.TimeUtils;
import com.business_clean.R;
import com.lxj.xpopup.core.CenterPopupView;

import java.util.Date;

/**
 * 打卡成功要去显示的内容
 */
public class CustomClockInDialog extends CenterPopupView {

    private TextView tvHour;
    private TextView tvDate;

    private TextView tvConfirm;

    public CustomClockInDialog(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_clock_in;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        tvHour = findViewById(R.id.tv_clock_in_hour);
        tvDate = findViewById(R.id.tv_clock_in_desc);

        Date date = TimeUtils.getNowDate();

        tvHour.setText(TimeUtils.date2String(date, "HH:mm"));
        tvDate.setText(TimeUtils.date2String(date, "yyyy-MM-dd") + TimeUtils.getChineseWeek(date));


        tvConfirm = findViewById(R.id.tv_clock_in_confirm);
        tvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        //执行定时器
        startCountdown(4);
    }


    private void startCountdown(int seconds) {
        new CountDownTimer(seconds * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                int secondsLeft = (int) (millisUntilFinished / 1000);
                tvConfirm.setText("我知道了(" + secondsLeft + ")");
            }

            @Override
            public void onFinish() {
                dialog.dismiss(); // 关闭对话框
                dialog = null; // 销毁对象
            }
        }.start();
    }


}
