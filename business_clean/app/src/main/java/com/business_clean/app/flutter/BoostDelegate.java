package com.business_clean.app.flutter;

import android.app.Activity;
import android.content.Intent;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.App;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.util.MMKVHelper;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostDelegate;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.idlefish.flutterboost.containers.FlutterBoostActivity;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import io.flutter.embedding.android.FlutterActivityLaunchConfigs;

public class BoostDelegate implements FlutterBoostDelegate {

    public BoostDelegate() {
    }

    //这里是Flutter 跳转 原生
    @Override
    public void pushNativeRoute(FlutterBoostRouteOptions options) {
        Activity activity = FlutterBoost.instance().currentActivity();
        if (activity == null) {
            return;
        }
        Intent intent = new Intent();
        if (options.arguments() != null) {
            Set<String> keys = options.arguments().keySet();
            for (String key : keys) {
                intent.putExtra(key, (Serializable) options.arguments().get(key));
            }
        }
        //这里根据options.pageName来判断你想跳转哪个页面，这里简单给一个

        activity.startActivityForResult(intent, options.requestCode());
    }


    @Override
    public void pushFlutterRoute(FlutterBoostRouteOptions options) {//这里是原生跳转 Flutter
        Activity activity = FlutterBoost.instance().currentActivity();
        if (activity == null) {
            return;
        }
        LogUtils.e("跳转Flutter  - pushFlutterRoute- " + options.pageName());
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("serverType", FlutterManager.sServerType);
        arguments.put("company_uuid", MMKVHelper.getString(ConstantMMVK.COMPANY_UUID));
        arguments.put("company_name", MMKVHelper.getString(ConstantMMVK.COMPANY_NAME));
        arguments.put("xmjztoken", MMKVHelper.getString(ConstantMMVK.TOKEN));
        arguments.put("xmjzversion", AppUtils.getAppVersionName());
        arguments.put("xmjzdevice", DeviceUtils.getManufacturer() + DeviceUtils.getModel());
        arguments.put("role_id", Constant.ROLE_ID);//角色id
        ///注意如果有相同的名字，可能会冲突
        if (App.getAppViewModelInstance().getProjectInfo().getValue() != null) {
            arguments.put("current_project_uuid", App.getAppViewModelInstance().getProjectInfo().getValue().getUuid());//项目的 uuid
            arguments.put("current_project_name", App.getAppViewModelInstance().getProjectInfo().getValue().getProject_short_name());//项目的 uuid
        }
        ///注意如果有相同的名字，可能会冲突
        if (App.getAppViewModelInstance().getUserInfo().getValue() != null && App.getAppViewModelInstance().getUserInfo().getValue().getUser() != null) {
            arguments.put("current_user_uuid", App.getAppViewModelInstance().getUserInfo().getValue().getUser().getUuid());//当前用户的user_uuid
            arguments.put("current_user_name", App.getAppViewModelInstance().getUserInfo().getValue().getUser().getUser_name());//当前用户的user_name
        }

        arguments.put("is_entry_sign", MMKVHelper.getBoolean(ConstantMMVK.IS_ENTRY_SIGN, false));//入职的时候需要需要签名
        arguments.put("is_entry_before_today", MMKVHelper.getBoolean(ConstantMMVK.IS_ENTRY_BEFORE_TODAY, false));//是否不允许补入今日之前的入职信息
        arguments.put("head_office_mode", true);//是什么模式 默认改成true 因为以后没简单

        if (options.arguments() != null && options.arguments().size() > 0) {
            arguments.putAll(options.arguments());
        }

        Intent intent = new FlutterBoostActivity.CachedEngineIntentBuilder(XmFlutterBoostActivity.class)
                .backgroundMode(FlutterActivityLaunchConfigs.BackgroundMode.transparent)
                .destroyEngineWithActivity(false)
                .uniqueId(options.uniqueId())
                .url(options.pageName())
                .urlParams(arguments)
                .build(activity);
        activity.startActivity(intent);
    }
}
