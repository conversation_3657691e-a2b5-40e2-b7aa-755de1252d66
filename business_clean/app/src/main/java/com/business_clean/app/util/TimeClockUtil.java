package com.business_clean.app.util;

import android.content.Context;
import android.os.SystemClock;
import android.text.TextUtils;

import com.business_clean.app.App;
import com.business_clean.app.config.ConstantMMVK;

public class TimeClockUtil {

    /**
     * initialServerTime 是网络时间
     * initialBootTime 是启动的系统时间
     *
     * @param initialServerTime
     * @param initialBootTime
     * @return
     */
    public static long getCurrentAccurateTime(long initialServerTime, long initialBootTime) {
        // 应用启动以来经过的时间
        long elapsedSinceStart = initialServerTime + (getCurrentSystemUptimeInMillis() - initialBootTime);
        // 如果没有成功获取网络时间，则回退到系统时间
        return elapsedSinceStart;
    }

    public static long getCurrentSystemUptimeInMillis() {
        return SystemClock.elapsedRealtime();
    }
}
