package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.business_clean.app.callback.OnDialogAttendanceResultConfirmListener;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.GlideUtil;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.weight.RoundCornerImageView;
import com.business_clean.data.mode.ImageEntity;
import com.business_clean.data.mode.attendance.MyAttendanceDetailClockEntity;
import com.business_clean.ui.adapter.BaseStringAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.BottomPopupView;
import com.yalantis.ucrop.decoration.GridSpacingItemDecoration;
import com.zhixinhuixue.library.common.ext.DensityExtKt;

import java.util.ArrayList;
import java.util.List;

/**
 * 查看考勤详情
 */
public class CustomAttendanceLookOnePopup extends BottomPopupView {

    private TextView tvTime;//打卡时间
    private TextView tvSTime;//实际打卡时间
    private TextView tvClockType;//类型
    private TextView tvAction;//操作人

    private FrameLayout flImage;
    private RoundCornerImageView imageView;

    private ImageView ivPlay;

    private MyAttendanceDetailClockEntity data;

    private boolean isInWork;

    public CustomAttendanceLookOnePopup(@NonNull Context context, MyAttendanceDetailClockEntity data, boolean isInWork) {
        super(context);
        this.data = data;
        this.isInWork = isInWork;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_attendance_one_result;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        tvTime = findViewById(R.id.tv_dialog_attendance_time);
        tvSTime = findViewById(R.id.tv_dialog_attendance_s_time);
        tvClockType = findViewById(R.id.tv_dialog_attendance_clock_type);
        tvAction = findViewById(R.id.tv_dialog_attendance_action);
        flImage = findViewById(R.id.fl_dialog_in_my_attendance_desc);
        imageView = findViewById(R.id.iv_dialog_in_my_attendance_pic);
        ivPlay = findViewById(R.id.iv_dialog_in_my_attendance_play);


        //应打卡时间
        tvTime.setText(isInWork ? data.getIn_should_clock_time() : data.getOut_should_clock_time());
        //实际打卡时间
        tvSTime.setText(isInWork ? data.getIn_actual_clock_time() : data.getOut_actual_clock_time());
        //操作人
        tvAction.setText(isInWork ? data.getIn_operate_name() : data.getOut_operate_name());
        //打卡类型
        tvClockType.setText(isInWork ? data.getIn_clock_type_name() : data.getOut_clock_type_name());
        //图片，或者视频

        if (!TextUtils.isEmpty(isInWork ? data.getIn_media_url() : data.getOut_media_url())) {
            flImage.setVisibility(View.VISIBLE);
        } else {
            flImage.setVisibility(View.GONE);
        }

        if ("1".equals(data.getIn_media_type())) {//1 是图片 2是视频
            ivPlay.setVisibility(View.GONE);
            GlideUtil.loadPic(imageView.getContext(), isInWork ? data.getIn_pic_thumb() : data.getOut_pic_thumb(), imageView);
        } else {
            ivPlay.setVisibility(View.VISIBLE);
            GlideUtil.loadPic(imageView.getContext(), isInWork ? data.getIn_video_cover_url() : data.getOut_video_cover_url(), imageView);
        }

        flImage.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                showPreViewImage(data, isInWork);
            }
        });

        //关闭弹窗
        findViewById(R.id.dialog_choose_classes_close).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });


    }


    /**
     * 做转换 需要去公共图片查看的地方
     */
    private void showPreViewImage(MyAttendanceDetailClockEntity clock, boolean isInWork) {
        List<ImageEntity> images = new ArrayList<>();
        ImageEntity image = new ImageEntity();
        image.setResouceType(Integer.parseInt(isInWork ? clock.getIn_media_type() : clock.getOut_media_type()));//数据类型
        if ("2".equals(isInWork ? clock.getIn_media_type() : clock.getOut_media_type())) {
            image.setVideoUrl(isInWork ? clock.getIn_media_url() : clock.getOut_media_url());
            image.setVideoCoverUrl(isInWork ? clock.getIn_video_cover_url() : clock.getOut_video_cover_url());
        } else {
            image.setPhotoUrl(isInWork ? clock.getIn_media_url() : clock.getOut_media_url());
            image.setOriginalUrl(isInWork ? clock.getIn_origin_media_url() : clock.getOut_origin_media_url());
        }
        images.add(image);
        new XPopup.Builder(getContext())
                .asCustom(new CustomImageViewPagerPopup(getContext(), images, 0))
                .show();
    }

    @Override
    protected int getMaxHeight() {
        return (int) (DensityExtKt.getScreenHeight() / 2);
    }
}
