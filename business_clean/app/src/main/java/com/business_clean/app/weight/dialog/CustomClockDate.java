package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bigkoo.pickerview.adapter.ArrayWheelAdapter;
import com.bigkoo.pickerview.view.WheelTime;
import com.business_clean.R;
import com.business_clean.app.callback.OnDialogClockDateSelect;
import com.business_clean.data.mode.init.BaseIDNameEntity;
import com.contrarywind.listener.OnItemSelectedListener;
import com.contrarywind.view.WheelView;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.BottomPopupView;

import org.jetbrains.annotations.NotNull;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import me.hgj.mvvmhelper.ext.LogExtKt;


/**
 * 打卡
 */
public class CustomClockDate extends BottomPopupView {

    private String title = "请选择";

    private Context context;

    private WheelView wheelView1;
    private WheelView wheelView2;
    private WheelView wheelView3;

    private int selectedPosition1;
    private int selectedPosition3;
    //犯懒了，就这样写了

    private List<String> array1;
    private List<String> array3;

    private OnDialogClockDateSelect onDialogClockDateSelect;

    private String invertStart;
    private String invertEnd;


    public CustomClockDate(@NonNull @NotNull Context context, List<String> list1, List<String> list3, String start, String end, OnDialogClockDateSelect select) {
        super(context);
        this.context = context;
        this.array1 = list1;
        this.array3 = list3;
        this.invertStart = start;
        this.invertEnd = end;
        this.onDialogClockDateSelect = select;
    }


    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_clock_date;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        initView();
        initData();
        initClick();
        if (wheelView1 != null && !TextUtils.isEmpty(invertStart) && array1 != null) {
            wheelView1.setCurrentItem(array1.indexOf(invertStart));
            selectedPosition1 = array1.indexOf(invertStart);
        }
        if (wheelView3 != null && !TextUtils.isEmpty(invertEnd) && array3 != null) {
            wheelView3.setCurrentItem(array3.indexOf(invertEnd));
            selectedPosition3 = array3.indexOf(invertEnd);
        }
    }

    private void initView() {
        wheelView1 = findViewById(R.id.wheelView_clock_1);
        wheelView2 = findViewById(R.id.wheelView_clock_2);
        wheelView3 = findViewById(R.id.wheelView_clock_3);
        wheelView1.setCyclic(false);
        wheelView3.setCyclic(false);
        wheelView2.setCyclic(false);
    }


    private void initData() {


        List<String> array2 = new ArrayList<>();
        array2.add("至");

        // 设置WheelView的适配器和数据源
        wheelView1.setAdapter(new ArrayWheelAdapter(array1));
        wheelView2.setAdapter(new ArrayWheelAdapter(array2));
        wheelView3.setAdapter(new ArrayWheelAdapter(array3));
    }

    private void initClick() {
        // 设置WheelView的选择监听
        wheelView1.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition1 = index;
            }
        });

        wheelView3.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition3 = index;
            }
        });

        findViewById(R.id.tv_common_dialog_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        TextView tv_common_dialog_center = findViewById(R.id.tv_common_dialog_center);
        tv_common_dialog_center.setText(title);

        TextView btnConfirm = findViewById(R.id.tv_common_dialog_yes);
        btnConfirm.setTextColor(XPopup.getPrimaryColor());
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                LogExtKt.logE("选择的内容下表" + selectedPosition1 + " ; " + selectedPosition3, "");
                if (onDialogClockDateSelect != null) {
                    onDialogClockDateSelect.onSelected(selectedPosition1, selectedPosition3);
                }
            }
        });
    }
}
