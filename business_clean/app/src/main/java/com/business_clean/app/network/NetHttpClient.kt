package com.business_clean.app.network

import me.hgj.mvvmhelper.base.appContext
import me.hgj.mvvmhelper.net.interception.LogInterceptor
import okhttp3.OkHttpClient
import rxhttp.wrapper.cookie.CookieStore
import java.io.File
import java.util.concurrent.TimeUnit
import rxhttp.wrapper.ssl.HttpsUtils


object NetHttpClient {
    fun getDefaultOkHttpClient(): OkHttpClient.Builder {
        //在这里面可以写你想要的配置 太多了，我就简单的写了一点，具体可以看rxHttp的文档，有很多
        val sslParams = HttpsUtils.getSslSocketFactory()
        return OkHttpClient.Builder()
//            .cookieJar(CookieStore(File(appContext.externalCacheDir, "RxHttpCookie")))
            .connectTimeout(15, TimeUnit.SECONDS)//读取连接超时时间 15秒
            .readTimeout(15, TimeUnit.SECONDS)
            .writeTimeout(15, TimeUnit.SECONDS)
            .addInterceptor(MyHeadInterceptor())//自定义头部参数拦截器
//            .addInterceptor(TokenOutInterceptor())
            .addInterceptor(LogInterceptor())//添加Log拦截器
            .sslSocketFactory(sslParams.sSLSocketFactory, sslParams.trustManager) //添加信任证书
            .hostnameVerifier { hostname, session -> true } //忽略host验证
    }
}