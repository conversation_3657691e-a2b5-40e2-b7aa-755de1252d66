package com.business_clean.app.util.appupdate.update;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.text.TextUtils;

import androidx.core.content.ContextCompat;

import com.business_clean.R;
import com.business_clean.app.callback.OnDialogCancelListener;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.data.mode.appupdate.DialogAlertNoticeInfo;

import org.lzh.framework.updatepluginlib.base.InstallNotifier;

import java.lang.reflect.Field;


/**
 * 默认使用的下载完成后的通知创建器：创建一个弹窗提示用户已下载完成。可直接安装。
 *
 * <AUTHOR>
 */
public class MyInstallNotifier extends InstallNotifier {

    @Override
    public Dialog create(Activity activity) {

        DialogAlertNoticeInfo dialogAlertNoticeInfo = new DialogAlertNoticeInfo();
        dialogAlertNoticeInfo.setTitle(update.getVersionName() + "版本已经准备好了");
        dialogAlertNoticeInfo.setDescribe("升级一下新功能，也许能帮助到您");
        dialogAlertNoticeInfo.setContent(update.getUpdateContent());
        dialogAlertNoticeInfo.setSub_title_color(ContextCompat.getColor(activity, R.color.base_primary_warning));
        dialogAlertNoticeInfo.setConfirm_button("立即安装");
        dialogAlertNoticeInfo.setDismissOnBackPressed(false);
        dialogAlertNoticeInfo.setDismissOnTouchOutside(false);

        OnDialogCancelListener onDialogCancelListener = new OnDialogCancelListener() {
            @Override
            public void onCancel() {

            }
        };
        if (!update.isForced() && update.isIgnore()) {
            dialogAlertNoticeInfo.setClose_button("忽略此版本");
            onDialogCancelListener = new OnDialogCancelListener() {
                @Override
                public void onCancel() {
                    sendCheckIgnore();
                    MMKVHelper.putInt(ConstantMMVK.APP_UPDATE_VERSION_CODE, update.getVersionCode());
                }
            };
        }

        if (!update.isForced()) {
            dialogAlertNoticeInfo.setClose_button("取消");
            onDialogCancelListener = new OnDialogCancelListener() {
                @Override
                public void onCancel() {
                    MMKVHelper.putInt(ConstantMMVK.APP_UPDATE_VERSION_CODE, update.getVersionCode());
                    sendUserCancel();
                }
            };
        }

        OnDialogConfirmListener onDialogConfirmListener = new OnDialogConfirmListener() {
            @Override
            public void onConfirm() {
//                if (update.isForced()) {
//                    preventDismissDialog(dialog);
//                } else {
//                    SafeDialogHandle.safeDismissDialog((Dialog) dialog);
//                }
                sendToInstall();
            }
        };
        CommonUtils.showSystemAlertPop(activity, dialogAlertNoticeInfo, onDialogCancelListener, onDialogConfirmListener);
        return null;
    }

    /**
     * 通过反射 阻止自动关闭对话框
     */
    private void preventDismissDialog(DialogInterface dialog) {
        try {
            Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
            field.setAccessible(true);
            //设置mShowing值，欺骗android系统
            field.set(dialog, false);
        } catch (Exception e) {
            // ignore
        }
    }

}
