package com.business_clean.app.base;

import static me.hgj.mvvmhelper.ext.ViewBindUtilKt.inflateBinding;

import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.ConsoleMessage;
import android.webkit.ValueCallback;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.viewbinding.ViewBinding;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.R;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.config.OnJsBridge;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.weight.CustomTabBar;
import com.gyf.immersionbar.ImmersionBar;
import com.just.agentweb.AgentWeb;
import com.just.agentweb.AgentWebConfig;
import com.just.agentweb.DefaultWebClient;
import com.just.agentweb.WebChromeClient;
import com.just.agentweb.WebViewClient;

import me.hgj.mvvmhelper.base.BaseVBActivity;
import me.hgj.mvvmhelper.base.BaseViewModel;
import me.hgj.mvvmhelper.ext.LogExtKt;

public abstract class BaseAgentWebActivity<VM extends BaseViewModel, DB extends ViewBinding> extends BaseVBActivity<VM, DB> {

    private String TAG = "BaseAgentWebActivity";
    protected AgentWeb.PreAgentWeb mAgentWeb;
    protected CustomTabBar mToolbar;

    protected boolean isOnResumeReload = false;
    protected boolean isCustomTitle = true;//是否需要自定义Title

    private boolean isClose = false; //是否立即关闭该界面


    public void setCustomTitle(boolean customTitle) {
        isCustomTitle = customTitle;
    }

    public void setOnResumeReload(boolean onResumeReload) {
        isOnResumeReload = onResumeReload;
    }


    public void setClose(boolean close) {
        isClose = close;
    }

    @Nullable
    @Override
    public View getTitleBarView() {
        View titleBarView = LayoutInflater.from(this).inflate(R.layout.layou_titilebar_view, null);
        mToolbar = titleBarView.findViewById(R.id.custom_tabbar);
        mToolbar.getBackButton().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mAgentWeb != null && mAgentWeb.get().getWebCreator().getWebView().canGoBack() && !isClose) {
                    mAgentWeb.get().back();
                } else {
                    finish();
                }
            }
        });
        return titleBarView;
    }

    @Override
    protected void initImmersionBar() {
        if (showToolBar()) {
            ImmersionBar.with(this)
                    .statusBarColor(R.color.white)
                    .navigationBarColor(R.color.white)
                    .titleBar(mToolbar)
                    .statusBarDarkFont(true) //状态栏字体是深色，不写默认为亮色
                    .init();
        }
    }


    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        initAgentWeb();
        LogUtils.e("来到BaseWeb 这里了 initView");
    }

    @Override
    public void initObserver() {
        LogUtils.e("来到BaseWeb 这里了 initObserver");
    }

    @Override
    public void onRequestSuccess() {
        LogUtils.e("来到BaseWeb 这里了 onRequestSuccess");
    }

    @Override
    public void initAgentWeb() {
        super.initAgentWeb();

        //获取apptoken
        String token = MMKVHelper.getString(ConstantMMVK.TOKEN);

        if (!TextUtils.isEmpty(token) && !TextUtils.isEmpty(getUrl())) {
            //设置cookie之前先清楚之前的 设置 重新
            AgentWebConfig.removeAllCookies();
            AgentWebConfig.syncCookie(getUrl(), "xmjz_token=" + token);
            String tagCookie = AgentWebConfig.getCookiesByUrl(getUrl());//获取当前设置的cookie
            LogUtils.e("AgentWeb Token - " + tagCookie);
        }
        LogUtils.e("AgentWeb url - " + getUrl());
        // 初始化AgentWeb
        mAgentWeb = AgentWeb.with(this)
                .setAgentWebParent(getAgentWebParent(), new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))
                .useDefaultIndicator()
                .setWebViewClient(mWebViewClient)
                .setWebChromeClient(mWebChromeClient)
                .additionalHttpHeader(getUrl(), "xmjztoken", token)
                .additionalHttpHeader(getUrl(), "xmjzplatform", "android")
                .additionalHttpHeader(getUrl(), "xmjzversion", AppUtils.getAppVersionName())
                .setMainFrameErrorView(R.layout.agentweb_error_page, -1)//参数1是错误显示的布局，参数2点击刷新控件ID -1表示点击整个布局都刷新， AgentWeb 3.0.0 加入。
                .setSecurityType(AgentWeb.SecurityType.STRICT_CHECK)     //严格模式 Android 4.2.2 以下会放弃注入对象 ，使用AgentWebView没影响。
                .setOpenOtherPageWays(DefaultWebClient.OpenOtherPageWays.ASK)//打开其他应用时，弹窗咨询用户是否前往其他应用
                .interceptUnkownUrl()
                .createAgentWeb()
                .ready();
        //设置交互
        mAgentWeb.get().getJsInterfaceHolder().addJavaObject(Constant.JS_SPACE_NAME, getOnJsBridge());
        //设置userAgent
        WebSettings webSettings = mAgentWeb.get().getAgentWebSettings().getWebSettings();
        webSettings.setUserAgentString(getWebViewUserAgent(webSettings));
        webSettings.setAllowContentAccess(true);
        webSettings.setDatabaseEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setSaveFormData(false);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            webSettings.setLoadsImagesAutomatically(true);
        } else {
            webSettings.setLoadsImagesAutomatically(false);
        }
        //跳转
        mAgentWeb.go(getUrl());
        //解决跟flutter h5 生命周期的问题
        mAgentWeb.get().getWebLifeCycle().onResume();
    }

    /**
     * 设置Agent的UserAgent
     *
     * @param webSettings
     * @return
     */
    private String getWebViewUserAgent(WebSettings webSettings) {
        String userAgent = "XmjzCapp/" + AppUtils.getAppVersionName() + " " + webSettings.getUserAgentString();
        LogUtils.e("UserAgent - " + userAgent);
        return userAgent;
    }


    /**
     * 交互回调 ，继承者实现
     *
     * @return
     */
    protected OnJsBridge getOnJsBridge() {
        return new OnJsBridge(mAgentWeb.get());
    }

    private WebViewClient mWebViewClient = new WebViewClient() {
        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
        }

        @Override
        public void onLoadResource(WebView view, String url) {
            super.onLoadResource(view, url);
        }

        //拦截初次加载的黑名单url
        @Override
        public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
            LogExtKt.logE("shouldInterceptRequest ", TAG);
            return super.shouldInterceptRequest(view, url);
        }

        //拦截初次加载的黑名单url
        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        @Override
        public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
            LogExtKt.logE("shouldInterceptRequest ", TAG);
            return shouldInterceptRequest(view, request.getUrl().toString());
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            LogExtKt.logE("shouldOverrideUrlLoading ", TAG);
            return super.shouldOverrideUrlLoading(view, url);
        }

        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            LogUtils.e("shouldOverrideUrlLoading ", TAG);
            return super.shouldOverrideUrlLoading(view, request);
        }

    };

    private WebChromeClient mWebChromeClient = new WebChromeClient() {
        final View[] customView = {null};

        @Override
        public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> valueCallback, FileChooserParams fileChooserParams) {
            LogExtKt.logE("onShowFileChooser 打开相册", TAG);
            return super.onShowFileChooser(webView, valueCallback, fileChooserParams);
        }

        @Override
        public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
            // 打印H5日志到Android端的Logcat
            LogExtKt.logE("H5_LOG -> " + consoleMessage.message(), TAG);
            return true;
        }

        @Override
        public void onReceivedTitle(WebView view, String title) {
            super.onReceivedTitle(view, title);
            LogExtKt.logE("H5的 title -> " + title, TAG);
            if (mToolbar != null && !title.contains("/") && isCustomTitle) {
                mToolbar.setTitle(TextUtils.isEmpty(getBarTitle()) ? ("http".contains(title) ? "" : title) : getBarTitle());
            }
        }

        @Override
        public void onShowCustomView(View view, CustomViewCallback callback) {
            customView[0] = view;
        }

        @Override
        public void onHideCustomView() {
            super.onHideCustomView();
        }
    };


    @Override
    public void onPause() {
        if (mAgentWeb != null) {
            mAgentWeb.get().getWebLifeCycle().onPause();
        }
        super.onPause();
    }

    @Override
    public void onResume() {
        if (mAgentWeb != null) {
            mAgentWeb.get().getWebLifeCycle().onResume();
        }
        if (mAgentWeb != null && isOnResumeReload) {
            mAgentWeb.get().getUrlLoader().reload();
        }
        super.onResume();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 销毁AgentWeb
        if (mAgentWeb != null) {
            mAgentWeb.get().destroy();
        }
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        if (mAgentWeb != null && isOnResumeReload) {
            mAgentWeb.get().getUrlLoader().reload();
        }
    }

    protected abstract ViewGroup getAgentWebParent();

    protected abstract String getUrl();


    public String getBarTitle() {
        return "";
    }

}
