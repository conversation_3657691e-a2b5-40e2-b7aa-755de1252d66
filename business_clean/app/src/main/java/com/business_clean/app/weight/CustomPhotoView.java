package com.business_clean.app.weight;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.FutureTarget;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.transition.Transition;
import com.business_clean.R;
import com.lxj.xpopup.photoview.PhotoView;
import com.lxj.xpopup.util.ImageDownloadTarget;
import com.lxj.xpopup.util.XPopupUtils;

import java.io.File;

public class CustomPhotoView extends LinearLayout {

    private ImageView photoView;

    private ProgressBar progressBar;

    public CustomPhotoView(Context context) {
        super(context);
        init(context);
    }

    public CustomPhotoView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public CustomPhotoView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.layout_custom_photo, this, true);
        photoView = findViewById(R.id.photo);
        progressBar = findViewById(R.id.progress_custom_photo);
    }

    public void loadImage(String bitmapUrl) {
        progressBar.setVisibility(View.VISIBLE);
        Glide.with(photoView)
                .downloadOnly()
                .load(bitmapUrl)
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .into(new ImageDownloadTarget() {
                    @Override
                    public void onLoadFailed(Drawable errorDrawable) {
                        super.onLoadFailed(errorDrawable);
                        progressBar.setVisibility(View.GONE);
//                        ToastUtil.show("下载失败");
                    }

                    @Override
                    public void onResourceReady(@NonNull File resource, Transition<? super File> transition) {
                        super.onResourceReady(resource, transition);
                        progressBar.setVisibility(View.GONE);
                        int degree = XPopupUtils.getRotateDegree(resource.getAbsolutePath());
                        int maxW = XPopupUtils.getAppWidth(photoView.getContext());
                        int maxH = XPopupUtils.getScreenHeight(photoView.getContext());
                        int[] size = XPopupUtils.getImageSize(resource);
                        if (size[0] > maxW || size[1] > maxH) {
                            //缩放加载
                            Bitmap rawBmp = XPopupUtils.getBitmap(resource, maxW, maxH);
                            photoView.setImageBitmap(XPopupUtils.rotate(rawBmp, degree, size[0] / 2f, size[1] / 2f));
                        } else {
                            Glide.with(photoView).load(resource).apply(new RequestOptions().override(size[0], size[1])).into(photoView);
                        }
                    }
                });
    }


    public ImageView getPhotoView() {
        return photoView;
    }

    public Bitmap getDownloadedBitmap(Context context, String imageUrl) {
        try {
            FutureTarget<Bitmap> futureTarget = Glide.with(context)
                    .asBitmap()
                    .load(imageUrl)
                    .diskCacheStrategy(DiskCacheStrategy.NONE) // 不使用磁盘缓存
                    .skipMemoryCache(true) // 不使用内存缓存
                    .submit(); // 提交请求

            return futureTarget.get(); // 获取已下载的Bitmap
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public File getImageFile(@NonNull Context context, @NonNull Object uri) {
        try {
            return Glide.with(context).downloadOnly().load(uri).submit().get();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
