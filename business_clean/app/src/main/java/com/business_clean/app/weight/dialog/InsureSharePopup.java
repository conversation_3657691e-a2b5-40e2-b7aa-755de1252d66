package com.business_clean.app.weight.dialog;

import android.app.Activity;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;

import com.business_clean.R;
import com.business_clean.app.util.share.ShareHelperTools;
import com.business_clean.app.util.share.ShareParams;
import com.business_clean.app.util.share.ShareType;
import com.just.agentweb.AgentWeb;
import com.lxj.xpopup.core.BottomPopupView;
import com.zhixinhuixue.library.common.ext.DensityExtKt;


/**
 * 合同分享签署的合同
 */
public class InsureSharePopup extends BottomPopupView {

    private Activity mActivity;

    private String url;
    private String title;

    protected AgentWeb.PreAgentWeb mAgentWeb;

    private SharePopupListener listener;

    public InsureSharePopup(@NonNull Activity activity, String url, String title, SharePopupListener listener) {
        super(activity);
        this.mActivity = activity;
        this.url = url;
        this.title = title;
        this.listener = listener;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_insure_share;
    }

    @Override
    protected void onCreate() {
        super.onCreate();

        mAgentWeb = AgentWeb.with(mActivity)
                .setAgentWebParent(findViewById(R.id.ll_dialog_layout), new LinearLayout.LayoutParams(-1, -1))
                .useDefaultIndicator()
                .createAgentWeb()
                .ready();

        //开始url
        mAgentWeb.go(url);
        mAgentWeb.get().getWebLifeCycle().onResume();

        findViewById(R.id.iv_dialog_insure_close).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                mAgentWeb.get().getWebLifeCycle().onDestroy();
                dismiss();
                if (listener != null) {
                    listener.onXpopupDismiss();
                }
            }
        });

        findViewById(R.id.tv_dialog_insure_close).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                mAgentWeb.get().getWebLifeCycle().onDestroy();
                if (listener != null) {
                    listener.onXpopupDismiss();
                }
            }
        });


        //分享保险信息
        findViewById(R.id.tv_dialog_insure_share).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                mAgentWeb.get().getWebLifeCycle().onDestroy();
                ShareParams params = new ShareParams();
                params.setShareType(ShareType.WEIXIN);
                params.setTitle(title);
                params.setLinkUrl(url);
                ShareHelperTools.getInstance().shareCardLink(params, mActivity);
                dismiss();
            }
        });
    }


    @Override
    protected void onShow() {
        super.onShow();
        Log.e("tag", "PagerDrawerPopup onShow");
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        if (mAgentWeb != null) {
            mAgentWeb.get().getWebLifeCycle().onDestroy();
        }
        Log.e("tag", "PagerDrawerPopup onDismiss");
    }

    @Override
    protected int getMaxHeight() {
        return (int) (DensityExtKt.getScreenHeight() / 1.2);
    }

    public interface SharePopupListener {
        void onXpopupDismiss();
    }
}

