package com.business_clean.app.weight.dialog;

import android.app.Activity;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.business_clean.R;
import com.business_clean.app.config.Constant;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.share.ShareHelperTools;
import com.business_clean.app.util.share.ShareParams;
import com.business_clean.app.util.share.ShareType;
import com.just.agentweb.AgentWeb;
import com.lxj.xpopup.core.BottomPopupView;
import com.lxj.xpopup.core.CenterPopupView;
import com.lxj.xpopup.interfaces.OnConfirmListener;
import com.zhixinhuixue.library.common.ext.DensityExtKt;

import java.io.IOException;
import java.io.InputStream;


/**
 * 合同分享签署的合同
 */
public class UserPrivacyPolicyPopup extends BottomPopupView {

    private Activity mActivity;

    private OnConfirmListener listener;

    private TextView tvOk;
    private TextView tvNo;
    private TextView tvService;

    private TextView tvContent;


    public UserPrivacyPolicyPopup(@NonNull Activity activity, OnConfirmListener listener) {
        super(activity);
        this.mActivity = activity;
        this.listener = listener;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_user_privacy_policy_share;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        tvService = findViewById(R.id.dialog_tv_privacy_web);
        tvContent = findViewById(R.id.dialog_tv_privacy_content);
        tvOk = findViewById(R.id.dialog_tv_privacy_ok);
        tvNo = findViewById(R.id.dialog_tv_privacy_no);

        setRawTxt();

        tvOk.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onConfirm();
                }
            }
        });

        tvNo.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                System.exit(0);
            }
        });

        // 创建原始字符串
        String fullText = "查看完整版 服务协议 和 隐私协议";
        SpannableString spannableString = new SpannableString(fullText);

        // 定义颜色资源
        int clickableColor = ContextCompat.getColor(mActivity, R.color.base_primary); // 假设你有一个绿色的颜色资源
        int normalColor = ContextCompat.getColor(mActivity, R.color.base_primary_text_title);

        // 设置 "服务协议" 的样式
        spannableString.setSpan(new ClickableSpan() {
                                    @Override
                                    public void onClick(View widget) {
                                        CommonUtils.gotoLinkInBrowser(Constant.SERVICE_AGREEMENT);
                                    }

                                    @Override
                                    public void updateDrawState(TextPaint ds) {
                                        super.updateDrawState(ds);
                                        ds.setColor(clickableColor);
                                        ds.setUnderlineText(false); // 可选：移除下划线
                                    }
                                },
                fullText.indexOf("服务协议"),
                fullText.indexOf("服务协议") + "服务协议".length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // 设置 "隐私协议" 的样式
        spannableString.setSpan(new ClickableSpan() {
                                    @Override
                                    public void onClick(View widget) {
                                        CommonUtils.gotoLinkInBrowser(Constant.PRIVACY_POLICY);
                                    }

                                    @Override
                                    public void updateDrawState(TextPaint ds) {
                                        super.updateDrawState(ds);
                                        ds.setColor(clickableColor);
                                        ds.setUnderlineText(false); // 可选：移除下划线
                                    }
                                },
                fullText.indexOf("隐私协议"),
                fullText.indexOf("隐私协议") + "隐私协议".length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // 设置 TextView 的文本为 SpannableString
        tvService.setText(spannableString);

        // 设置 MovementMethod 以使点击事件生效
        tvService.setMovementMethod(LinkMovementMethod.getInstance());

        // 设置默认文本颜色（非点击部分）
        tvService.setTextColor(normalColor);
    }

    private void setRawTxt() {
        String privacyPolicyText = readRawResource(R.raw.privacy_policy);
        if (!TextUtils.isEmpty(privacyPolicyText)) {
            tvContent.setText(privacyPolicyText);
        }
    }

    private String readRawResource(int resourceId) {
        InputStream inputStream = getResources().openRawResource(resourceId);
        StringBuilder stringBuilder = new StringBuilder();
        try {
            int size = inputStream.available();
            byte[] buffer = new byte[size];
            inputStream.read(buffer);
            inputStream.close();
            stringBuilder.append(new String(buffer));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return stringBuilder.toString();
    }


    @Override
    protected int getMaxHeight() {
        return (int) (DensityExtKt.getScreenHeight() / 1.2);
    }



    @Override
    protected void onShow() {
        super.onShow();
        Log.e("tag", "PagerDrawerPopup onShow");
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        Log.e("tag", "PagerDrawerPopup onDismiss");
    }


}

