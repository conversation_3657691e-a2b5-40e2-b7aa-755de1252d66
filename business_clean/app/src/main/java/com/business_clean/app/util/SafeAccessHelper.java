package com.business_clean.app.util;

import com.business_clean.app.App;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.chad.library.adapter.base.BaseQuickAdapter;

import java.util.List;

/**
 * 安全访问工具类
 * 用于减少重复的空值检查代码
 */
public class SafeAccessHelper {
    
    /**
     * 安全获取当前用户项目UUID
     * @return 项目UUID，如果获取失败返回空字符串
     */
    public static String getCurrentProjectUuid() {
        try {
            if (App.getAppViewModelInstance() != null && 
                App.getAppViewModelInstance().getUserInfo() != null &&
                App.getAppViewModelInstance().getUserInfo().getValue() != null &&
                App.getAppViewModelInstance().getUserInfo().getValue().getProject() != null &&
                App.getAppViewModelInstance().getUserInfo().getValue().getProject().getUuid() != null) {
                return App.getAppViewModelInstance().getUserInfo().getValue().getProject().getUuid();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
    
    /**
     * 安全设置用户项目信息
     * @param project 要设置的项目
     * @return 是否设置成功
     */
    public static boolean safeSetUserProject(ProjectMangerList project) {
        try {
            if (project != null && 
                App.getAppViewModelInstance() != null && 
                App.getAppViewModelInstance().getUserInfo() != null &&
                App.getAppViewModelInstance().getUserInfo().getValue() != null) {
                App.getAppViewModelInstance().getUserInfo().getValue().setProject(project);
                App.getAppViewModelInstance().getProjectInfo().setValue(project);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
    
    /**
     * 安全获取适配器中指定位置的项目数据
     * @param adapter 适配器
     * @param position 位置
     * @return 项目数据，如果获取失败返回null
     */
    public static ProjectMangerList safeGetAdapterItem(BaseQuickAdapter<ProjectMangerList, ?> adapter, int position) {
        try {
            if (adapter != null && adapter.getData() != null && 
                position >= 0 && position < adapter.getData().size()) {
                return adapter.getData().get(position);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * 检查项目数据是否有效
     * @param project 项目数据
     * @return 是否有效
     */
    public static boolean isProjectValid(ProjectMangerList project) {
        return project != null && 
               project.getUuid() != null && 
               project.getProject_short_name() != null;
    }
    
    /**
     * 安全执行操作，捕获所有异常
     * @param operation 要执行的操作
     */
    public static void safeExecute(Runnable operation) {
        try {
            if (operation != null) {
                operation.run();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 安全执行操作，捕获所有异常，并在异常时执行fallback
     * @param operation 要执行的操作
     * @param fallback 异常时的fallback操作
     */
    public static void safeExecuteWithFallback(Runnable operation, Runnable fallback) {
        try {
            if (operation != null) {
                operation.run();
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (fallback != null) {
                try {
                    fallback.run();
                } catch (Exception fallbackException) {
                    fallbackException.printStackTrace();
                }
            }
        }
    }
    
    /**
     * 安全获取字符串，为null时返回默认值
     * @param value 原始值
     * @param defaultValue 默认值
     * @return 安全的字符串值
     */
    public static String safeString(String value, String defaultValue) {
        return value != null ? value : defaultValue;
    }
    
    /**
     * 安全获取字符串，为null时返回空字符串
     * @param value 原始值
     * @return 安全的字符串值
     */
    public static String safeString(String value) {
        return safeString(value, "");
    }
    
    /**
     * 检查适配器数据是否有效
     * @param adapter 适配器
     * @param position 位置
     * @return 是否有效
     */
    public static boolean isAdapterDataValid(BaseQuickAdapter<?, ?> adapter, int position) {
        try {
            return adapter != null && 
                   adapter.getData() != null && 
                   position >= 0 && 
                   position < adapter.getData().size();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 安全获取列表大小
     * @param list 列表
     * @return 列表大小，如果列表为null返回0
     */
    public static int safeListSize(List<?> list) {
        try {
            return list != null ? list.size() : 0;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}
