package com.business_clean.app.weight.dialog

import android.content.Context
import android.view.Gravity
import android.widget.TextView
import com.bigkoo.pickerview.adapter.NumericWheelAdapter
import com.blankj.utilcode.util.LogUtils
import com.business_clean.R
import com.business_clean.app.callback.OnDialogCreateFinish
import com.business_clean.app.callback.OnDialogDoubleDateConfirmListener
import com.business_clean.app.callback.OnDialogISelectTimeCallback
import com.contrarywind.listener.OnItemSelectedListener
import com.contrarywind.view.WheelView
import com.contrarywind.view.WheelView.DividerType
import com.lxj.xpopup.core.BottomPopupView
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

class CustomDoubleTimePickerPopup(context: Context) : BottomPopupView(context) {

    private var wheelFirstYear: WheelView? = null
    private var wheelFirstMonth: WheelView? = null
    private var wheelFirstDay: WheelView? = null

    private var wheelSecondYear: WheelView? = null
    private var wheelSecondMonth: WheelView? = null
    private var wheelSecondDay: WheelView? = null

    private val DEFAULT_START_YEAR = 1900
    private val DEFAULT_END_YEAR = 2100
    private val DEFAULT_START_MONTH = 1
    private val DEFAULT_END_MONTH = 12
    private val DEFAULT_START_DAY = 1
    private val DEFAULT_END_DAY = 31

    private var startYear = DEFAULT_START_YEAR
    private var endYear = DEFAULT_END_YEAR
    private var startMonth = DEFAULT_START_MONTH
    private var endMonth = DEFAULT_END_MONTH
    private var startDay = DEFAULT_START_DAY
    private var endDay = DEFAULT_END_DAY //表示31天的

    private var mFirstCurrentYear = 0 //当前选中的天
    private var mFirstCurrentMonth = 0 //当前选中的月
    private var mFirstCurrentDay = 0//当前选中的天

    private var mSecondCurrentYear = 0 //当前选中的天
    private var mSecondCurrentMonth = 0 //当前选中的月
    private var mSecondCurrentDay = 0//当前选中的天

    private var gravity = Gravity.CENTER

    private var textSize: Float = 15f // 字体大小

    private var tvCancel: TextView? = null
    private var tvConfirm: TextView? = null
    private var tvTitle: TextView? = null

    private var mSelectChangeCallback: OnDialogISelectTimeCallback? = null
    private var onDialogConfirmListener: OnDialogDoubleDateConfirmListener? = null
    private var onDialogCreateFinish: OnDialogCreateFinish? = null


    override fun getImplLayoutId() = R.layout.common_popup_double_time_picker

    override fun onCreate() {
        super.onCreate()
        tvCancel = findViewById(R.id.tv_common_dialog_cancel)
        tvTitle = findViewById(R.id.tv_common_dialog_center)
        tvConfirm = findViewById(R.id.tv_common_dialog_yes)


        wheelFirstYear = findViewById(R.id.common_first_year)
        wheelFirstMonth = findViewById(R.id.common_first_month)
        wheelFirstDay = findViewById(R.id.common_first_day)

        wheelSecondYear = findViewById(R.id.common_second_year)
        wheelSecondMonth = findViewById(R.id.common_second_month)
        wheelSecondDay = findViewById(R.id.common_second_day)

        wheelFirstYear?.setLabel("年")
        wheelFirstMonth?.setLabel("月")
        wheelFirstDay?.setLabel("日")

        wheelSecondYear?.setLabel("年")
        wheelSecondMonth?.setLabel("月")
        wheelSecondDay?.setLabel("日")

        wheelFirstYear?.setCyclic(false)
        wheelFirstMonth?.setCyclic(false)
        wheelFirstDay?.setCyclic(false)

        wheelSecondYear?.setCyclic(false)
        wheelSecondMonth?.setCyclic(false)
        wheelSecondDay?.setCyclic(false)

        onBindViewClick()
        onDialogCreateFinish?.onViewInit()

    }

    /**
     * 设置Wheel的全部属性
     */
    fun initWheelDate() {
        setSolar(wheelFirstYear, wheelFirstMonth, wheelFirstDay, mFirstCurrentYear, mFirstCurrentMonth, mFirstCurrentDay, true)
        setSolar(wheelSecondYear, wheelSecondMonth, wheelSecondDay, mSecondCurrentYear, mSecondCurrentMonth, mSecondCurrentDay, false)
    }

    private fun onBindViewClick() {
        tvCancel?.setOnClickListener {
            dialog?.dismiss()
        }
        tvConfirm?.setOnClickListener {
            val firstTime = getFirstTime();
            val secondTime = getSecondTime();
            LogUtils.i("----compare_date_format-----");
            if (compare_date_format(firstTime, secondTime) > 0) {
                onDialogConfirmListener?.onConfirm(secondTime, firstTime)
            } else {
                onDialogConfirmListener?.onConfirm(firstTime, secondTime)
            }
            dialog?.dismiss()
        }
    }

    /**
     * 两个日期比较
     *
     * @param DATE1
     * @param DATE2
     * @return
     */
    fun compare_date_format(DATE1: String?, DATE2: String?): Int {
        val df: DateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA)
        try {
            val dt1 = df.parse(DATE1)
            val dt2 = df.parse(DATE2)
            return if (dt1.time - dt2.time > 0) { //date1>date2
                1
            } else {
                -1
            }
        } catch (exception: Exception) {
            LogUtils.i("====exception=======$exception")
            exception.printStackTrace()
        }
        return 0
    }


    fun setListener(onDialogCreateFinish: OnDialogCreateFinish, onDialogConfirmListener: OnDialogDoubleDateConfirmListener) {
        this.onDialogCreateFinish = onDialogCreateFinish
        this.onDialogConfirmListener = onDialogConfirmListener
    }

    /**
     * 设置文字
     */
    fun setCancelText(content: String) {
        tvCancel?.text = content
    }

    /**
     * 设置文字
     */
    fun setConfirmText(content: String) {
        tvConfirm?.text = content
    }

    /**
     * 设置文字
     */
    fun setCenterText(content: String) {
        tvTitle?.text = content
    }

    /**
     * 设置日期文字大小
     */
    fun setDateTextSize(size: Float) {
        this.textSize = size
    }

    /**
     * 设置开始时间跟结束时间
     */
    fun setDateRange(startYear: Int, endYear: Int, inverseYear: Int = 0, inverseMonth: Int = 0, inverseDay: Int = 0) {
        this.startYear = startYear
        this.endYear = endYear
        // 默认设置当前日期
        this.mFirstCurrentYear = if (inverseYear != 0) inverseYear else Calendar.getInstance()[Calendar.YEAR]
        this.mFirstCurrentMonth = if (inverseMonth != 0) inverseMonth else Calendar.getInstance()[Calendar.MONTH] + 1
        this.mFirstCurrentDay = if (inverseDay != 0) inverseDay else Calendar.getInstance()[Calendar.DAY_OF_MONTH]
        this.mSecondCurrentYear = if (inverseYear != 0) inverseYear else Calendar.getInstance()[Calendar.YEAR]
        this.mSecondCurrentMonth = if (inverseMonth != 0) inverseMonth else Calendar.getInstance()[Calendar.MONTH] + 1
        this.mSecondCurrentDay = if (inverseDay != 0) inverseDay else Calendar.getInstance()[Calendar.DAY_OF_MONTH]
    }

    /**
     * 设置开始第一组时间跟结束时间
     */
    fun setFirstRange(inverseYear: Int = 0, inverseMonth: Int = 0, inverseDay: Int = 0) {
        this.mFirstCurrentYear = if (inverseYear != 0) inverseYear else Calendar.getInstance()[Calendar.YEAR]
        this.mFirstCurrentMonth = if (inverseMonth != -1) inverseMonth else Calendar.getInstance()[Calendar.MONTH]
        this.mFirstCurrentDay = if (inverseDay != 0) inverseDay else Calendar.getInstance()[Calendar.DAY_OF_MONTH]
    }

    /**
     * 设置第二组开始时间跟结束时间
     */
    fun setSecondRange(inverseYear: Int = 0, inverseMonth: Int = 0, inverseDay: Int = 0) {
        this.mSecondCurrentYear = if (inverseYear != 0) inverseYear else Calendar.getInstance()[Calendar.YEAR]
        this.mSecondCurrentMonth = if (inverseMonth != -1) inverseMonth else Calendar.getInstance()[Calendar.MONTH] + 1
        this.mSecondCurrentDay = if (inverseDay != 0) inverseDay else Calendar.getInstance()[Calendar.DAY_OF_MONTH]
    }

    /**
     * 设置日期
     */
    fun setRangDate(startDate: Calendar?, endDate: Calendar?) {
        if (startDate == null && endDate != null) {
            val year = endDate[Calendar.YEAR]
            val month = endDate[Calendar.MONTH] + 1
            val day = endDate[Calendar.DAY_OF_MONTH]
            if (year > startYear) {
                endYear = year
                endMonth = month
                endDay = day
            } else if (year == startYear) {
                if (month > startMonth) {
                    endYear = year
                    endMonth = month
                    endDay = day
                } else if (month == startMonth) {
                    if (day > startDay) {
                        endYear = year
                        endMonth = month
                        endDay = day
                    }
                }
            }
        } else if (startDate != null && endDate == null) {
            val year = startDate[Calendar.YEAR]
            val month = startDate[Calendar.MONTH] + 1
            val day = startDate[Calendar.DAY_OF_MONTH]
            if (year < endYear) {
                startMonth = month
                startDay = day
                startYear = year
            } else if (year == endYear) {
                if (month < endMonth) {
                    startMonth = month
                    startDay = day
                    startYear = year
                } else if (month == endMonth) {
                    if (day < endDay) {
                        startMonth = month
                        startDay = day
                        startYear = year
                    }
                }
            }
        } else if (startDate != null && endDate != null) {
            startYear = startDate[Calendar.YEAR]
            endYear = endDate[Calendar.YEAR]
            startMonth = startDate[Calendar.MONTH] + 1
            endMonth = endDate[Calendar.MONTH] + 1
            startDay = startDate[Calendar.DAY_OF_MONTH]
            endDay = endDate[Calendar.DAY_OF_MONTH]
        }
    }

    /**
     * 初始化开始时间
     */
    private fun setSolar(
        wheelYear: WheelView?,
        wheelMonth: WheelView?,
        wheelDay: WheelView?,
        year: Int, month: Int, day: Int, isFirst: Boolean
    ) {
        // 添加大小月月份并将其转换为list,方便之后的判断
        val months_big = arrayOf("1", "3", "5", "7", "8", "10", "12")
        val months_little = arrayOf("4", "6", "9", "11")
        val list_big = listOf(*months_big)
        val list_little = listOf(*months_little)

        if (isFirst) {
            mFirstCurrentYear = year
        } else {
            mSecondCurrentYear = year
        }
        // 年
        wheelYear?.adapter = NumericWheelAdapter(startYear, endYear) // 设置"年"的显示数据
        wheelYear?.currentItem = year - startYear // 初始化时显示的数据
        wheelYear?.setGravity(gravity)
        // 月
        when {
            startYear == endYear -> { //开始年等于终止年
                wheelMonth?.adapter = NumericWheelAdapter(startMonth, endMonth)
                wheelMonth?.currentItem = month + 1 - startMonth
            }

            year == startYear -> {
                //起始日期的月份控制
                wheelMonth?.adapter = NumericWheelAdapter(startMonth, 12)
                wheelMonth?.currentItem = month + 1 - startMonth
            }

            year == endYear -> {
                //终止日期的月份控制
                wheelMonth?.adapter = NumericWheelAdapter(1, endMonth)
                wheelMonth?.currentItem = month
            }

            else -> {
                wheelMonth?.adapter = NumericWheelAdapter(1, 12)
                wheelMonth?.currentItem = month
            }
        }
        wheelMonth?.setGravity(gravity)
        // 日
        val leapYear = year % 4 == 0 && year % 100 != 0 || year % 400 == 0
        if (year == startYear && month + 1 == startMonth) {
            // 起始日期的天数控制
            when {
                list_big.contains((month + 1).toString()) -> {
                    wheelDay?.adapter = NumericWheelAdapter(startDay, 31)
                }

                list_little.contains((month + 1).toString()) -> {
                    wheelDay?.adapter = NumericWheelAdapter(startDay, 30)
                }

                else -> {
                    // 闰年 29，平年 28
                    wheelDay?.adapter = NumericWheelAdapter(startDay, if (leapYear) 29 else 28)
                }
            }
            wheelDay?.currentItem = day - startDay
        } else if (year == endYear && month + 1 == endMonth) {
            // 终止日期的天数控制
            if (list_big.contains((month + 1).toString())) {
                if (endDay > 31) {
                    endDay = 31
                }
                wheelDay?.adapter = NumericWheelAdapter(1, endDay)
            } else if (list_little.contains((month + 1).toString())) {
                if (endDay > 30) {
                    endDay = 30
                }
                wheelDay?.adapter = NumericWheelAdapter(1, endDay)
            } else {
                // 闰年
                if (leapYear) {
                    if (endDay > 29) {
                        endDay = 29
                    }
                    wheelDay?.adapter = NumericWheelAdapter(1, endDay)
                } else {
                    if (endDay > 28) {
                        endDay = 28
                    }
                    wheelDay?.adapter = NumericWheelAdapter(1, endDay)
                }
            }
            wheelDay?.currentItem = day - 1
        } else {
            // 判断大小月及是否闰年,用来确定"日"的数据
            when {
                list_big.contains((month + 1).toString()) -> {
                    wheelDay?.adapter = NumericWheelAdapter(1, 31)
                }

                list_little.contains((month + 1).toString()) -> {
                    wheelDay?.adapter = NumericWheelAdapter(1, 30)
                }

                else -> {
                    // 闰年 29，平年 28
                    wheelDay?.adapter = NumericWheelAdapter(startDay, if (leapYear) 29 else 28)
                }
            }
            wheelDay?.currentItem = day - 1
        }
        wheelDay?.setGravity(gravity)


        // 添加"年"监听
        wheelYear?.setOnItemSelectedListener(OnItemSelectedListener { index ->
            val year_num = index + startYear
//            currentYear = year_num
            if (isFirst) {
                mFirstCurrentYear = year_num
            } else {
                mSecondCurrentYear = year_num
            }
            var currentMonthItem: Int = wheelMonth?.currentItem!! //记录上一次的item位置
            // 判断大小月及是否闰年,用来确定"日"的数据
            if (startYear == endYear) {
                //重新设置月份
                wheelMonth?.adapter = NumericWheelAdapter(startMonth, endMonth)
                if (currentMonthItem > wheelMonth?.adapter?.itemsCount!! - 1) {
                    currentMonthItem = wheelMonth?.adapter?.itemsCount!! - 1
                    wheelMonth?.currentItem = currentMonthItem
                }
                val monthNum = currentMonthItem + startMonth
                when (monthNum) {
                    startMonth -> {
                        //重新设置日
                        setReDay(wheelDay, year_num, monthNum, startDay, 31, list_big, list_little)
                    }

                    endMonth -> {
                        setReDay(wheelDay, year_num, monthNum, 1, endDay, list_big, list_little)
                    }

                    else -> { //重新设置日
                        setReDay(wheelDay, year_num, monthNum, 1, 31, list_big, list_little)
                    }
                }
            } else if (year_num == startYear) { //等于开始的年
                //重新设置月份
                wheelMonth?.adapter = NumericWheelAdapter(startMonth, 12)
                if (currentMonthItem > wheelMonth?.adapter?.itemsCount!! - 1) {
                    currentMonthItem = wheelMonth?.adapter?.itemsCount!! - 1
                    wheelMonth?.currentItem = currentMonthItem
                }
                val month = currentMonthItem + startMonth
                if (month == startMonth) {
                    //重新设置日
                    setReDay(wheelDay, year_num, month, startDay, 31, list_big, list_little)
                } else {
                    //重新设置日
                    setReDay(wheelDay, year_num, month, 1, 31, list_big, list_little)
                }
            } else if (year_num == endYear) {
                //重新设置月份
                wheelMonth?.adapter = NumericWheelAdapter(1, endMonth)
                if (currentMonthItem > wheelMonth?.adapter?.itemsCount!! - 1) {
                    currentMonthItem = wheelMonth?.adapter?.itemsCount!! - 1
                    wheelMonth?.currentItem = currentMonthItem
                }
                val monthNum = currentMonthItem + 1
                if (monthNum == endMonth) {
                    //重新设置日
                    setReDay(wheelDay, year_num, monthNum, 1, endDay, list_big, list_little)
                } else {
                    //重新设置日
                    setReDay(wheelDay, year_num, monthNum, 1, 31, list_big, list_little)
                }
            } else {
                //重新设置月份
                wheelMonth?.adapter = NumericWheelAdapter(1, 12)
                //重新设置日
                setReDay(wheelDay, year_num, wheelMonth?.currentItem!! + 1, 1, 31, list_big, list_little)
            }
            mSelectChangeCallback?.onTimeSelectChanged()
        })


        // 添加"月"监听
        wheelMonth?.setOnItemSelectedListener(OnItemSelectedListener { index ->
            var month_num = index + 1
            var currentYear = 0
            if (isFirst) {
                currentYear = mFirstCurrentYear
            } else {
                currentYear = mSecondCurrentYear
            }
            if (currentYear == startYear) {
                month_num = month_num + startMonth - 1
                if (month_num == startMonth) {
                    //重新设置日
                    setReDay(wheelDay, currentYear, month_num, startDay, 31, list_big, list_little)
                } else {
                    //重新设置日
                    setReDay(wheelDay, currentYear, month_num, 1, 31, list_big, list_little)
                }
            } else if (currentYear == endYear) {
                if (month_num == endMonth) {
                    //重新设置日
                    setReDay(wheelDay, currentYear, wheelMonth?.currentItem!! + 1, 1, endDay, list_big, list_little)
                } else {
                    setReDay(wheelDay, currentYear, wheelMonth?.currentItem!! + 1, 1, 31, list_big, list_little)
                }
            } else {
                //重新设置日
                setReDay(wheelDay, currentYear, month_num, 1, 31, list_big, list_little)
            }
            mSelectChangeCallback?.onTimeSelectChanged()
        })
        setChangedListener(wheelDay)
        setContentTextSize(wheelYear, wheelMonth, wheelDay)
        setLineSpacingMultiplier(1.8F)
    }

    private fun setReDay(wheelDay: WheelView?, year_num: Int, monthNum: Int, startD: Int, endD: Int, list_big: List<String>, list_little: List<String>) {
        var endD = endD
        var currentItem: Int = wheelDay?.currentItem!!

//        int maxItem;
        if (list_big.contains(monthNum.toString())) {
            if (endD > 31) {
                endD = 31
            }
            wheelDay?.adapter = NumericWheelAdapter(startD, endD)
            //            maxItem = endD;
        } else if (list_little.contains(monthNum.toString())) {
            if (endD > 30) {
                endD = 30
            }
            wheelDay?.adapter = NumericWheelAdapter(startD, endD)
            //            maxItem = endD;
        } else {
            if (year_num % 4 == 0 && year_num % 100 != 0
                || year_num % 400 == 0
            ) {
                if (endD > 29) {
                    endD = 29
                }
                wheelDay?.adapter = NumericWheelAdapter(startD, endD)
                //                maxItem = endD;
            } else {
                if (endD > 28) {
                    endD = 28
                }
                wheelDay?.adapter = NumericWheelAdapter(startD, endD)
                //                maxItem = endD;
            }
        }
        if (currentItem > wheelDay?.adapter?.itemsCount!! - 1) {
            currentItem = wheelDay?.adapter?.itemsCount!! - 1
            wheelDay?.currentItem = currentItem
        }
    }


    fun getFirstTime(): String? {
        var sb = StringBuilder()
        if (mFirstCurrentYear == startYear) {
            if (wheelFirstMonth?.currentItem!! + startMonth == startMonth) {
                sb.append(wheelFirstYear?.currentItem!! + startYear).append("-")
                    .append(formatNumber(wheelFirstMonth?.currentItem!! + startMonth)).append("-")
                    .append(formatNumber(wheelFirstDay?.currentItem!! + startDay))
            } else {
                sb.append(wheelFirstYear?.currentItem!! + startYear).append("-")
                    .append(formatNumber(wheelFirstMonth?.currentItem!! + startMonth)).append("-")
                    .append(formatNumber(wheelFirstDay?.currentItem!! + 1))
            }
        } else {
            sb.append(wheelFirstYear?.currentItem!! + startYear).append("-")
                .append(formatNumber(wheelFirstMonth?.currentItem!! + 1)).append("-")
                .append(formatNumber(wheelFirstDay?.currentItem!! + 1))
        }
        return sb.toString()
    }

    fun formatNumber(number: Int): String {
        return if (number in 1..9) {
            String.format("%02d", number)
        } else {
            number.toString()
        }
    }
    fun getSecondTime(): String? {
        var sb = StringBuilder()
        if (mSecondCurrentYear == startYear) {
            if (wheelSecondMonth?.currentItem!! + startMonth == startMonth) {
                sb.append(wheelSecondYear?.currentItem!! + startYear).append("-")
                    .append(formatNumber(wheelSecondMonth?.currentItem!! + startMonth)).append("-")
                    .append(formatNumber(wheelSecondDay?.currentItem!! + startDay))
            } else {
                sb.append(wheelSecondYear?.currentItem!! + startYear).append("-")
                    .append(formatNumber(wheelSecondMonth?.currentItem!! + startMonth)).append("-")
                    .append(formatNumber(wheelSecondDay?.currentItem!! + 1))
            }
        } else {
            sb.append(wheelSecondYear?.currentItem!! + startYear).append("-")
                .append(formatNumber(wheelSecondMonth?.currentItem!! + 1)).append("-")
                .append(formatNumber(wheelSecondDay?.currentItem!! + 1))
        }
        return sb.toString()
    }


    private fun setChangedListener(wheelView: WheelView?) {
        wheelView?.setOnItemSelectedListener { mSelectChangeCallback?.onTimeSelectChanged() }
    }

    /**
     * 设置间距倍数,但是只能在1.0-4.0f之间
     *
     * @param lineSpacingMultiplier
     */
    fun setLineSpacingMultiplier(lineSpacingMultiplier: Float) {
        wheelFirstYear?.setLineSpacingMultiplier(lineSpacingMultiplier)
        wheelFirstMonth?.setLineSpacingMultiplier(lineSpacingMultiplier)
        wheelFirstDay?.setLineSpacingMultiplier(lineSpacingMultiplier)
        wheelSecondYear?.setLineSpacingMultiplier(lineSpacingMultiplier)
        wheelSecondMonth?.setLineSpacingMultiplier(lineSpacingMultiplier)
        wheelSecondDay?.setLineSpacingMultiplier(lineSpacingMultiplier)
    }

    /**
     * 设置分割线的颜色
     *
     * @param dividerColor
     */
    fun setDividerColor(dividerColor: Int) {
        wheelFirstYear?.setDividerColor(dividerColor)
        wheelFirstMonth?.setDividerColor(dividerColor)
        wheelFirstDay?.setDividerColor(dividerColor)
        wheelSecondYear?.setDividerColor(dividerColor)
        wheelSecondMonth?.setDividerColor(dividerColor)
        wheelSecondDay?.setDividerColor(dividerColor)
    }

    /**
     * 设置分割线的类型
     *
     * @param dividerType
     */
    fun setDividerType(dividerType: DividerType?) {
        wheelFirstYear?.setDividerType(dividerType)
        wheelFirstMonth?.setDividerType(dividerType)
        wheelFirstDay?.setDividerType(dividerType)
        wheelSecondYear?.setDividerType(dividerType)
        wheelSecondMonth?.setDividerType(dividerType)
        wheelSecondDay?.setDividerType(dividerType)
    }

    /**
     * 设置分割线之间的文字的颜色
     *
     * @param textColorCenter
     */
    fun setTextColorCenter(textColorCenter: Int) {
        wheelFirstYear?.setTextColorCenter(textColorCenter)
        wheelFirstMonth?.setTextColorCenter(textColorCenter)
        wheelFirstDay?.setTextColorCenter(textColorCenter)
        wheelSecondYear?.setTextColorCenter(textColorCenter)
        wheelSecondMonth?.setTextColorCenter(textColorCenter)
        wheelSecondDay?.setTextColorCenter(textColorCenter)
    }

    /**
     * 设置分割线以外文字的颜色
     *
     * @param textColorOut
     */
    fun setTextColorOut(textColorOut: Int) {
        wheelFirstYear?.setTextColorOut(textColorOut)
        wheelFirstMonth?.setTextColorOut(textColorOut)
        wheelFirstDay?.setTextColorOut(textColorOut)
        wheelSecondYear?.setTextColorOut(textColorOut)
        wheelSecondMonth?.setTextColorOut(textColorOut)
        wheelSecondDay?.setTextColorOut(textColorOut)
    }

    /**
     * @param isCenterLabel 是否只显示中间选中项的
     */
    fun isCenterLabel(isCenterLabel: Boolean) {
        wheelFirstYear?.isCenterLabel(isCenterLabel)
        wheelFirstMonth?.isCenterLabel(isCenterLabel)
        wheelFirstDay?.isCenterLabel(isCenterLabel)
        wheelSecondYear?.isCenterLabel(isCenterLabel)
        wheelSecondMonth?.isCenterLabel(isCenterLabel)
        wheelSecondDay?.isCenterLabel(isCenterLabel)
    }

    // 设置文字大小
    private fun setContentTextSize(year: WheelView?, month: WheelView?, day: WheelView?) {
        year?.setTextSize(textSize)
        month?.setTextSize(textSize)
        day?.setTextSize(textSize)
    }
}