package com.business_clean.app.weight;

import android.app.Activity;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.business_clean.R;

public class CustomTabBar extends LinearLayout {

    private FrameLayout backButton;
    private TextView tvTitleView;
    private TextView tvSubTitleView;
    private TextView tvRightView, tvRightSubView;
    private ImageView ivRightView;
    private ImageView ivRightSideView;
    private Context mContext;

    private OnClickListener externalClickListener;


    public CustomTabBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        LayoutInflater.from(getContext()).inflate(R.layout.custom_tab_bar, this, true);

        backButton = findViewById(R.id.iv_back);
        tvTitleView = findViewById(R.id.tv_title);
        tvSubTitleView = findViewById(R.id.tv_sub_title);
        tvRightView = findViewById(R.id.tv_right);
        tvRightSubView = findViewById(R.id.tv_right_sub);
        ivRightView = findViewById(R.id.iv_right);
        ivRightSideView = findViewById(R.id.iv_right_side);

        TypedArray typedArray = getContext().obtainStyledAttributes(attrs, R.styleable.CustomTabBar);
        setBackButtonVisible(typedArray.getBoolean(R.styleable.CustomTabBar_backButtonVisible, true));
        setTitle(typedArray.getString(R.styleable.CustomTabBar_titleText));
        setRightText(typedArray.getString(R.styleable.CustomTabBar_rightText));
        typedArray.recycle();

        backButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mContext != null) {
                    ((Activity) mContext).finish();
                }
            }
        });

    }


    public FrameLayout getBackButton() {
        return backButton;
    }

    public void setBackButtonVisible(boolean visible) {
        backButton.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
    }

    public void setTitle(String title) {
        tvTitleView.setText(title);
    }

    public void setSubTitle(String subTitle) {
        if (!TextUtils.isEmpty(subTitle)) {
            tvSubTitleView.setText(subTitle);
            tvSubTitleView.setVisibility(View.VISIBLE);
        }
    }

    public TextView getCenterTitleView() {
        return tvTitleView;
    }

    public TextView getTvRightView() {
        return tvRightView;
    }

    public void setRightText(String rightText) {
        tvRightView.setText(rightText);
        tvRightView.setVisibility(View.VISIBLE);
    }

    public void setRightSubText(String rightText) {
        tvRightSubView.setText(rightText);
        tvRightSubView.setVisibility(View.VISIBLE);
    }

    public void setRightIcon(int drawable_id) {
        ivRightView.setImageResource(drawable_id);
        ivRightView.setVisibility(View.VISIBLE);
    }


    public void setRightSideIcon(int drawable_id) {
        ivRightSideView.setImageResource(drawable_id);
        ivRightSideView.setVisibility(View.VISIBLE);
    }


    public void setOnRightSubTextClickListener(OnClickListener listener) {
        tvRightSubView.setOnClickListener(listener);
    }

    public void setOnRightTextClickListener(OnClickListener listener) {
        tvRightView.setOnClickListener(listener);
    }

    public void setOnImageRightTextClickListener(OnClickListener listener) {
        ivRightView.setOnClickListener(listener);
    }

    public void setOnImageRightSideClickListener(OnClickListener listener) {
        ivRightSideView.setOnClickListener(listener);
    }

}
