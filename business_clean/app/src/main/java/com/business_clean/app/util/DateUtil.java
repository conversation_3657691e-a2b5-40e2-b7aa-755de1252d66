package com.business_clean.app.util;

import com.blankj.utilcode.util.TimeUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {

    public static Calendar getDate2Calendar(String date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(TimeUtils.string2Date(date));
        return calendar;
    }

    public static String formatDateString(String dateStr) {
        // 创建解析格式
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-M-d");
        // 创建输出格式
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            // 解析输入日期
            Date date = inputFormat.parse(dateStr);
            // 格式化为输出日期
            return outputFormat.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return null; // 或者返回一个默认值
        }
    }

}
