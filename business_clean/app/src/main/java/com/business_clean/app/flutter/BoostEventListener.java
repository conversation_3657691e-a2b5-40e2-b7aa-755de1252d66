package com.business_clean.app.flutter;

import static com.business_clean.app.util.ActivityForwardUtil.*;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.TimeUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.business_clean.app.App;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.ext.LoadingDialogExtKt;
import com.business_clean.app.fdd.FddWebActivity;
import com.business_clean.app.flutter.bean.BaseSerializableFlutterMap;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.DownLoadHelper;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.UploadFileHelper;
import com.business_clean.app.util.pay.AliPayTool;
import com.business_clean.app.util.pay.WxPayTool;
import com.business_clean.app.util.permission.PermissionInterceptor;
import com.business_clean.app.util.share.ShareHelperTools;
import com.business_clean.app.util.share.ShareParams;
import com.business_clean.app.util.share.ShareType;
import com.business_clean.app.weight.dialog.InsureSharePopup;
import com.business_clean.app.weight.dialog.PagerDrawerPopup;
import com.business_clean.data.initconfig.PayDataEntity;
import com.business_clean.data.initconfig.PayDataInfo;
import com.business_clean.data.initconfig.PayWxDataInfo;
import com.business_clean.data.mode.BaseUuidEntity;
import com.business_clean.data.mode.roster.RosterFilterData;
import com.business_clean.ui.activity.BaseH5Activity;
import com.business_clean.ui.activity.CheckPermissionActivity;
import com.business_clean.ui.activity.address.AddAddressActivity;
import com.business_clean.ui.activity.balance.CorporateRechargeActivity;
import com.business_clean.ui.activity.clean.CleanReportActivity;
import com.business_clean.ui.activity.contract.ChooseNativeWorkFileActivity;
import com.business_clean.ui.activity.custom.ContractPreviewActivity;
import com.business_clean.ui.activity.custom.ContractSignWebActivity;
import com.business_clean.ui.activity.insure.ClaimsProcessActivity;
import com.business_clean.ui.activity.insure.ClaimsProcessDetailActivity;
import com.business_clean.ui.activity.insure.ElePolicyActivity;
import com.business_clean.ui.activity.main.MainActivity;
import com.business_clean.ui.activity.me.MyPhotosActivity;
import com.business_clean.ui.activity.personnel.SignHandActivity;
import com.business_clean.ui.activity.project.InsureCompanyActivity;
import com.business_clean.ui.activity.todo.TodoDetailActivity;
import com.google.gson.Gson;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.idlefish.flutterboost.EventListener;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.interfaces.OnSelectListener;
import com.qiniu.android.utils.LogUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.LogManager;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import me.hgj.mvvmhelper.ext.AppExtKt;
import me.hgj.mvvmhelper.ext.LogExtKt;
import me.hgj.mvvmhelper.util.LogXmManager;
import rxhttp.wrapper.param.RxHttp;

/**
 * Flutter 发送给原生的通知
 * 在这里处理各种内容方式
 */
public class BoostEventListener implements EventListener {

    private String[] payStrings = {"微信支付", "支付宝支付"};
    private String[] payContractStrings = {"余额支付", "微信支付", "支付宝支付"};

    @Override
    public void onEvent(String s, Map<String, Object> map) {
        Activity activity = FlutterBoost.instance().currentActivity();
        if (activity == null) {
            return;
        }
        Log.e(s, map.toString());
        if (map != null) {
            BaseSerializableFlutterMap serializableMap = new BaseSerializableFlutterMap();
            serializableMap.setMap(map);
            Bundle bundle = new Bundle();
            bundle.putSerializable("map", serializableMap);
            bundle.putBoolean("jump_flutter", true);
            String method = (String) map.get("method");
            LogUtil.e("---method----" + method);

            switch (method) {
                case "native_logout"://token 失效，去登录
                    CommonUtils.logoutApp();
                    break;
                case "pay_native"://走这里了
                    App.getAppViewModelInstance().getPayChannel().setValue("pay_native");
                    showPayDialog(activity, map.get("number").toString());
                    break;
                case "goto_base_web"://跳转原生web 无交互模式
                    String baseUrl = map.get("url").toString();
                    String baseTitle = "";
                    boolean isClose = false;
                    boolean share = false;
                    if (map.get("close") != null) {
                        isClose = (boolean) map.get("close");
                    }
                    if (map.get("share") != null) {
                        share = (boolean) map.get("share");
                    }
                    if (map.get("title") != null) {
                        baseTitle = (String) map.get("title");
                    }
                    bundle.putString("url", baseUrl);
                    bundle.putString("title", baseTitle);
                    bundle.putBoolean("isClose", isClose);
                    bundle.putBoolean("share", share);
                    startActivity(BaseH5Activity.class, bundle);
                    break;
                case "gotoWebPage"://跳转法大大 web
                    String url = map.get("url").toString();
                    LogUtil.d("---url----" + url);
                    bundle.putString("URL", url);

                    startActivity(FddWebActivity.class, bundle);
                    break;
                case "gotoChooseNativeFile"://选择本地文件
                    startActivity(ChooseNativeWorkFileActivity.class, bundle);
                    break;
                case "uploadFileToQiniu"://上传文件到七牛
                    String filePath = map.get("filePath").toString();
                    toUploadFile(activity, filePath);
                    break;
                case "pay_native_balance_recharge"://余额充值金额
                    App.getAppViewModelInstance().getPayChannel().setValue("pay_native_balance_recharge");
                    showBalancePay(activity, map.get("price").toString());
                    break;
                case "goto_recharge_balance_web"://对公充值
                    bundle.putString("url", map.get("url").toString());
                    startActivity(CorporateRechargeActivity.class, bundle);
                    break;
                case "goto_mark_camera"://去拍照 从flutter 过来拍照，拍照完成后，返回给flutter
                    String uuid = map.get("uuid").toString();
                    String projectUuid = map.get("project_uuid").toString();
                    bundle.putString("uuid", uuid);//渠道
                    bundle.putString("project_uuid", projectUuid);//渠道
                    startActivity(CheckPermissionActivity.class, bundle);
                    break;
                case "goto_open_preview"://调用原生的图片/视频展示
                    String media_url = map.get("url").toString();
                    if (TextUtils.isEmpty(media_url)) {
                        ToastUtil.show("图片资源异常");
                        return;
                    }
                    CommonUtils.showPreviewBig(activity, null, media_url);
                    break;
                case "create_work_order"://创建任务工单后，默认选中我创建的
                    App.getAppViewModelInstance().getRefreshWorkOrder().setValue(true);
                    App.getAppViewModelInstance().getRefreshWorkTask().setValue(true);
                    break;
                case "goto_show_project_dialog"://切换项目
                    if (Constant.ROLE_LEADER || Constant.ROLE_CLEANER || Constant.ROLE_PROJECT_OWNER) {
                        ToastUtil.show("暂无可切换项目");
                        return;
                    }
                    boolean isChangeAppProject = true;
                    if (map.get("isChangeAppProject") != null) {
                        isChangeAppProject = (boolean) map.get("isChangeAppProject");
                    }

                    boolean isNeedAll = false;
                    if (map.get("isNeedAll") != null) {
                        isNeedAll = (boolean) map.get("isNeedAll");
                    }

                    boolean isHeadOffice = false;
                    if (map.get("isHeadOffice") != null) {
                        isHeadOffice = (boolean) map.get("isHeadOffice");
                    }

                    String project_uuid = "";
                    if (map.get("project_uuid") != null) {
                        project_uuid = (String) map.get("project_uuid");
                    }
                    CommonUtils.showSelectProjectDialog(activity, project_uuid, isChangeAppProject, isNeedAll, isHeadOffice, new PagerDrawerPopup.OnSelectProjectListener() {
                        @Override
                        public void onClick(String project_uuid, String project_name) {
                            LogUtils.e("Flutter 选择项目 " + project_uuid + " ; name " + project_name);
                            //通知列表刷新
                            HashMap<String, Object> params = new HashMap<>();
                            params.put("project_name", project_name);
                            params.put("project_uuid", project_uuid);
                            FlutterBoost.instance().sendEventToFlutter("SelectProject", params);
                        }
                    });
                    break;
                case "goto_contract_preview": //电子合同预览
                    bundle.putString("url", (String) map.get("url"));
                    bundle.putString("share_title", (String) map.get("share_title"));
                    startActivity(ContractPreviewActivity.class, bundle);
                    break;
                case "goto_insure_detail"://弹出0.8屏弹窗来显示H5
                    String webUrl = (String) map.get("url");
                    String title = (String) map.get("title");
                    showInsureDialog(activity, webUrl, title);
                    break;
                case "goto_insure_ele_policy"://电子保单
                    bundle.putString("url", (String) map.get("url"));
                    bundle.putString("user_name", (String) map.get("user_name"));
                    bundle.putString("order_number", (String) map.get("order_number"));
                    startActivity(ElePolicyActivity.class, bundle);
                    break;
                case "goto_insure_claims_process"://理赔流程
                    bundle.putString("order_number", (String) map.get("order_number"));
                    bundle.putString("guarantee", (String) map.get("guarantee"));
                    if (!TextUtils.isEmpty((String) map.get("guarantee"))) {
                        startActivity(ClaimsProcessDetailActivity.class, bundle);
                    } else {
                        startActivity(ClaimsProcessActivity.class, bundle);
                    }
                    break;
                case "export_download_url"://下载地址
                    ///下载之前，先判断权限的问题
                    XXPermissions.with(activity)
                            .permission(AppExtKt.getExternalStorage())
                            .request(new OnPermissionCallback() {
                                @Override
                                public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                                    if (allGranted) {
                                        LogUtils.e("获取到存储权限了");
                                        String download_url = (String) map.get("url");
                                        String fileName = null;
                                        if (map.get("fileName") != null) {
                                            fileName = (String) map.get("fileName");
                                        } else {
                                            fileName = App.getAppViewModelInstance().getUserInfo().getValue().getCompany().getCompany_name() + "_" + TimeUtils.date2String(TimeUtils.getNowDate(), "yyyy-MM-dd-hh-mm");
                                        }
                                        DownLoadHelper.downloadFile(activity, download_url, null, fileName, true);
                                    }
                                }
                            });
                    break;
                ///公共的分享，只接收
                case "goto_share_wx_link":
                    String shareTitle = (String) map.get("title");
                    String shareLink = (String) map.get("link");
                    ShareParams params = new ShareParams();
                    params.setShareType(ShareType.WEIXIN);
                    params.setTitle(shareTitle);
                    params.setLinkUrl(shareLink);
                    ShareHelperTools.getInstance().shareCardLink(params, activity);
                    break;
                ///选择了考勤规则
                case "goto_roster_filter_attendance":
                    String uuidFilterAttendance = (String) map.get("uuid");
                    String nameFilterAttendance = (String) map.get("name");
                    App.getAppViewModelInstance().getRosterFilterAttendanceBaseUuidEntity().setValue(new BaseUuidEntity(uuidFilterAttendance, nameFilterAttendance));
                    //添加/编辑总部成员后刷新列表
                case "save_head_office":
                    App.getAppViewModelInstance().getRefreshContactManagerList().setValue(true);
                    break;
                //跳转到编辑电子合同页面
                case "gotoContractSignWebActivity":
                    Map<String, Object> data = (Map<String, Object>) map.get("params");
                    LogUtils.e("-------data----" + data.toString());
                    // 转换为 Bundle
                    for (Map.Entry<String, Object> entry : data.entrySet()) {
                        if (entry.getValue() instanceof String) {
                            bundle.putString(entry.getKey(), (String) entry.getValue());
                        } else if (entry.getValue() instanceof Integer) {
                            bundle.putInt(entry.getKey(), (Integer) entry.getValue());
                        } else if (entry.getValue() instanceof Map) {
                            // 处理 HashMap
                            Map<String, Object> hashMap = (Map<String, Object>) entry.getValue();
                            HashMap<String, String> hashMapBundle = new HashMap<>();
                            for (Map.Entry<String, Object> hashEntry : hashMap.entrySet()) {
                                if (hashEntry.getValue() instanceof String) {
                                    hashMapBundle.put(hashEntry.getKey(), (String) hashEntry.getValue());
                                } else if (hashEntry.getValue() instanceof Integer) {
                                    hashMapBundle.put(hashEntry.getKey(), hashEntry.getValue().toString());
                                }
                                // 根据需要处理其他类型
                            }
                            bundle.putSerializable("hashMap", hashMapBundle);
                        }
                    }
                    LogUtils.e("-------bundle----" + bundle.toString());
                    startActivity(ContractSignWebActivity.class, bundle);
                    break;
                //跳H5的参保方案
                case "gotoInsuranceScheme":
                    String insureUrl = (String) map.get("'url'");
                    String project_name = (String) map.get("'project_name'");
                    String finalUrl = MMKVHelper.getString(ConstantMMVK.ENVIRONMENT_BASE_URL_M2, NetUrl.WEB_BASE_RELEASE_URL_M2) + insureUrl;
                    bundle.putString("url", finalUrl);
                    bundle.putString("project_name", project_name);
                    startActivity(InsureCompanyActivity.class, bundle);
                    break;
                ///去往个人相册
                case "gotoMyPhotos":
                    String photosUuid = (String) map.get("uuid");
                    bundle.putString("uuid", photosUuid);
                    String photosUserName = (String) map.get("user_name");
                    bundle.putString("user_name", photosUserName);
                    startActivity(MyPhotosActivity.class, bundle);
                    break;
                ///项目添加打卡地点
                case "goto_add_address":
                    LogUtils.e("来这里了。11111");
                    XXPermissions.with(activity)
                            .permission(Permission.ACCESS_FINE_LOCATION)
                            .interceptor(new PermissionInterceptor("添加打卡地点，需要您提供精准的位置信息。"))
                            .request(new OnPermissionCallback() {
                                @Override
                                public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                                    if (allGranted) {
                                        bundle.putString("project_uuid", (String) map.get("project_uuid"));
                                        startActivity(AddAddressActivity.class, bundle);
                                    }
                                }

                                @Override
                                public void onDenied(@NonNull List<String> permissions, boolean doNotAskAgain) {
                                    if (doNotAskAgain) {
                                        CommonUtils.showGeneralDialog(activity, "授权", "该权限被永久拒绝授权\n请手动授权",
                                                "取消", "确定", null, new OnDialogConfirmListener() {
                                                    @Override
                                                    public void onConfirm() {
                                                        // 如果是被永久拒绝就跳转到应用权限系统设置页面
                                                        XXPermissions.startPermissionActivity(activity, permissions);
                                                    }
                                                });
                                    } else {
                                        ToastUtils.showShort("已拒绝授权，无法使用该功能");
                                    }
                                }
                            });
                    break;
                ///去签字
                case "goto_sign_hand":
                    bundle.putString("uuid", (String) map.get("uuid"));
                    startActivity(SignHandActivity.class, bundle);
                    break;
                ///花名册筛选
                case "roster_filter_rules":
                    String filterData = (String) map.get("filterData");
                    if (!TextUtils.isEmpty(filterData)) {
                        RosterFilterData rosterFilterData = new Gson().fromJson(filterData, RosterFilterData.class);
                        App.getAppViewModelInstance().getFilterRosterFilterData().postValue(rosterFilterData);
                    }
                    break;
                ///跳转到审批代办
                case "goto_todo_approve":
                    MMKVHelper.putBoolean(ConstantMMVK.JUMP_TODO_PAGE, true);
                    ActivityForwardUtil.startActivityAndClearOther(MainActivity.class, new Bundle());
                    break;
                ///Flutter 报错了，显示出来 记录 flutter 的报错
                case "Flutter_Error":
                    String error_msg = (String) map.get("'error_msg'");
                    if (!TextUtils.isEmpty(error_msg)) {
                        LogUtils.e("Flutter 报错了 " + map.get("'error_msg'"));
                        LogXmManager.log("Flutter 报错  Start -- " + map.get("'error_msg'") + " | Flutter Error End");
                    }
                    break;
                ///跳转代办详情
                case "goto_todo_one_details":
                    bundle.putString("application_type", map.get("application_type").toString());
                    bundle.putString("application_no", map.get("application_no").toString());
                    bundle.putString("application_status", map.get("application_status").toString());
                    bundle.putString("task_id", map.get("task_id").toString());
                    bundle.putString("read_status", map.get("read_status").toString());
                    bundle.putString("type", map.get("type").toString());
                    bundle.putBoolean("show_bottom_button", (Boolean) map.get("show_bottom_button"));
                    ActivityForwardUtil.startActivity(TodoDetailActivity.class, bundle);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 展示保险的套餐详情
     *
     * @param webUrl
     * @param title
     */
    private void showInsureDialog(Activity activity, String webUrl, String title) {
        new XPopup.Builder(activity)
                .asCustom(new InsureSharePopup(activity, webUrl, title, new InsureSharePopup.SharePopupListener() {
                    @Override
                    public void onXpopupDismiss() {

                    }
                }))
                .show();
    }


    /**
     * 余额充值的弹窗
     *
     * @param activity
     */
    private void showBalancePay(Activity activity, String amount) {
        CommonUtils.showBottomListWith(activity, 20, "", payStrings, new OnSelectListener() {
            @Override
            public void onSelect(int position, String text) {
                switch (position) {
                    case 0://微信支付
                        requestRechargeBalance(activity, 10, amount);
                        break;
                    case 1://支付宝支付
                        requestRechargeBalance(activity, 20, amount);
                        break;
                }
            }
        });
    }

    private void toUploadFile(Activity activity, String filePath) {
        LoadingDialogExtKt.showLoadingExt(activity, "上传中");
        ArrayList<String> strings1 = new ArrayList<>();
        strings1.add(filePath);
        UploadFileHelper.getInstance().uploadFiles(activity, strings1, UploadFileHelper.PATH_HEADER_CUSTOM, new UploadFileHelper.UploadListener() {
            @Override
            public void onUploadSuccess(String url) {
                LogUtil.e("----url------" + url);
                if (!activity.isFinishing()) {
                    LoadingDialogExtKt.dismissLoadingExt(activity);
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("url", url);
                    FlutterBoost.instance().sendEventToFlutter("UploadFile", hashMap);
                }
            }

            @Override
            public void onUploadFailed(String errorMessage) {
                LogExtKt.logE("失败  ： " + errorMessage, "七牛云上传");
                ToastUtil.show("上传失败，请重新上传");
                if (!activity.isFinishing()) {
                    LoadingDialogExtKt.dismissLoadingExt(activity);
                }
            }

            @Override
            public void onUploadProgress(int progress) {

            }

        });
    }

    /**
     * 支付弹窗
     *
     * @param activity
     */
    private void showPayDialog(Activity activity, String number) {

        CommonUtils.showBottomListWith(activity, 20, "", payContractStrings, new OnSelectListener() {
            @Override
            public void onSelect(int position, String text) {
                switch (position) {
                    case 0:
                        requestContract(activity, 1, number);
                        break;
                    case 1://微信支付
                        requestContract(activity, 10, number);
                        break;
                    case 2://支付宝支付
                        requestContract(activity, 20, number);
                        break;
                }
            }
        });
    }

    /**
     * 电子合同支付
     */
    private void requestContract(Activity activity, int pay_type, String product_number) {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("pay_type", "" + pay_type);//支付方式
        hashMap.put("product_number", "" + product_number);//支付方式
        RxHttp.get(NetUrl.CREATE_PAY_ORDER).addAll(hashMap)
                .asResponse(PayDataEntity.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<PayDataEntity>() {
                    @Override
                    public void accept(PayDataEntity response) throws Throwable {
                        if ("10".equals(response.getPay_type())) {
                            getWechatPay(activity, response.getPay_data().getWx_data());
                        } else if ("20".equals(response.getPay_type())) {
                            getAliPay(activity, response.getPay_data());
                        } else if ("1".equals(response.getPay_type())) {//余额支付,直接跳转成功
                            ToastUtil.show("购买成功");
                            FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                                    .pageName("contractPaySuccessPage")
                                    .arguments(new HashMap<>())
                                    .build());
                        }
                    }
                });
    }


    /**
     * 余额现金充值接口
     */
    private void requestRechargeBalance(Activity activity, int pay_type, String amount) {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("amount", "" + amount);//充值金额
        hashMap.put("pay_type", "" + pay_type);//支付方式
        RxHttp.get(NetUrl.CREATE_CASH_BALANCE_RECHARGE).addAll(hashMap)
                .asResponse(PayDataEntity.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<PayDataEntity>() {
                    @Override
                    public void accept(PayDataEntity response) throws Throwable {
                        if (pay_type == 10) {
                            getWechatPay(activity, response.getPay_data().getWx_data());
                        } else {
                            getAliPay(activity, response.getPay_data());
                        }
                    }
                });
    }


    private void getWechatPay(Activity activity, PayWxDataInfo payDataInfo) {
        new WxPayTool(activity, new WxPayTool.WxPayCallBack() {
            @Override
            public void NoPaySupported() {
            }
        }).pay(payDataInfo);
    }


    private void getAliPay(Activity activity, PayDataInfo payDataInfo) {
        new AliPayTool(activity, new AliPayTool.AliPayCallBack() {
            @Override
            public void succeedCallBack() {
                if (!TextUtils.isEmpty(App.getAppViewModelInstance().getPayChannel().getValue())) {
                    switch (App.getAppViewModelInstance().getPayChannel().getValue()) {
                        case "pay_native": //购买电子合同
                            FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                                    .pageName("contractPaySuccessPage")
                                    .arguments(new HashMap<>())
                                    .build());
                            break;
                        case "pay_native_balance_recharge"://充值余额
                            FlutterBoost.instance().sendEventToFlutter("pay_success_balance", null);
                            break;
                        default:
                            break;
                    }
                }
                ToastUtil.show("购买成功");
            }

            @Override
            public void failCallBack() {
            }
        }).aliPay(payDataInfo);
    }


}
