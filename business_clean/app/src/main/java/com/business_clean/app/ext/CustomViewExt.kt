package com.business_clean.app.ext

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.util.DisplayMetrics
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.SizeUtils
import com.business_clean.R
import com.business_clean.app.weight.loadCallBack.EmptyCallback
import com.business_clean.app.weight.loadCallBack.ErrorCallback
import com.business_clean.app.weight.loadCallBack.LoadingCallback
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.kingja.loadsir.core.LoadService
import com.kingja.loadsir.core.LoadSir
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import me.hgj.mvvmhelper.base.appContext

/**
 * 作者　: hegaojian
 * 时间　: 2020/2/20
 * 描述　:项目中自定义类的拓展函数
 */


fun LoadService<*>.setErrorText(message: String) {
    if (message.isNotEmpty()) {
        this.setCallBack(ErrorCallback::class.java) { _, view ->
            view.findViewById<TextView>(R.id.error_text).text = message
        }
    }
}

/**
 * 设置错误布局
 * @param message 错误布局显示的提示内容
 */
fun LoadService<*>.showError(message: String = "") {
    this.setErrorText(message)
    this.showCallback(ErrorCallback::class.java)
}

/**
 * 设置空布局
 */
fun LoadService<*>.showEmpty() {
    this.showCallback(EmptyCallback::class.java)
}

/**
 * 设置加载中
 */
fun LoadService<*>.showLoading() {
    this.showCallback(LoadingCallback::class.java)
}


/**
 * 获取屏幕宽
 */
fun getScreenWidth(): Int {
    val metric = DisplayMetrics()
    (appContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay
        .getMetrics(metric)
    return metric.widthPixels
}

/**
 * 获取屏幕高，包含状态栏，但不包含虚拟按键，如1920屏幕只有1794
 */
fun getScreenHeight(): Int {
    val metric = DisplayMetrics()
    (appContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay
        .getMetrics(metric)
    return metric.heightPixels
}


fun loadServiceInit(view: View, callback: () -> Unit): LoadService<Any> {
    val loadsir = LoadSir.getDefault().register(view) {
        //点击重试时触发的操作
        callback.invoke()
    }
    loadsir.showSuccess()
//    SettingUtil.setLoadingColor(SettingUtil.getColor(appContext), loadsir)
    return loadsir
}

//绑定普通的Recyclerview
fun RecyclerView.init(
    layoutManger: RecyclerView.LayoutManager,
    bindAdapter: RecyclerView.Adapter<*>,
    isScroll: Boolean = true
): RecyclerView {
    layoutManager = layoutManger
    setHasFixedSize(true)
    adapter = bindAdapter
    isNestedScrollingEnabled = isScroll
    return this
}

//绑定SwipeRecyclerView
//fun SwipeRecyclerView.init(
//    layoutManger: RecyclerView.LayoutManager,
//    bindAdapter: RecyclerView.Adapter<*>,
//    isScroll: Boolean = true
//): SwipeRecyclerView {
//    layoutManager = layoutManger
//    setHasFixedSize(true)
//    adapter = bindAdapter
//    isNestedScrollingEnabled = isScroll
//    return this
//}

//fun SwipeRecyclerView.initFooter(loadmoreListener: SwipeRecyclerView.LoadMoreListener): DefineLoadMoreView {
//    val footerView = DefineLoadMoreView(appContext)
//    //给尾部设置颜色
//    footerView.setLoadViewColor(SettingUtil.getOneColorStateList(appContext))
//    //设置尾部点击回调
//    footerView.setmLoadMoreListener(SwipeRecyclerView.LoadMoreListener {
//        footerView.onLoading()
//        loadmoreListener.onLoadMore()
//    })
//    this.run {
//        //添加加载更多尾部
//        addFooterView(footerView)
//        setLoadMoreView(footerView)
//        //设置加载更多回调
//        setLoadMoreListener(loadmoreListener)
//    }
//    return footerView
//}


fun RecyclerView.initFloatBtn(floatbtn: FloatingActionButton) {
    //监听recyclerview滑动到顶部的时候，需要把向上返回顶部的按钮隐藏
    addOnScrollListener(object : RecyclerView.OnScrollListener() {
        @SuppressLint("RestrictedApi")
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            super.onScrolled(recyclerView, dx, dy)
            if (!canScrollVertically(-1)) {
                floatbtn.visibility = View.INVISIBLE
            }
        }
    })
//    floatbtn.backgroundTintList = SettingUtil.getOneColorStateList(appContext)
    floatbtn.setOnClickListener {
        val layoutManager = layoutManager as LinearLayoutManager
        //如果当前recyclerview 最后一个视图位置的索引大于等于40，则迅速返回顶部，否则带有滚动动画效果返回到顶部
        if (layoutManager.findLastVisibleItemPosition() >= 40) {
            scrollToPosition(0)//没有动画迅速返回到顶部(马上)
        } else {
            smoothScrollToPosition(0)//有滚动动画返回到顶部(有点慢)
        }
    }
}

//初始化 SwipeRefreshLayout
fun SwipeRefreshLayout.init(onRefreshListener: () -> Unit) {
    this.run {
        setOnRefreshListener {
            onRefreshListener.invoke()
        }
        //设置主题颜色
//        setColorSchemeColors(SettingUtil.getColor(appContext))
    }
}

/**
 * 初始化普通的toolbar 只设置标题
 */
fun Toolbar.init(titleStr: String = ""): Toolbar {
//    setBackgroundColor(SettingUtil.getColor(appContext))
    title = titleStr
    return this
}

/**
 * 初始化有返回键的toolbar
 */
//fun Toolbar.initClose(
//    titleStr: String = "",
//    backImg: Int = R.drawable.ic_back,
//    onBack: (toolbar: Toolbar) -> Unit
//): Toolbar {
//    setBackgroundColor(SettingUtil.getColor(appContext))
//    title = titleStr.toHtml()
//    setNavigationIcon(backImg)
//    setNavigationOnClickListener { onBack.invoke(this) }
//    return this
//}

/**
 * 根据控件的类型设置主题，注意，控件具有优先级， 基本类型的控件建议放到最后，像 Textview，FragmentLayout，不然会出现问题，
 * 列如下面的BottomNavigationViewEx他的顶级父控件为FragmentLayout，如果先 is Fragmentlayout判断在 is BottomNavigationViewEx上面
 * 那么就会直接去执行 is FragmentLayout的代码块 跳过 is BottomNavigationViewEx的代码块了
 */
//fun setUiTheme(color: Int, vararg anyList: Any?) {
//    anyList.forEach { view ->
//        view?.let {
//            when (it) {
//                is LoadService<*> -> SettingUtil.setLoadingColor(color, it as LoadService<Any>)
//                is FloatingActionButton -> it.backgroundTintList =
//                    SettingUtil.getOneColorStateList(color)
//                is SwipeRefreshLayout -> it.setColorSchemeColors(color)
//                is DefineLoadMoreView -> it.setLoadViewColor(SettingUtil.getOneColorStateList(color))
//                is BottomNavigationViewEx -> {
//                    it.itemIconTintList = SettingUtil.getColorStateList(color)
//                    it.itemTextColor = SettingUtil.getColorStateList(color)
//                }
//                is Toolbar -> it.setBackgroundColor(color)
//                is TextView -> it.setTextColor(color)
//                is LinearLayout -> it.setBackgroundColor(color)
//                is ConstraintLayout -> it.setBackgroundColor(color)
//                is FrameLayout -> it.setBackgroundColor(color)
//            }
//        }
//    }
//}

//设置适配器的列表动画
fun BaseQuickAdapter<*, *>.setAdapterAnimation(mode: Int) {
    //等于0，关闭列表动画 否则开启
    if (mode == 0) {
        this.animationEnable = false
    } else {
        this.animationEnable = true
        this.setAnimationWithDefault(BaseQuickAdapter.AnimationType.values()[mode - 1])
    }
}

//fun MagicIndicator.bindViewPager2(
//    viewPager: ViewPager2,
//    mStringList: List<String> = arrayListOf(),
//    action: (index: Int) -> Unit = {}) {
//    val commonNavigator = CommonNavigator(appContext)
//    commonNavigator.adapter = object : CommonNavigatorAdapter() {
//
//        override fun getCount(): Int {
//            return  mStringList.size
//        }
//        override fun getTitleView(context: Context, index: Int): IPagerTitleView {
//            return ScaleTransitionPagerTitleView(appContext).apply {
//                //设置文本
//                text = mStringList[index].toHtml()
//                //字体大小
//                textSize = 17f
//                //未选中颜色
//                normalColor = Color.WHITE
//                //选中颜色
//                selectedColor = Color.WHITE
//                //点击事件
//                setOnClickListener {
//                    viewPager.currentItem = index
//                    action.invoke(index)
//                }
//            }
//        }
//        override fun getIndicator(context: Context): IPagerIndicator {
//            return LinePagerIndicator(context).apply {
//                mode = LinePagerIndicator.MODE_EXACTLY
//                //线条的宽高度
//                lineHeight = UIUtil.dip2px(appContext, 3.0).toFloat()
//                lineWidth = UIUtil.dip2px(appContext, 30.0).toFloat()
//                //线条的圆角
//                roundRadius = UIUtil.dip2px(appContext, 6.0).toFloat()
//                startInterpolator = AccelerateInterpolator()
//                endInterpolator = DecelerateInterpolator(2.0f)
//                //线条的颜色
//                setColors(Color.WHITE)
//            }
//        }
//    }
//    this.navigator = commonNavigator
//
//    viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
//        override fun onPageSelected(position: Int) {
//            super.onPageSelected(position)
//            <EMAIL>(position)
//            action.invoke(position)
//        }
//
//        override fun onPageScrolled(
//            position: Int,
//            positionOffset: Float,
//            positionOffsetPixels: Int
//        ) {
//            super.onPageScrolled(position, positionOffset, positionOffsetPixels)
//            <EMAIL>(position, positionOffset, positionOffsetPixels)
//        }
//
//        override fun onPageScrollStateChanged(state: Int) {
//            super.onPageScrollStateChanged(state)
//            <EMAIL>(state)
//        }
//    })
//}

fun ViewPager2.init(
    fragment: Fragment,
    fragments: ArrayList<Fragment>,
    isUserInputEnabled: Boolean = true
): ViewPager2 {
    //是否可滑动
    this.isUserInputEnabled = isUserInputEnabled
    //设置适配器
    adapter = object : FragmentStateAdapter(fragment) {
        override fun createFragment(position: Int) = fragments[position]
        override fun getItemCount() = fragments.size
    }
    return this
}

//fun ViewPager2.initMain(fragment: Fragment): ViewPager2 {
//    //是否可滑动
//    this.isUserInputEnabled = false
//    this.offscreenPageLimit = 5
//    //设置适配器
//    adapter = object : FragmentStateAdapter(fragment) {
//        override fun createFragment(position: Int): Fragment {
//            when (position) {
//                0 -> {
//                    return HomeFragment()
//                }
//                1 -> {
//                    return ProjectFragment()
//                }
//                2 -> {
//                    return TreeArrFragment()
//                }
//                3 -> {
//                    return PublicNumberFragment()
//                }
//                4 -> {
//                    return MeFragment()
//                }
//                else -> {
//                    return HomeFragment()
//                }
//            }
//        }
//        override fun getItemCount() = 5
//    }
//    return this
//}

//fun BottomNavigationViewEx.init(navigationItemSelectedAction: (Int) -> Unit): BottomNavigationViewEx {
//    enableAnimation(true)
//    enableShiftingMode(false)
//    enableItemShiftingMode(true)
//    itemIconTintList = SettingUtil.getColorStateList(SettingUtil.getColor(appContext))
//    itemTextColor = SettingUtil.getColorStateList(appContext)
//    setTextSize(12F)
//    setOnNavigationItemSelectedListener {
//        navigationItemSelectedAction.invoke(it.itemId)
//        true
//    }
//    return this
//}


/**
 * 拦截BottomNavigation长按事件 防止长按时出现Toast ---- 追求完美的大屌群友提的bug
 * @receiver BottomNavigationViewEx
 * @param ids IntArray
 */
//fun BottomNavigationViewEx.interceptLongClick(vararg ids:Int) {
//    val bottomNavigationMenuView: ViewGroup = (this.getChildAt(0) as ViewGroup)
//    for (index in ids.indices){
//        bottomNavigationMenuView.getChildAt(index).findViewById<View>(ids[index]).setOnLongClickListener {
//            true
//        }
//    }
//}

/**
 * 隐藏软键盘
 */
fun hideSoftKeyboard(activity: Activity?) {
    activity?.let { act ->
        val view = act.currentFocus
        view?.let {
            val inputMethodManager =
                act.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
            inputMethodManager.hideSoftInputFromWindow(
                view.windowToken,
                InputMethodManager.HIDE_NOT_ALWAYS
            )
        }
    }
}

/**
 * 加载列表数据
 */
//fun <T> loadListData(
//    data: ListDataUiState<T>,
//    baseQuickAdapter: BaseQuickAdapter<T, *>,
//    loadService: LoadService<*>,
//    recyclerView: SwipeRecyclerView,
//    swipeRefreshLayout: SwipeRefreshLayout
//) {
//    swipeRefreshLayout.isRefreshing = false
//    recyclerView.loadMoreFinish(data.isEmpty, data.hasMore)
//    if (data.isSuccess) {
//        //成功
//        when {
//            //第一页并没有数据 显示空布局界面
//            data.isFirstEmpty -> {
//                loadService.showEmpty()
//            }
//            //是第一页
//            data.isRefresh -> {
//                baseQuickAdapter.setList(data.listData)
//                loadService.showSuccess()
//            }
//            //不是第一页
//            else -> {
//                baseQuickAdapter.addData(data.listData)
//                loadService.showSuccess()
//            }
//        }
//    } else {
//        //失败
//        if (data.isRefresh) {
//            //如果是第一页，则显示错误界面，并提示错误信息
//            loadService.showError(data.errMessage)
//        } else {
//            recyclerView.loadMoreError(0, data.errMessage)
//        }
//    }
//}

/**
 * 初始化recyclerView的模式
 */
fun <T> initRecyclerViewVertical(
    recyclerView: RecyclerView,
    baseQuickAdapter: BaseQuickAdapter<T, *>
) {
    recyclerView.adapter = baseQuickAdapter
    recyclerView.layoutManager = LinearLayoutManager(recyclerView.context)
}


//绑定横向的Recyclerview
fun RecyclerView.initGridLayoutManager(
    context: Context,
    bindAdapter: RecyclerView.Adapter<*>,
    spanCount: Int,
    isScroll: Boolean = true,
    isDecoration: Boolean = true,
    includeEdge: Boolean = false,
): RecyclerView {
    var lm = GridLayoutManager(context, spanCount)
    layoutManager = lm
    setHasFixedSize(true)
    adapter = bindAdapter
    isNestedScrollingEnabled = isScroll
    if (isDecoration) {
        addItemDecoration(GridSpacingItemDecoration(spanCount, SizeUtils.dp2px(10f), includeEdge))
    }
    return this
}


fun setAdapterEmpty(text: String? = "暂无上传记录", isHideEmptyLogo: Boolean): View {
    val empty = View.inflate(appContext, R.layout.layout_empty, null)
    val tvEmpty = empty.findViewById<TextView>(R.id.tv_empty_data)
    val ivEmpty = empty.findViewById<ImageView>(R.id.loading_emptyimg)
    tvEmpty.text = text
    ivEmpty.visibility = if (isHideEmptyLogo) View.GONE else View.VISIBLE
    return empty
}