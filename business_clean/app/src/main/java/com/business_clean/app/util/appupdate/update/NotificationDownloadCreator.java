package com.business_clean.app.util.appupdate.update;

import android.app.Activity;
import android.app.NotificationManager;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.core.app.NotificationCompat;

import com.business_clean.R;
import com.business_clean.app.config.Constant;

import org.lzh.framework.updatepluginlib.base.DownloadCallback;
import org.lzh.framework.updatepluginlib.base.DownloadNotifier;
import org.lzh.framework.updatepluginlib.model.Update;

import java.io.File;
import java.util.UUID;

public class NotificationDownloadCreator extends DownloadNotifier {

    @Override
    public DownloadCallback create(Update update, Activity activity) {
        // 返回一个UpdateDownloadCB对象用于下载时使用来更新界面。
        return new NotificationCB(activity, update);
    }

    private static class NotificationCB implements DownloadCallback {
        private final NotificationManager manager;
        private final NotificationCompat.Builder builder;
        private final int id;
        private final ProgressBar dialogProgressBar;
        private final TextView dialogTvProgress;
        private final AlertDialog dialog;
        private int preProgress;
        private Update update;

        NotificationCB(Activity activity, Update update) {
            this.update = update;
            this.manager = (NotificationManager) activity.getSystemService(Context.NOTIFICATION_SERVICE);
            builder = new NotificationCompat.Builder(activity, Constant.channelDownload)
                    .setSmallIcon(activity.getApplicationInfo().icon)
                    .setAutoCancel(false)
                    .setContentTitle("正在下载")
                    .setContentText("下载中...")
                    .setPriority(NotificationCompat.PRIORITY_DEFAULT);


            id = Math.abs(UUID.randomUUID().hashCode());

            // 初始化对话框 dialog
            LayoutInflater inflater = activity.getLayoutInflater();
            View dialogView = inflater.inflate(R.layout.dialog_download_progress, null);
            ImageView ivClose = dialogView.findViewById(R.id.iv_dialog_update_download_close);

            ivClose.setVisibility(update.isForced() ? View.GONE : View.VISIBLE);

            dialogProgressBar = dialogView.findViewById(R.id.progress_update);
            dialogTvProgress = dialogView.findViewById(R.id.tv_dialog_update_progress);

            AlertDialog.Builder dialogBuilder = new AlertDialog.Builder(activity)
                    .setCancelable(update.isForced())
                    .setView(dialogView);

            dialog = dialogBuilder.create();

            ivClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });
        }

        @Override
        public void onDownloadStart() {
            // 下载开始时的通知回调。运行于主线程
            manager.notify(id, builder.build());
            dialog.show();
        }

        @Override
        public void onDownloadComplete(File file) {
            // 下载完成的回调。运行于主线程
            manager.cancel(id);
            dialog.dismiss();
        }

        @Override
        public void onDownloadProgress(long current, long total) {
            // 下载过程中的进度信息。在此获取进度信息。运行于主线程
            int progress = (int) (current * 1f / total * 100);
            // 过滤不必要的刷新进度
            if (preProgress < progress) {
                preProgress = progress;
                builder.setProgress(100, progress, false);
                builder.setContentText("下载中..." + progress + "%");
                manager.notify(id, builder.build());

                // 更新对话框进度
                dialogProgressBar.setProgress(progress);
                dialogTvProgress.setText("更新中" + progress + "%");
            }
        }

        @Override
        public void onDownloadError(Throwable t) {
            // 下载时出错。运行于主线程
            manager.cancel(id);
            dialog.dismiss();
        }
    }
}



