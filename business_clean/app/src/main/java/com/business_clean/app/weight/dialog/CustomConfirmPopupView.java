package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.business_clean.R;
import com.business_clean.app.callback.OnDialogCancelListener;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.lxj.xpopup.core.CenterPopupView;

public class CustomConfirmPopupView extends CenterPopupView {

    private int layout;

    private TextView tvTitle;
    private TextView tvContent;
    private TextView tvCancel;
    private TextView tvConfirm;

    private View mBottomViewLine;

    private String title;
    private String msg;
    private String negationText;
    private String positiveText;
    private OnDialogCancelListener onCancelListener;
    private OnDialogConfirmListener onConfirmListener;

    public CustomConfirmPopupView(@NonNull Context context, int layout, String title, String msg, String negationText, String positiveText,
                                  OnDialogCancelListener onCancelListener, OnDialogConfirmListener onConfirmListener) {
        super(context);
        this.layout = layout;
        this.title = title;
        this.msg = msg;
        this.negationText = negationText;
        this.positiveText = positiveText;
        this.onCancelListener = onCancelListener;
        this.onConfirmListener = onConfirmListener;
    }

    @Override
    protected int getImplLayoutId() {
        return layout;
    }

    @Override
    protected void onCreate() {
        tvTitle = findViewById(R.id.tv_title);
        tvContent = findViewById(R.id.tv_content);
        tvCancel = findViewById(R.id.tv_cancel);
        tvConfirm = findViewById(R.id.tv_confirm);
        mBottomViewLine = findViewById(R.id.dialog_material_line);

        tvTitle.setText(title);
        tvContent.setText(msg);
        tvCancel.setText(negationText);
        tvConfirm.setText(positiveText);

        if (!TextUtils.isEmpty(negationText)) {
            tvCancel.setVisibility(View.VISIBLE);
        } else {
            tvCancel.setVisibility(View.GONE);
        }

        if (!TextUtils.isEmpty(positiveText)) {
            tvConfirm.setVisibility(View.VISIBLE);
        } else {
            tvConfirm.setVisibility(View.GONE);
        }
        if (tvCancel.getVisibility() == View.GONE) {
            mBottomViewLine.setVisibility(View.GONE);
        }
        onBindClick();
    }

    private void onBindClick() {
        if (tvCancel != null) {
            tvCancel.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                    if (onCancelListener != null) {
                        onCancelListener.onCancel();
                    }
                }
            });
        }

        if (tvConfirm != null) {
            tvConfirm.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                    if (onConfirmListener != null) {
                        onConfirmListener.onConfirm();
                    }
                }
            });
        }
    }
}
