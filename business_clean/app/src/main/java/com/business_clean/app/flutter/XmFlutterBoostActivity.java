package com.business_clean.app.flutter;

import android.app.Activity;
import android.content.pm.ActivityInfo;
import android.content.res.TypedArray;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.idlefish.flutterboost.containers.FlutterBoostActivity;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;

import io.flutter.embedding.engine.FlutterEngine;
import me.hgj.mvvmhelper.ext.AppExtKt;

public class XmFlutterBoostActivity extends FlutterBoostActivity {

    private HashMap<String, String> hashMap = new HashMap<>();


    @Override
    public void configureFlutterEngine(@NonNull @NotNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
//        FlutterChannelManager.instance().configureFlutterEngine(this, flutterEngine);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (Build.VERSION.SDK_INT == 26 && isTranslucentOrFloating()) {
            fixOrientation(this);
        }
        super.onCreate(savedInstanceState);
        AppExtKt.addActivity(this);
        //设置沉浸式状态栏
//        ImmersionBar.with(this)
//                .statusBarColor(R.color.white)
//                .navigationBarColor(R.color.white)
//                .statusBarDarkFont(true,0.5f)   //状态栏字体是深色，不写默认为亮色
//                .init();
    }


    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(0, 0);
    }


    /**
     * hook反射方向检查
     **/
    protected static void fixOrientation(Activity activity) {
        try {
            Class activityClass = Activity.class;
            Field mActivityInfoField = activityClass.getDeclaredField("mActivityInfo");
            mActivityInfoField.setAccessible(true);
            ActivityInfo activityInfo = (ActivityInfo) mActivityInfoField.get(activity);
            //设置屏幕不固定
            activityInfo.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED;
        } catch (Exception e) {
        }
    }

    /**
     * hook反射检查是否透明色或者悬浮
     **/
    protected boolean isTranslucentOrFloating() {
        boolean isTranslucentOrFloating = false;
        try {
            int[] styleableRes = (int[]) Class.forName("com.android.internal.R$styleable").getField("Window").get(null);
            final TypedArray typedArray = obtainStyledAttributes(styleableRes);
            Method method = ActivityInfo.class.getMethod("isTranslucentOrFloating", TypedArray.class);
            method.setAccessible(true);
            isTranslucentOrFloating = (boolean) method.invoke(null, typedArray);
            method.setAccessible(false);
        } catch (Exception e) {
        }
        return isTranslucentOrFloating;
    }
}
