package com.business_clean.app.service;

import android.content.Context;
import android.location.Location;
import android.os.Looper;
import android.util.Log;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;

/**
 * 高德地图通用定位服务
 */
public class LocService {

    private static LocService instance;
    private AMapLocationClient locationClient;
    private AMapLocationClientOption locationOption;
    private Object objLock;

    public static LocService getInstance(Context context) {
        if (instance == null) {
            instance = new LocService(context);
        }
        return instance;
    }


    public void setLocationOption(AMapLocationClientOption locationOption) {
        this.locationOption = locationOption;
    }

    /***
     * 初始化 AMapLocationClient
     *
     * @param context
     */
    public LocService(Context context) {
        objLock = new Object();
        synchronized (objLock) {
            if (locationClient == null) {
                try {
                    locationClient = new AMapLocationClient(context);
                    if (locationOption == null) {
                        locationOption = getDefaultLocationClientOption();
                    }
                    locationClient.setLocationOption(locationOption);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /***
     * 注册定位监听
     *
     * @param listener
     */
    public void registerListener(AMapLocationListener listener) {
        if (locationClient != null && listener != null) {
            locationClient.setLocationListener(listener);
            Log.e("LocService", "高德定位 注册地址监听器了");
        }
    }

    public void unregisterListener() {
        if (locationClient != null) {
            locationClient.setLocationListener(null);
            Log.e("LocService", "高德定位 注销地址监听器了");
        }
    }

    /***
     * 启动定位
     */
    public void start() {
        synchronized (objLock) {
            // 先停止定位
            if (locationClient != null && locationClient.isStarted()) {
                locationClient.stopLocation();
                Log.e("LocService", "高德地图Service stop");
            }

            // 然后启动定位
            if (locationClient != null && !locationClient.isStarted()) {
                locationClient.startLocation();
                Log.e("LocService", "高德地图Service start");
            }
        }
    }

    /***
     * 停止定位
     */
    public void stop() {
        synchronized (objLock) {
            if (locationClient != null && locationClient.isStarted()) {
                locationClient.stopLocation();
                Log.e("LocService", "高德地图Service stop");
            }
        }
    }

    /***
     * 请求位置
     */
    public void requestLocation() {
        if (locationClient != null) {
            locationClient.startLocation();
        }
    }

    public void destroy() {
        if (locationClient != null) {
            locationClient.onDestroy();
        }
    }

    /***
     * 设置定位参数
     *
     * @return
     */
    public AMapLocationClientOption getDefaultLocationClientOption() {
        AMapLocationClientOption option = new AMapLocationClientOption();
        option.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);//可选，设置定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
        option.setGpsFirst(true);//可选，设置是否gps优先，只在高精度模式下有效。默认关闭
        option.setHttpTimeOut(10000);//可选，设置网络请求超时时间。默认为30秒。在仅设备模式下无效
        option.setInterval(2000);//可选，设置定位间隔。默认为2秒
        option.setNeedAddress(true);//可选，设置是否返回逆地理地址信息。默认是true
        AMapLocationClientOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTP);//可选， 设置网络请求的协议。可选HTTP或者HTTPS。默认为HTTP
        //关闭缓存机制
        option.setLocationCacheEnable(false);
        option.setGeoLanguage(AMapLocationClientOption.GeoLanguage.DEFAULT);//可选，设置逆地理信息的语言，默认值为默认语言（根据所在地区选择语言）
        return option;
    }

    public boolean isStarted() {
        return locationClient != null && locationClient.isStarted();
    }
}