package com.business_clean.app.weight.camera;

import static me.hgj.mvvmhelper.ext.CommExtKt.isLocationOpen;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.location.Geocoder;
import android.location.Location;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.blankj.utilcode.util.TimeUtils;
import com.business_clean.R;

import java.util.Date;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;


public class WaterMaskView extends ConstraintLayout {

    private View mView;
    private TextView mTvAddress;
    private TextView mTvWeek;
    private TextView mTvHour;
    private TextView mTvDate;
    private String mTextColor;
    private String currentAddress = "";
    private String mRemark = "";
    private Geocoder geocoder;
    private Location mLocation;
    public static String currentProduct = "worldmap";


    public WaterMaskView(@NonNull Context context) {
        this(context, null);
    }

    public WaterMaskView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WaterMaskView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        geocoder = new Geocoder(context, Locale.getDefault());
        initView();
        initTimeListener();
    }

    private void initView() {
        mView = View.inflate(getContext(), R.layout.view_water_mask, this);
        mTvAddress = mView.findViewById(R.id.tv_mask_address);
        mTvWeek = mView.findViewById(R.id.tv_mask_week);
        mTvDate = mView.findViewById(R.id.tv_mask_date);
        mTvHour = mView.findViewById(R.id.tv_mask_hour);
        updateTime(null);
//        Typeface typeFace = Typeface.createFromAsset(getContext().getAssets(), "fonts/Modern.ttf");
//        mTvHour.setTypeface(typeFace);

//        mTvDate.setText(TimeUtils.date2String(TimeUtils.getNowDate(), "yyyy-MM-dd"));
//        mTvWeek.setText(TimeUtils.getChineseWeek(TimeUtils.getNowDate()));
    }

    /**
     * 设置字体大小
     */
    public void waterViewTvSize() {
        int size = 20;

//        mTvAddress.setTextSize(TypedValue.COMPLEX_UNIT_SP, size);
//
//        mTvDesc.setTextSize(TypedValue.COMPLEX_UNIT_SP, size);
//
//        mTvTime.setTextSize(TypedValue.COMPLEX_UNIT_SP, size);
//
//        mTvLat.setTextSize(TypedValue.COMPLEX_UNIT_SP, size);
        setDrawingCacheEnabled(false);
    }


    /**
     * 初始化页面中的时钟数据
     */
    @SuppressLint("CheckResult")
    private void initTimeListener() {

    }

    /**
     * 更新时间
     * 时间层面如果节省性能可以年,月变动频繁的在initTimeListener中获取,此处防止测试进行暴力测试,始终获取
     */
    public void updateTime(Date date) {
        if (date != null) {
            //更新周几
            mTvWeek.setText(TimeUtils.getChineseWeek(date));
            //更新日期
            String nowDate = TimeUtils.date2String(date, "yyyy-MM-dd");
            mTvDate.setText(nowDate);
            //更新分钟
            String minute = TimeUtils.date2String(date, "HH:mm");
            mTvHour.setText(minute);//更新分钟
        } else {
            mTvWeek.setText("--");
            mTvDate.setText("0000-00-00");
            mTvHour.setText("--:--");
        }
    }

    public TextView getTvAddress() {
        return mTvAddress;
    }

    public TextView getTvHour() {
        return mTvAddress;
    }

    /**
     * 设置定位数据
     *
     * @param location
     */
    String addr = "";

    /**
     * 更新 mLocation
     *
     * @param location
     */
    public void setMLocation(Location location) {
        mLocation = location;
    }


    public void setTextColor(int textColorId, boolean latlngEnable, boolean addressEnable, boolean timeEnable, boolean descEnable) {
        mTextColor = "#000000";
//        mTvLat.setTextColor(Color.parseColor(mTextColor));
////        mTvLon.setTextColor(Color.parseColor(mTextColor));
//        mTvAddress.setTextColor(Color.parseColor(mTextColor));
//        mTvTime.setTextColor(Color.parseColor(mTextColor));
//        mTvDesc.setTextColor(Color.parseColor(mTextColor));
//        mTvLat.setVisibility(latlngEnable ? View.VISIBLE : View.GONE);
////        mTvLon.setVisibility(latlngEnable ? View.VISIBLE : View.GONE);
//        mTvAddress.setVisibility(addressEnable ? View.VISIBLE : View.GONE);
//        mTvTime.setVisibility(timeEnable ? View.VISIBLE : View.GONE);
//        mTvDesc.setVisibility(descEnable ? View.VISIBLE : View.GONE);
        setDrawingCacheEnabled(false);
    }


    public String getTextColor() {
        return mTextColor;
    }

    public String getAddress() {
        if (TextUtils.isEmpty(currentAddress)) {
            return "";
        }
        return currentAddress;
    }


    public void setAddress(String address) {
        if (TextUtils.isEmpty(address)) {
            currentAddress = "";
        } else {
            currentAddress = address;
        }
        setDrawingCacheEnabled(false);
    }


    /**
     * 获取时间
     *
     * @return
     */
    public long getTime() {
        long currentMills = TimeUtils.getNowMills();
        return currentMills;
    }

}
