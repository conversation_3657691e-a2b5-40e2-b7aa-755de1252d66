package com.business_clean.app.util.pay;

import android.app.Activity;
import android.widget.Toast;

import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.config.Constant;
import com.business_clean.data.initconfig.PayWxDataInfo;
import com.tencent.mm.opensdk.constants.Build;
import com.tencent.mm.opensdk.modelpay.PayReq;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import java.util.HashMap;

/**
 * 微信支付
 */
public class WxPayTool {
    private WxPayCallBack wxPayCallBack;

    public interface WxPayCallBack {
        void NoPaySupported();
    }

    private Activity mActivity;
    private IWXAPI api;

    public WxPayTool(Activity activity, WxPayCallBack wxPayCallBack) {
        mActivity = activity;
        this.wxPayCallBack = wxPayCallBack;
        if (null == api && mActivity != null) {
            //把app注册到微信
            api = WXAPIFactory.createWXAPI(mActivity, Constant.WECHAT_APP_ID);
        }
    }

    public void pay(PayWxDataInfo info) {
        if (info == null || api == null) {
            return;
        }

        // 将该app注册到微信
        api.registerApp(Constant.WECHAT_APP_ID);

        //检查当前手机是否支持微信支付
        boolean isPaySupported = api.getWXAppSupportAPI() >= Build.PAY_SUPPORTED_SDK_INT;
        if (!isPaySupported) {
            Toast.makeText(mActivity, "该手机不支持微信支付", Toast.LENGTH_SHORT).show();
            if (wxPayCallBack != null) {
                wxPayCallBack.NoPaySupported();
            }
            return;
        }

        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("appId", info.getAppid());
        hashMap.put("getPartnerid", info.getMchId());
        hashMap.put("getPrepayid", info.getPrepayId());
        hashMap.put("getNoncestr", info.getNoncestr());
        hashMap.put("getTimestamp", info.getTimestamp());
        hashMap.put("getPackageValue", info.getPackages());
        hashMap.put("getSign", info.getPaySign());

        LogUtils.e("支付的参数--->" + hashMap.toString());

        PayReq req = new PayReq();
        //req.appId = "wxf8b4f85f3a794e77";  // 测试用appId
        req.appId = info.getAppid();
        req.partnerId = info.getMchId();
        req.prepayId = info.getPrepayId();
        req.nonceStr = info.getNoncestr();
        req.timeStamp = info.getTimestamp();
        req.packageValue = info.getPackages();
        req.sign = info.getPaySign();
        req.extData = "app data"; // optional
        // 在支付之前，如果应用没有注册到微信，应该先调用IWXMsg.registerApp将应用注册到微信
        api.sendReq(req);
    }
}
