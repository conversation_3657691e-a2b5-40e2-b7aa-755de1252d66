package com.business_clean.app.weight.camera;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

public class CameraView extends View {
    private static final int STATE_IDLE = 0;
    private static final int STATE_RECORDING = 1;
    private static final int STATE_CAPTURE = 2;

    private int currentState = STATE_IDLE;
    private boolean isLongPress = false;
    private Paint watermarkPaint;
    private String watermarkText;

    public CameraView(Context context) {
        super(context);
        init();
    }

    public CameraView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CameraView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        watermarkPaint = new Paint();
        watermarkPaint.setColor(Color.WHITE);
        watermarkPaint.setTextSize(32);
        watermarkPaint.setAntiAlias(true);
        watermarkText = "Watermark";
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        // 绘制水印
        if (currentState == STATE_CAPTURE) {
            float x = getWidth() / 2; // 水印的X坐标
            float y = getHeight() / 2; // 水印的Y坐标
            canvas.drawText(watermarkText, x, y, watermarkPaint);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                // 开始录制视频
                isLongPress = false;
                startRecording();
                break;
            case MotionEvent.ACTION_UP:
                // 结束录制视频
                if (isLongPress) {
                    stopRecording();
                } else {
                    // 拍照
                    capture();
                }
                break;
            case MotionEvent.ACTION_CANCEL:
                // 取消录制视频
                cancelRecording();
                break;
        }
        return true;
    }

    private void startRecording() {
        isLongPress = true;
        currentState = STATE_RECORDING;
        // 执行录制视频的逻辑
        invalidate();
    }

    private void stopRecording() {
        currentState = STATE_IDLE;
        // 结束录制视频的逻辑
        invalidate();
    }

    private void cancelRecording() {
        currentState = STATE_IDLE;
        // 取消录制视频的逻辑
        invalidate();
    }

    private void capture() {
        currentState = STATE_CAPTURE;
        // 执行拍照的逻辑

        // 绘制水印
        invalidate();
    }
}