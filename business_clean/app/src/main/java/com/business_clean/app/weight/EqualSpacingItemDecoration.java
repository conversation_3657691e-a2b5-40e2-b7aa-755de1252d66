package com.business_clean.app.weight;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 用于LinearLayoutManager模式
 * item 之间的间距
 */
public class EqualSpacingItemDecoration extends RecyclerView.ItemDecoration {
    private int spacing;

    public EqualSpacingItemDecoration(int spacing) {
        this.spacing = spacing;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);

        int position = parent.getChildAdapterPosition(view);

        // 设置item之间的间距
        outRect.bottom = spacing;

        // 第一个item的上边距为 spacing，其他item的上边距为 0
        if (position == 0) {
            outRect.top = spacing;
        } else {
            outRect.top = 0;
        }
    }
}
