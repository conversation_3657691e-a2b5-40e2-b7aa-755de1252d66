package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.TimeUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.business_clean.R;
import com.lxj.xpopup.core.CenterPopupView;

import java.util.Date;

/**
 * 打卡成功要去显示的内容
 */
public class CustomLocationDialog extends CenterPopupView {

    private ImageView ivLocation;

    public CustomLocationDialog(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_location_in;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        ivLocation = findViewById(R.id.iv_dialog_location);

        Glide.with(this)
                .asGif()
                .load(R.mipmap.icon_water_location)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(ivLocation);

        findViewById(R.id.tv_clock_in_confirm).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        if (ivLocation != null) {//如果关闭，那么就清除这个  因为gif 太占用内存了
            Glide.with(this).clear(ivLocation);
        }
    }
}
