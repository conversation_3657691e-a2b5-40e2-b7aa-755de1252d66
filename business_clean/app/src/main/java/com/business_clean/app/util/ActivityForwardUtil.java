package com.business_clean.app.util;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;


import me.hgj.mvvmhelper.base.Ktx;


public class ActivityForwardUtil {

    public static void startActivity(Class toActivityClass) {
        Context context = Ktx.app;
        if (context == null) {
            return;
        }
        Intent intent = new Intent(context, toActivityClass);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
        //解决华为手机8.0以上  页面转场闪烁问题
        if (context instanceof Activity) {
            ((Activity) context).overridePendingTransition(0, 0);
        }
    }

    public static void startActivityAndClearOther(Class toActivityClass, Bundle bundle) {
        Context context = Ktx.app;
        if (context == null) {
            return;
        }
        Intent intent = new Intent(context, toActivityClass);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        intent.putExtras(bundle);
        context.startActivity(intent);
        //解决华为手机8.0以上  页面转场闪烁问题
        if (context instanceof Activity) {
            ((Activity) context).overridePendingTransition(0, 0);
        }
    }

    public static void startActivity(Class toActivityClass, Bundle bundle) {
        Context context = Ktx.app;
        if (context == null) {
            return;
        }
        Intent intent = new Intent(context, toActivityClass);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtras(bundle);
        context.startActivity(intent);
        //解决华为手机8.0以上  页面转场闪烁问题
        if (context instanceof Activity) {
            ((Activity) context).overridePendingTransition(0, 0);
        }
    }

    public static void startActivityForResult(Class toActivityClass, int requestCode) {
        Context context = Ktx.app;

        if (context == null) {
            return;
        }
        Intent intent = new Intent(context, toActivityClass);
        if (context instanceof Activity) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            ((Activity) context).startActivityForResult(intent, requestCode);
            //解决华为手机8.0以上  页面转场闪烁问题
            ((Activity) context).overridePendingTransition(0, 0);
        }
    }

    public static void startActivityForResult(Class toActivityClass, Bundle bundle, int requestCode) {
        Context context = Ktx.app;
        if (context == null) {
            return;
        }
        Intent intent = new Intent(context, toActivityClass);
        intent.putExtras(bundle);
        if (context instanceof Activity) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            ((Activity) context).startActivityForResult(intent, requestCode);
            //解决华为手机8.0以上  页面转场闪烁问题
            ((Activity) context).overridePendingTransition(0, 0);
        }
    }


}
