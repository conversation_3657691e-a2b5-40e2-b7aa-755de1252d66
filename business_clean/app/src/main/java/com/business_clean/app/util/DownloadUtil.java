package com.business_clean.app.util;

import android.Manifest;
import android.content.Context;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Environment;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.business_clean.app.ext.CommonUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 下载文件到外部存储的工具类
 */
public class DownloadUtil {

    private static final Object LOCK = new Object();

    public interface DownloadListener {
        void onDownloadProgress(int progress);

        void onDownloadSuccess(File file);

        void onDownloadFailed(Exception e);
    }

    public static void downloadFile(Context context, String url, String suffix, String customFileName, boolean useLocalFile, DownloadListener listener) {
        new DownloadTask(context, url, suffix, customFileName, useLocalFile, listener).execute();
    }

    private static class DownloadTask extends AsyncTask<Void, Integer, File> {
        private String mUrl;
        private String mSuffix;
        private String mCustomFileName;
        private boolean mUseLocalFile;
        private DownloadListener mListener;
        private Context mContext;

        DownloadTask(Context context, String url, String suffix, String customFileName, boolean useLocalFile, DownloadListener listener) {
            mContext = context;
            mUrl = url;
            mSuffix = suffix;
            mCustomFileName = customFileName;
            mUseLocalFile = useLocalFile;
            mListener = listener;
        }

        @Override
        protected File doInBackground(Void... params) {
            File file = null;
            try {
                // 如果允许使用本地文件,先检查是否存在
                if (mUseLocalFile) {
                    file = getLocalFile(mUrl, mSuffix);
                    if (file != null && file.exists()) {
                        Log.e("下载", "是本地文件");
                        return file;
                    }
                }

                // 下载文件
                file = downloadFileFromUrl(mUrl, mSuffix);
            } catch (IOException e) {
                e.printStackTrace();
                return null; // 返回 null 表示下载失败
            }
            return file;
        }

        @Override
        protected void onProgressUpdate(Integer... values) {
            super.onProgressUpdate(values);
            mListener.onDownloadProgress(values[0]);
        }

        @Override
        protected void onPostExecute(File file) {
            super.onPostExecute(file);
            if (file != null) {
                mListener.onDownloadSuccess(file);
            } else {
                mListener.onDownloadFailed(new Exception("Download failed"));
            }
        }

        private File getLocalFile(String url, String suffix) {
            // 构建本地文件路径
            return new File(Environment.getExternalStorageDirectory(), getFileName(url, mCustomFileName));
        }

        private File downloadFileFromUrl(String url, String suffix) throws IOException {
            // 下载文件并保存到外部存储的公共 Downloads 目录
            String savePath = CommonUtils.getSavePath();
            File targetFile = new File(savePath, getFileName(url, mCustomFileName));

            // 加锁以保证线程安全
            synchronized (LOCK) {
                URL downloadUrl = new URL(url);
                HttpURLConnection connection = (HttpURLConnection) downloadUrl.openConnection();
                connection.connect();

                int fileLength = connection.getContentLength();

                try (InputStream input = connection.getInputStream();
                     OutputStream output = new FileOutputStream(targetFile)) {
                    byte[] buffer = new byte[4096];
                    int count;
                    int downloaded = 0;
                    while ((count = input.read(buffer)) != -1) {
                        output.write(buffer, 0, count);
                        downloaded += count;
                        publishProgress((int) (downloaded * 100 / fileLength));
                    }
                }
            }

            return targetFile;
        }


        private String getFileName(String url, String customFileName) {
            String fileSuffix = getFileSuffixFromUrl(url);
            return customFileName + fileSuffix;
        }

        private String getFileNameFromUrl(String url) {
            return Uri.parse(url).getLastPathSegment();
        }

        private String getFileSuffixFromUrl(String url) {
            String fileName = getFileNameFromUrl(url);
            int dotIndex = fileName.lastIndexOf(".");
            if (dotIndex != -1) {
                return fileName.substring(dotIndex);
            }
            return "";
        }
    }
}