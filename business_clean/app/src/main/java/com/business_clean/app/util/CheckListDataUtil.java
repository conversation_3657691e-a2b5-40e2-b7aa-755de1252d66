package com.business_clean.app.util;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.business_clean.R;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;

import java.util.Collection;
import java.util.List;

public class CheckListDataUtil {


    private static View emptyView;

    public static boolean checkInitData(Collection<?> datas, BaseQuickAdapter adapter, SmartRefreshLayout refreshLayout) {
        if (adapter != null) {
            adapter.removeAllFooterView();
        }

        boolean result = false; // 用于指定数据是否是OK的
        if (datas == null || datas.isEmpty()) {
            showErrorView(refreshLayout.getContext(), adapter, "暂无数据", 0);
        } else {
            result = true;
        }
        if (refreshLayout != null) {
            refreshLayout.finishRefresh();
            if (datas == null || datas.size() < 20) {
                refreshLayout.setEnableLoadMore(false);
//                addFooter(refreshLayout.getContext(), adapter);
            } else {
                refreshLayout.setEnableLoadMore(true);
            }
        }
        return result;
    }


    public static boolean checkInitData(Collection<?> datas, BaseQuickAdapter adapter, SmartRefreshLayout refreshLayout, String emptyMsg, int icon_data) {
        if (adapter != null) {
            adapter.removeAllFooterView();
        }

        boolean result = false; // 用于指定数据是否是OK的
        if (datas == null || datas.isEmpty()) {
            showErrorView(refreshLayout.getContext(), adapter, emptyMsg, icon_data);
        } else {
            result = true;
        }
        if (refreshLayout != null) {
            refreshLayout.finishRefresh();
            if (datas == null || datas.size() < 20) {
                refreshLayout.setEnableLoadMore(false);
                addFooter(refreshLayout.getContext(), adapter);
            } else {
                refreshLayout.setEnableLoadMore(true);
            }
        }
        return result;
    }

    public static boolean checkLoadMoreData(List<?> datas, BaseQuickAdapter adapter, SmartRefreshLayout refreshLayout) {
        if (refreshLayout != null) {
            refreshLayout.finishLoadMore();
        }
        if (adapter != null) {
            adapter.removeAllFooterView();
        }
        boolean result = false; // 用于指定数据是否是OK的
        if (datas == null || datas.isEmpty()) {
            ToastUtil.show("没有更多数据了...");
            addFooter(refreshLayout.getContext(), adapter);
            if (refreshLayout != null) {
                refreshLayout.setEnableLoadMore(false);
            }
        } else {
            result = true;
        }
        return result;
    }

    public static void showErrorView(Context context, BaseQuickAdapter adapter, String msg, int icon_data) {
        if (TextUtils.isEmpty(msg)) {
            msg = "暂无数据";
        }
        setAdapterView(context, adapter, msg, icon_data, false, 2);
    }

    public static void setAdapterView(Context context, BaseQuickAdapter adapter, String msg, int imgResId, boolean isLoad, int type) {
        if (emptyView == null) {
            emptyView = View.inflate(context, R.layout.item_empty_view, null);
        }
        ((TextView) emptyView.findViewById(R.id.tv_notice)).setText(msg);
        if (imgResId == 0) {
            ((ImageView) emptyView.findViewById(R.id.iv_empty_icon)).setVisibility(View.VISIBLE);
            ((ImageView) emptyView.findViewById(R.id.iv_empty_icon)).setImageResource(R.mipmap.icon_empty);
            ((TextView) emptyView.findViewById(R.id.tv_notice)).setTextColor(ContextCompat.getColor(context, R.color.base_primary_text_body));
            ((TextView) emptyView.findViewById(R.id.tv_notice)).setTextSize(15);
        } else {
            ((ImageView) emptyView.findViewById(R.id.iv_empty_icon)).setVisibility(View.VISIBLE);
            ((ImageView) emptyView.findViewById(R.id.iv_empty_icon)).setImageResource(imgResId);
        }

        if (type == 1) {
            emptyView.findViewById(R.id.tv_sub_notice).setVisibility(View.GONE);
            emptyView.findViewById(R.id.qumi_refresh).setVisibility(View.GONE);
        } else {
            emptyView.findViewById(R.id.tv_sub_notice).setVisibility(View.GONE);
            emptyView.findViewById(R.id.qumi_refresh).setVisibility(View.GONE);
        }

        adapter.getData().clear();
        if (emptyView.getParent() != null) {
            ((ViewGroup) emptyView.getParent()).removeView(emptyView);
        }
        adapter.setEmptyView(emptyView);
        adapter.notifyDataSetChanged();
    }

    private static void addFooter(Context context, BaseQuickAdapter adapter) {
        if (adapter == null) {
            return;
        }
        adapter.removeAllFooterView();
        View inflate = View.inflate(context, R.layout.layout_list_footer, null);
        adapter.addFooterView(inflate);
    }
}
