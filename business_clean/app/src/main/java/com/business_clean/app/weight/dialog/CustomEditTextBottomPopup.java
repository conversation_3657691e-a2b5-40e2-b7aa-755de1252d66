package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.view.View;
import android.widget.EditText;

import androidx.annotation.NonNull;

import com.business_clean.R;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.callback.OnDialogEditTextBottomListener;
import com.lxj.xpopup.core.BottomPopupView;

/**
 * Description: 自定义带有输入框的Bottom弹窗
 */
public class CustomEditTextBottomPopup extends BottomPopupView {

    private OnDialogEditTextBottomListener listener;

    private EditText editText;

    private String editString;

    public CustomEditTextBottomPopup(@NonNull Context context, String editString, OnDialogEditTextBottomListener listener) {
        super(context);
        this.editString = editString;
        this.listener = listener;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.custom_edittext_bottom_popup;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        editText = findViewById(R.id.et_dialog_comment);
        if (editText != null) {
            editText.setText(editString);
            // 将光标移动到文本的末尾
            editText.setSelection(editString.length());
        }

        findViewById(R.id.tv_dialog_edittext_bottom_clean).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (listener != null) {
                    listener.onDialogEditTextBottom(null);
                }
            }
        });

        findViewById(R.id.tv_dialog_edittext_bottom_search).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (listener != null) {
                    if (editText != null) {
                        listener.onDialogEditTextBottom(editText.getText().toString());
                    } else {
                        listener.onDialogEditTextBottom(null);
                    }
                }
            }
        });
    }

    @Override
    protected void onShow() {
        super.onShow();
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
    }

}
