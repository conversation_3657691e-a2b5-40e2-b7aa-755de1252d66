package com.business_clean.app.weight.dialog;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import com.business_clean.R;
import com.business_clean.app.util.share.ShareHelperTools;
import com.business_clean.app.util.share.ShareParams;
import com.business_clean.app.util.share.ShareType;
import com.business_clean.ui.activity.custom.ContractSignWebActivity;
import com.lxj.xpopup.core.CenterPopupView;


/**
 * 合同分享签署的合同
 */
public class ContractSharePopup extends CenterPopupView {

    private Context mContext;

    private String url;

    private SharePopupListener listener;

    public ContractSharePopup(@NonNull Context context, String url, SharePopupListener listener) {
        super(context);
        this.mContext = context;
        this.url = url;
        this.listener = listener;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_contract_share;
    }

    @Override
    protected void onCreate() {
        super.onCreate();

        findViewById(R.id.dialog_tv_cancel).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
//                AppExtKt.finishActivity(ContractSignWebActivity.class);
//                AppExtKt.finishActivity(AddProjectActivity.class);
//                AppExtKt.finishActivity(QuickDepartActivity.class);
//                AppExtKt.finishActivity(SelectPersonnelActivity.class);
                if (listener != null) {
                    listener.onXpopupDismiss();
                }
            }
        });

        findViewById(R.id.dialog_tv_again).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareParams params = new ShareParams();
                params.setShareType(ShareType.WEIXIN);
                params.setLinkUrl(url);
                params.setTitle("签署电子合同");
                ShareHelperTools.getInstance().shareCardLink(params, (Activity) mContext);
            }
        });

        findViewById(R.id.dialog_tv_wx_again).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareParams params = new ShareParams();
                params.setShareType(ShareType.WWEIXIN);
                params.setWWeiXinDescription(url);
                params.setTitle("签署电子合同");
                ShareHelperTools.getInstance().shareCardLink(params, (Activity) mContext);
            }
        });
    }


    @Override
    protected void onShow() {
        super.onShow();
        Log.e("tag", "PagerDrawerPopup onShow");
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        Log.e("tag", "PagerDrawerPopup onDismiss");
    }


    public interface SharePopupListener {
        void onXpopupDismiss();
    }
}

