package com.business_clean.app.base

import android.app.Application
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.databinding.ViewDataBinding
import com.business_clean.R
import com.business_clean.app.App
import com.business_clean.app.weight.CustomTabBar
import com.gyf.immersionbar.ImmersionBar
import me.hgj.mvvmhelper.base.BaseVBActivity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.logE
import org.lzh.framework.updatepluginlib.util.L


abstract class BaseActivity<VM : BaseViewModel, DB : ViewDataBinding> : BaseVBActivity<VM, DB>() {

    lateinit var mToolbar: CustomTabBar

    override fun getTitleBarView(): View? {
        val titleBarView = LayoutInflater.from(this).inflate(R.layout.layou_titilebar_view, null)
        mToolbar = titleBarView.findViewById(R.id.custom_tabbar)
        return titleBarView
    }

    abstract override fun initView(savedInstanceState: Bundle?)

    override fun onBindViewClick() {

    }

    //设置共同沉浸式样式 但是特殊的Fragment 以及 界面都需要单独设置，请看MainActivity 下的Fragment的沉浸式
    override fun initImmersionBar() {
        if (showToolBar()) {
            ImmersionBar.with(this)
                .statusBarColor(R.color.white)
                .navigationBarColor(R.color.white)
                .titleBar(mToolbar)
                .statusBarDarkFont(true) //状态栏字体是深色，不写默认为亮色
                .init()
        }
    }


}