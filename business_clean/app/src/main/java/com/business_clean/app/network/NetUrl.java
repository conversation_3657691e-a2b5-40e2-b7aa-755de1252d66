package com.business_clean.app.network;

import rxhttp.wrapper.annotation.DefaultDomain;

public class NetUrl {
    public static String SERVER_URL_RELEASE = "https://pccappapi.jiazhengye.cn/";
    public static String SERVER_URL_TEST = "https://pccappapi-test.jiazhengye.cn/";
    public static String SERVER_URL_DEV = "https://pccappapi-dev.jiazhengye.cn/";


    //web 的域名

    public static String WEB_BASE_DEV_URL = "https://pccm-dev.jiazhengye.cn/";
    public static String WEB_BASE_TEST_URL = "https://pccm-test.jiazhengye.cn/";
    public static String WEB_BASE_RELEASE_URL = "https://pccm.jiazhengye.cn/";

    public static String WEB_BASE_DEV_URL_M2 = "https://m2-dev.jiazhengye.cn";
    public static String WEB_BASE_TEST_URL_M2 = "https://m2-test.jiazhengye.cn";
    public static String WEB_BASE_RELEASE_URL_M2 = "https://m2.jiazhengye.cn";


    public static String WEB_BASE_URL = WEB_BASE_RELEASE_URL;//默认是 RELEASE


    //待办的链接
    public static String WEB_TODO_DETAIL_URL = "examine?";
    //项目的链接
    public static String WEB_PROJECT_DETAIL_URL = "project?";
    //档案的链接
    public static String WEB_PROJECT_FILE_DETAIL_URL = "files?";

    //清洁报告
    public static String WEB_PROJECT_CLEAN_REPORT_URL = "report/detail?";

    @DefaultDomain
    public static String defaultUrl = SERVER_URL_RELEASE;

    public static String AUDIO_URL = "https://pccmedia.jiazhengye.cn/";

    //源数据

    public static String INIT_CITY = "/data/city/getTree";
    //阿里一键授权
    public static String GET_ALI_AUTH_TOKEN = "/auth/oneClickLogin/getAccessToken";

    //阿里的授权登陆
    public static String GET_ALI_SING_IN = "/auth/oneClickLogin/signIn";
    //确定企业
    public static String SELECT_COMPANY = "/auth/login/selectCompany";

    //图片标记的接口
    public static String MARK_PHOTO = "/user/workCircle/create";
    //图片拍照的接口
    public static String MARK_CAMERA_PHOTO = "/user/workCircle/photo";
    //图片集体打卡的接口
    public static String MARK_TEAM_PHOTO = "/kq/clockIn/team";

    //验证码
    public static String GET_LOGIN_CODE = "/auth/login/getVerificationCode";

    //登陆账号
    public static String LOGIN_SIGN_IN = "/auth/login/signIn";

    //获取首页
    public static String GET_WORK_BENCH_GRID = "/workbench/index/getApplicationList";

    // 获取首页统计
    public static String GET_WORK_BENCH_TO_GRID = "/workbench/index/getDataNew";

    // Todo列表
    public static String GET_TODO_LIST = "/user/approve/getList";

    // todo同意
    public static String TODO_AGREE = "/user/approve/agree";

    //todo取消
    public static String TODO_CANCEL = "/user/approve/cancel";

    //todo撤回
    public static String TODO_REJECT = "/user/approve/reject";
    //todo删除
    public static String TODO_DELETE = "/user/approve/delete";

    //审批抄送已读设置
    public static String COPY_READ_SET = "/user/approve/setCopyRead";

    //后去源数据
    public static String GET_INIT_DATA = "/data/metadata/getList";

    //工作圈
    public static String GET_CIRCLE_LIST = "/user/workCircle/getList";

    //工作圈子统计
    public static String GET_CIRCLE_STAT = "/user/workCircle/statDay";

    //员工册
    public static String GET_ROSTER_LIST = "/user/archives/getList";

    //获取休息人员的列表
    public static String GET_CAN_OVERTIME_HOLIDAY_LIST = "/user/archives/getCanOvertimeHolidayList";

    //获取审批单子的详情
    public static String GET_DEPART_GET_ONE = "/user/approve/getOne";

    //删除客户UUID
    public static String CUSTOM_DELETE = "/company/custom/delete";
    ///审批列表
    public static String GET_APPLY_TEMPLATE_LIST = "/user/approve/getApplyTemplateList";

    //获取用户管理的审批管理的审批模版列表
    public static String GET_MANAGER_TEMPLATE_LIST = "/user/approve/getManageTemplateList";

    //花名册
    public static String GET_ROSTER_PERSON_LIST = "/user/archives/getPersonList";

    //客户列表
    public static String GET_CUSTOM_LIST = "/company/custom/getList";

    //创建客户信息
    public static String SAVE_CUSTOM = "/company/custom/save";

    //获取全部项目列表
    public static String GET_PROJECT_MANGER_ALL = "/project/project/getAll";

    //获取项目列表
    public static String GET_PROJECT_MANGER_LIST = "/project/project/getList";

    //获取客户的详情
    public static String GET_CUSTOM_DETAILS = "/company/custom/getOne";


    //获取项目详情
    public static String GET_PROJECT_MANGER_DETAIL = "/project/project/getEditDetail";


    //保存项目
    public static String SAVE_COMPANY_PROJECT = "/project/project/save";

    //获取业态
    public static String GET_CAT_ALL = "/data/project/getCatAll";

    //获取七牛token
    public static String GET_QINIU_TOKEN = "/data/oss/getQiniuToken";


    //常用联系人
    public static String GET_CONTACTA_ALL = "/project/contactPerson/getList";
    //删除
    public static String DEL_CONTACTC = "/project/contactPerson/delete";
    //新增联系人
    public static String SAVE_CONTACTA = "/project/contactPerson/save";

    //新增总部人员
    public static String SAVE_COMPANY_CONTACTA = "/user/archives/saveHeadOffice";
    //获取详情
    public static String GET_CONTACTA_DETAIL = "/project/contactPerson/getOne";
    //获取总部人员详情
    public static String GET_COMPANY_CONTACTA_DETAIL = "/user/archives/getOneHeadOffice";

    //考勤规则列表
    public static String GET_GROUP_ALL = "/project/group/getList";
    public static String SAVE_GROUP = "/project/group/save";
    public static String DEL_GROUP = "/project/group/delete";
    public static String GET_GROUP_DETAIL = "/project/group/getOne";

    //班次
    public static String GET_CLASSES_ALL = "/project/classes/getList";
    public static String SAVE_CLASSES = "/project/classes/save";
    public static String DEL_CLASSES = "/project/classes/delete";
    public static String GET_CLASSES_DETAIL = "/project/classes/getOne";
    ///现场照片删除
    public static String DELETE_WORK_CIRILE_PHOTO = "/user/workCircle/delete";

    //地址
    public static String GET_ADDRESS_ALL = "/project/workAddress/getList";

    //获取企业下的
    public static String GET_COMPANY_ADDRESS_ALL = "/company/workAddress/getList";
    public static String SAVE_ADDRESS = "/project/workAddress/save";
    public static String DEL_ADDRESS = "/project/workAddress/delete";
    public static String GET_ADDRESS_DETIALI = "/project/workAddress/getOne";
    ///逆地理编码
    public static String GET_ADDRESS_REGEO = "/data/map/regeo";

    //花名册-增加员工相册
    public static String ROSTER_ADD = "/user/archives/saveHeadOffice";

    //考勤 月
    public static String GET_ATTENDANCE_MONTH = "/attendance/stat/getMonthList";

    //考勤 日
    public static String GET_ATTENDANCE_DAY = "/attendance/stat/getDailyList";
    public static String UPDATE_MY_ATTENDANCE = "/attendance/daily/update";

    //获取岗位列表
    public static String JOB_LIST = "/company/job/getAll";
    //带分页的岗位列表
    public static String JOB_LIST_PAGE = "/company/job/getList";
    //切换项目列表
    public static String CHANGE_PROJECT = "/user/project/change";
    //获取当前时间戳
    public static String GET_TIME_STAMP = "/data/metadata/getTimeStamp";
    //高德的key
    public static String GET_A_MAP_KEY = "/data/map/getSdkKey";
    //获取个人信息
    public static String GET_USER_INFO = "/user/archives/getPersonalInfo";
    //请求民族列表
    public static String GET_NATION_LIST = "/data/nation/getList";

    //增加项目成员
    public static String CREATE_MEMBERS = "/user/archivesDraft/create";
    //编辑草稿成员
    public static String UPDATE_MEMBERS = "/user/archivesDraft/update";
    //编辑已存在的成员
    public static String UPDATE_MEMBERS_UPDATE = "/user/archives/update";
    //获取草稿详情
    public static String GET_MEMBERS_DETAILS = "/user/archivesDraft/getOne";
    //获取员工详情
    public static String GET_STAFF_DETAILS = "/user/archives/getOne";

    //相机员工设置
    public static String CAMERA_SETTING = "/user/setting/update";

    //获取默认的出勤的信息
    public static String CAMERA_ATTENDANCE_INFO = "/user/attendance/getCurrentInfo";

    //更新版本
    public static String CHECK_UPDATE_APP = "data/version/getLatestOne";

    //获取相册分享的Url
    public static String GET_MY_PHOTOS_SHARE_URL = "/user/workCircle/getSharePhotoUrl";

    //身份证识别
    public static String OCR_ID_CARD_INFO = "/data/ocr/idCardRecognition";
    //银行卡识别
    public static String OCR_BANK_INFO = "/data/ocr/bankCardRecognition";
    //员工快速离职
    public static String DEPOART_MEMBERS = "/user/approve/createDimission";

    //我的考勤
    public static String MY_ATTENDANCE = "/kq/stat/getUserMonth";

    //当日考勤
    public static String MY_ATTENDANCE_DAYS = "/kq/daily/getClockInList";
    //导出 xls 文件内容
    public static String DOWNLOAD_EXPORT_EXCEL = "/data/export/excel";

    //获取所有的角色权限
    public static String GET_PERMISSIONS = "/company/role/getAll";

    //查询当前地址和时间是否可打卡
    public static String CHECK_CLOCK_ADDRESS = "/attendance/clockIn/check";

    //获取用户管理的所有项目列表
    public static String GET_DEPART_GET_PROJECT_ALL = "/user/project/getAll";

    //获取用工规则配置
    public static String GET_WORK_POST_RULES = "/company/setting/getOne";
    //信用查询
    public static String GET_USER_CREDIT_SEARCH = "/user/credit/search";

    //购买电子合同
    public static String CREATE_PAY_ORDER = "/company/contractOrder/create";


    //获取合同账户信息 获取合同信息
    public static String CREATE_CONTRACT_INFO = "/contract/contractCustomer/getOne";

    //获取合同的模版列表
    public static String CREATE_CONTRACT_LIST = "/contract/contractTemplate/getList";

    //获取合同标签详情
    public static String CREATE_CONTRACT_TAG_ONE_LIST = "/contract/contractLabel/getOne";

    //获取合同 分享签署链接
    public static String CREATE_CONTRACT_SIGN_INFO = "/contract/contract/shareExtSign";

    //如果没有customer_id 注册三方账户
    public static String CREATE_CONTRACT_REGISTER = "/contract/contractCustomer/register";

    //创建电子合同
    public static String CREATE_CONTRACT = "/contract/contract/create";

    //获取余额现金充值接口
    public static String CREATE_CASH_BALANCE_RECHARGE = "/company/balance/createCashRechargeOrder";

    //获取账户的余额
    public static String CREATE_CASH_BALANCE = "/company/balance/getOne";

    //获取计划部分的内容
    public static String GET_PLAN_LIST_ALL = "/project/plan/getTaskList";
    //获取代办的总数
    public static String GET_TODO_TOTAL = "/user/archives/getTodoTotal";

    //获取任务详情
    public static String GET_TASK_ONE = "/project/plan/getOneTask";

    //任务结果图删除
    public static String GET_TASK_DEL = "/project/plan/deleteTaskDealPic";


    //任务结果图增加
    public static String GET_TASK_ADD_PIC = "/project/plan/addTaskDealPic";

    //获取今日待办任务
    public static String GET_TODAY_TASK = "/user/todo/getTodayTask";

    ///任务完成接口
    public static String GET_TODAY_FINISH_TASK = "/project/plan/finishTask";

    //纸质合同添加/编辑
    public static String SAVE_USER_CONTRACT = "/user/paperContract/save";
    //纸质合同添加/编辑 详情
    public static String SAVE_USER_CONTRACT_ONE = "/user/paperContract/getOne";

    //首页工作台的统计接口
    public static String GET_STAT_DATA = "/project/project/getStatData";

    //项目详情
    public static String GET_PROJECT_ONE = "/project/project/getOne";

    //获取员工打卡范围
    public static String GET_CLOCK_RADIUS = "/user/attendance/getClockInRadius";

    //获取集体打卡的班次列表
    public static String GET_TEAM_CLASS_LIST = "/kq/class/getList";

    //获取集体打卡的人员列表
    public static String GET_TEAM_CLASS_USER_LIST = "/kq/class/getUserList";
    //删除考勤的修改记录
    public static String DELETE_MY_ATTEDANCE_LOG = "/kq/daily/deleteUpdateClockRecord";
    //修改考勤的状态
    public static String MY_ATTENDANCE_UPDATE_RECORD = "/kq/daily/updateSegmentClockRecord";
    ///修改班次
    public static String MY_ATTENDANCE_UPDATE_CLASS = "/kq/workSchedule/update";
    ///重置考勤结果
    public static String MY_ATTENDANCE_RESET_ATTENDANCE = "/kq/daily/clearUpdateClockRecord";

    //删除项目
    public static String COMPANY_PROJECT_DELETE = "/project/project/delete";
    // 删除档案
    public static String DELETE_COMPANY_STAFF = "/user/archives/delete";
    ///刷新token
    public static String REGRESH_TOKEN = "/auth/token/refresh";
    //身份证校验
    public static String CHECK_ID_NUMBER = "/user/archivesDraft/checkIdNumber";
    ///草稿档案删除
    public static String DELETE_USER_ARCHIVEES_DRAFT = "/user/archivesDraft/delete";
    //身份证二要素校验
    public static String CHECK_ID_NUMBER_AUTH = "/data/user/checkIdNumber";
    ///获取企业总部项目
    public static String GET_ONE_HEAD_OFFICE = "/project/project/getOneHeadOffice";


}
