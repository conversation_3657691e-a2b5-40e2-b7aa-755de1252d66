package com.business_clean.app.weight.recycler;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

public class GalleryRecyclerView extends RecyclerView {
    /**
     * 相邻卡片之间的间隔
     */
    private int mIntervalDistance = 0;
    private float mSelectedScale = 0.0f;


    private static final int FLING_MAX_VELOCITY = 8000; // 最大顺时滑动速度


    public GalleryRecyclerView(@NonNull Context context) {
        super(context);
    }

    public GalleryRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public GalleryRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public int getIntervalDistance() {
        return mIntervalDistance;
    }

    @Override
    public boolean drawChild(Canvas canvas, View child, long drawingTime) {
        int childWidth = child.getWidth() - child.getPaddingLeft() - child.getPaddingRight();
        int childHeight = child.getHeight() - child.getPaddingTop() - child.getPaddingBottom();
        int width = getWidth();
        if (width <= child.getWidth()) {
            return super.drawChild(canvas, child, drawingTime);
        }
        int pivot = (width - childWidth) / 2;
        int x = child.getLeft();
        float scale, alpha;
        alpha = 1 - 0.6f * Math.abs(x - pivot) / pivot;
        if (x <= pivot) {
            scale = 2 * (1 - mSelectedScale) * (x + childWidth) / (width + childWidth) + mSelectedScale;
        } else {
            scale = 2 * (1 - mSelectedScale) * (width - x) / (width + childWidth) + mSelectedScale;
        }
        child.setPivotX(childWidth / 2);
        child.setPivotY(childHeight / 2);
//        child.setScaleX(scale);//设置缩放
//        child.setScaleY(scale);
//        child.setAlpha(alpha);//设置透明度
        return super.drawChild(canvas, child, drawingTime);
    }


    @Override
    public boolean fling(int velocityX, int velocityY) {
        velocityX = solveVelocity(velocityX);
        velocityY = solveVelocity(velocityY);
        return super.fling(velocityX, velocityY);
    }

    private int solveVelocity(int velocity) {
        if (velocity > 0) {
            return Math.min(velocity, FLING_MAX_VELOCITY);
        } else {
            return Math.max(velocity, -FLING_MAX_VELOCITY);
        }
    }
}
