package com.business_clean.app.util;

public class ResultDouble<R1, R2> extends ResultSingle<R1> {
    public final R2 returnValue2;

    public ResultDouble(R1 returnValue1, R2 returnValue2) {
        super(returnValue1);
        this.returnValue2 = returnValue2;
    }

    @Override
    public String toString() {
        return super.toString() + " , " + (returnValue2 != null ? returnValue2.toString() : "Value2 = null");
    }
}
