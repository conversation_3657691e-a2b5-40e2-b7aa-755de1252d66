package com.luck.picture.lib.decoration;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

/**
 * @author：luck
 * @data：2016/12/27 下午23:50
 * @describe:GridSpacingItemDecoration
 */

public class MyGridSpacingItemDecoration extends RecyclerView.ItemDecoration {

    private int spanCount;
    private int spacing;
    private boolean includeEdge;
    private boolean isTopBottom = true;

    public MyGridSpacingItemDecoration(int spanCount, int spacing, boolean includeEdge) {
        this.spanCount = spanCount;
        this.spacing = spacing;
        this.includeEdge = includeEdge;
    }

    public MyGridSpacingItemDecoration(int spanCount, int spacing, boolean includeEdge, boolean isTopBottom) {
        this.spanCount = spanCount;
        this.spacing = spacing;
        this.includeEdge = includeEdge;
        this.isTopBottom = isTopBottom;
    }


    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        int column = position % spanCount;
        if (includeEdge) {
            outRect.left = spacing - column * spacing / spanCount;
            outRect.right = (column + 1) * spacing / spanCount;
            if (isTopBottom) {
                if (position < spanCount) {
                    outRect.top = spacing;
                }
                outRect.bottom = spacing;
            }

        } else {
            outRect.left = column * spacing / spanCount;
            outRect.right = spacing - (column + 1) * spacing / spanCount;
            if (isTopBottom) {
                if (position < spanCount) {
                    outRect.top = spacing;
                }
                outRect.bottom = spacing;
            }
        }
    }
}