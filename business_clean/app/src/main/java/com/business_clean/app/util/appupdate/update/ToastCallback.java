package com.business_clean.app.util.appupdate.update;

import android.text.TextUtils;

import com.business_clean.app.util.ToastUtil;

import org.lzh.framework.updatepluginlib.base.CheckCallback;
import org.lzh.framework.updatepluginlib.base.DownloadCallback;
import org.lzh.framework.updatepluginlib.model.Update;

import java.io.File;

import me.hgj.mvvmhelper.ext.LogExtKt;


/**
 * <AUTHOR> on 2018/1/9.
 */

public class ToastCallback implements CheckCallback, DownloadCallback {


    public ToastCallback() {
    }

    private void show(String message) {
        ToastUtil.show(message);
    }

    @Override
    public void onCheckStart() {
//        show("启动更新任务");
    }

    @Override
    public void hasUpdate(Update update) {
//        show("检测到有更新");
    }

    @Override
    public void noUpdate() {
//        show("检测到没有更新");
    }

    @Override
    public void onCheckError(Throwable t) {
//        show("更新检查失败：" + t.getMessage());
    }

    @Override
    public void onUserCancel() {
//        show("用户取消更新");
    }

    @Override
    public void onCheckIgnore(Update update) {
//        show("用户忽略此版本更新");
    }

    @Override
    public void onDownloadStart() {
        show("开始下载");
    }

    @Override
    public void onDownloadComplete(File file) {
        show("下载完成");
    }

    @Override
    public void onDownloadProgress(long current, long total) {

    }

    @Override
    public void onDownloadError(Throwable t) {
        //java.lang.RuntimeException: You can not download the same file using multiple download tasks simultaneously，the file path is /storage/emulated/0/Android/data/cn.jiazhengye.panda_home/cache/update/update_normal_5.3.5
        LogExtKt.logI("------onDownloadError-----" + t, "");
        if (!TextUtils.isEmpty(t.getMessage()) && t.getMessage().contains("multiple download tasks simultaneously")) {
            show("新版本已经在下载中，下载完成后会弹窗安装，请耐心等待哦");
        } else {
            show("下载失败");
        }
    }
}
