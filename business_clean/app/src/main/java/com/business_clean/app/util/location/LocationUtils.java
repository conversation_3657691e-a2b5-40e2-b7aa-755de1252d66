package com.business_clean.app.util.location;


import android.content.Context;
import android.location.LocationManager;
import android.provider.Settings;
import android.widget.Toast;

public class LocationUtils {

    public static final String LOCATION_MODE_CHANGE_ACTION = "android.location.MODE_CHANGED";


    public static boolean isLocationServiceEnabled(Context context) {
        try {
            int locationMode = Settings.Secure.getInt(
                    context.getContentResolver(), Settings.Secure.LOCATION_MODE);

            return locationMode != Settings.Secure.LOCATION_MODE_OFF;
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
            return false;
        }
    }
    public static void showLocationStatusToast(Context context, boolean isEnabled) {
        Toast.makeText(context,
                isEnabled ? "定位服务已开启" : "定位服务已关闭",
                Toast.LENGTH_LONG).show();
    }
}
