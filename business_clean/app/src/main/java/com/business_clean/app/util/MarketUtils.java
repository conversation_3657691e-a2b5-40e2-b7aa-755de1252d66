package com.business_clean.app.util;

import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.LogUtils;

public class MarketUtils {

    private static MarketUtils tools;
    private static final String schemaUrl = "market://details?id=";

    public static MarketUtils getTools() {
        if (null == tools) {
            tools = new MarketUtils();
        }
        return tools;
    }

    /***
     /* 不指定包名
     /* @param mContext
     */
    public boolean startMarket(Context mContext) {
        String packageName = mContext.getPackageName();//得到包名
        return startMarket(mContext, packageName);
    }

    /**
     * /* 指定包名
     * <p>
     * /*
     * <p>
     * /* @param mContext
     * <p>
     * /* @param packageName
     */
    public boolean startMarket(Context mContext, String packageName) {
        try {
            String deviceBrand = getDeviceBrand();//获得手机厂商
            //根据厂商获取对应市场的包名
            String brandName = deviceBrand.toUpperCase();//大写
            if (TextUtils.isEmpty(brandName)) {
                LogUtils.e("没有读取到手机厂商~~");
                return false;
            }
            String marketPackageName = getBrandName(brandName);
            LogUtils.e("marketPackageName:" + marketPackageName);
            LogUtils.e("brandName:" + brandName);
            LogUtils.e("deviceBrand:" + deviceBrand);
            if (null == marketPackageName || "".equals(marketPackageName)) {
                return false;
            }
            if (marketPackageName.equals(MarketUtils.PACKAGE_NAME.SAMSUNG_PACKAGE_NAME)) {
                //三星特殊处理
                Uri uri = Uri.parse("http://www.samsungapps.com/appquery/appDetail.as?appId=" + packageName);
                Intent goToMarket = new Intent();
                goToMarket.setClassName("com.sec.android.app.samsungapps", "com.sec.android.app.samsungapps.Main");
                goToMarket.setData(uri);
                try {
                    mContext.startActivity(goToMarket);
                    return true;
                } catch (ActivityNotFoundException e) {
                    return false;
                }
            } else {
                return startMarket(mContext, packageName, marketPackageName);
            }
        } catch (ActivityNotFoundException anf) {
            LogUtils.e("要跳转的应用市场不存在!" + anf.getMessage());
        } catch (Exception e) {
            LogUtils.e("其他错误：" + e.getMessage());
        }
        return false;
    }

    /***
     /* 指定包名，指定市场
     /* @param mContext
     /* @param packageName
     /* @param marketPackageName
     */
    public boolean startMarket(Context mContext, String packageName, String marketPackageName) {
        try {
            openMarket(mContext, packageName, marketPackageName);
            return true;
        } catch (ActivityNotFoundException anf) {
            LogUtils.e("要跳转的应用市场不存在!" + anf.getMessage());
            return false;
        } catch (Exception e) {
            LogUtils.e("其他错误：" + e.getMessage());
            return false;
        }
    }

    /***
     /* 打开应用市场
     /* @param mContext
     /* @param packageName
     /* @param marketPackageName
     */
    private boolean openMarket(Context mContext, String packageName, String marketPackageName) {
        try {
            Uri uri = Uri.parse(schemaUrl + packageName);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            intent.setPackage(marketPackageName);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(intent);
            return true;
        } catch (ActivityNotFoundException anf) {
            LogUtils.e("要跳转的应用市场不存在!" + anf.getMessage());
            return false;
        } catch (Exception e) {
            LogUtils.e("其他错误：" + e.getMessage());
            return false;
        }
    }

    /***
     /* 检测是否是应用宝或者是百度市场
     /* @param mContext
     /* @param packageName
     /* @return
     */
    private boolean isCheckBaiduOrYYB(Context mContext, String packageName) {
        boolean installed = isInstalled(packageName, mContext);
        return installed;
    }

    /****
     /* 检查APP是否安装成功
     /* @param packageName
     /* @param context
     /* @return
     */
    private boolean isInstalled(String packageName, Context context) {
        if ("".equals(packageName) || packageName.length() <= 0) {
            return false;
        }

        PackageInfo packageInfo;

        try {
            packageInfo = context.getPackageManager().getPackageInfo(packageName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            packageInfo = null;
        }

        if (packageInfo == null) {
            return false;
        } else {
            return true;
        }
    }

    private String getBrandName(String brandName) {
        if (BRAND.HUAWEI_BRAND.equals(brandName)) {
            //华为
            return PACKAGE_NAME.HUAWEI_PACKAGE_NAME;
        } else if (BRAND.OPPO_BRAND.equals(brandName)) {
            //oppo
            return PACKAGE_NAME.OPPO_PACKAGE_NAME;
        } else if (BRAND.REALME_BRAND.equals(brandName)) {
            //realme
            return PACKAGE_NAME.OPPO_PACKAGE_NAME;
        } else if (BRAND.VIVO_BRAND.equals(brandName)) {
            //vivo
            return PACKAGE_NAME.VIVO_PACKAGE_NAME;
        } else if (BRAND.XIAOMI_BRAND.equals(brandName)) {
            //小米
            return PACKAGE_NAME.XIAOMI_PACKAGE_NAME;
        } else if (BRAND.REDMI_BRAND.equals(brandName)) {
            //红米
            return PACKAGE_NAME.XIAOMI_PACKAGE_NAME;
        } else if (BRAND.LENOVO_BRAND.equals(brandName)) {
            //联想
            return PACKAGE_NAME.LIANXIANG_PACKAGE_NAME;
        } else if (BRAND.MEIZU_BRAND.equals(brandName)) {
            //魅族
            return PACKAGE_NAME.MEIZU_PACKAGE_NAME;
        } else if (BRAND.HONOR_BRAND.equals(brandName)) {
            //荣耀
            return PACKAGE_NAME.HONOR_PACKAGE_NAME;
        } else if (BRAND.ZTE_BRAND.equals(brandName)) {
            //zte
            return PACKAGE_NAME.ZTE_PACKAGE_NAME;
        } else if (BRAND.NIUBIA_BRAND.equals(brandName)) {
            //努比亚
            return PACKAGE_NAME.NIUBIA_PACKAGE_NAME;
        } else if (BRAND.ONE_PLUS_BRAND.equals(brandName)) {
            //OnePlus
            return PACKAGE_NAME.OPPO_PACKAGE_NAME;
        } else if (BRAND.SONY_BRAND.equals(brandName)) {
            //索尼
            return PACKAGE_NAME.GOOGLE_PACKAGE_NAME;
        } else if (BRAND.SAMSUNG_BRAND.equals(brandName)) {
            return PACKAGE_NAME.SAMSUNG_PACKAGE_NAME;
        } else if (BRAND.GOOGLE_BRAND.equals(brandName)) {
            //google
            return PACKAGE_NAME.GOOGLE_PACKAGE_NAME;
        }
        return "";
    }

    /**
     * /* 获取手机厂商
     */
    private String getDeviceBrand() {
        return android.os.Build.BRAND;
    }

    public static class BRAND {

        public static final String HUAWEI_BRAND = "HUAWEI";//HUAWEI_PACKAGE_NAME

        public static final String HONOR_BRAND = "HONOR";//HUAWEI_PACKAGE_NAME

        public static final String OPPO_BRAND = "OPPO";//OPPO_PACKAGE_NAME

        public static final String REALME_BRAND = "REALME";//OPPO_PACKAGE_NAME

        public static final String MEIZU_BRAND = "MEIZU";//MEIZU_PACKAGE_NAME

        public static final String VIVO_BRAND = "VIVO";//VIVO_PACKAGE_NAME

        public static final String XIAOMI_BRAND = "XIAOMI";//XIAOMI_PACKAGE_NAME
        public static final String REDMI_BRAND = "REDMI";//XIAOMI_PACKAGE_NAME

        public static final String LENOVO_BRAND = "LENOVO";//LIANXIANG_PACKAGE_NAME //Lenovo

        public static final String ZTE_BRAND = "ZTE";//ZTE_PACKAGE_NAME
        public static final String XIAOLAJIAO_BRAND = "XIAOLAJIAO";//ZHUOYI_PACKAGE_NAME
        public static final String QH360_BRAND = "360";//QH360_PACKAGE_NAME
        public static final String NIUBIA_BRAND = "NUBIA";//NIUBIA_PACKAGE_NAME
        public static final String ONE_PLUS_BRAND = "ONEPLUS";//OPPO_PACKAGE_NAME
        public static final String MEITU_BRAND = "MEITU";//MEITU_PACKAGE_NAME
        public static final String SONY_BRAND = "SONY";//GOOGLE_PACKAGE_NAME
        public static final String GOOGLE_BRAND = "GOOGLE";//GOOGLE_PACKAGE_NAME
        public static final String HTC_BRAND = "HTC";//未知应用商店包名
        public static final String ZUK_BRAND = "ZUK";//未知应用商店包名
        public static final String SAMSUNG_BRAND = "SAMSUNG";//未知应用商店包名
    }

    /** Redmi*/

    /**
     * /* 华为，oppo,vivo,小米，360，联想，魅族，安智，百度，阿里，应用宝，goog，豌豆荚，pp助手
     **/

    public static class PACKAGE_NAME {
        public static final String OPPO_PACKAGE_NAME = "com.heytap.market";//oppo
        public static final String VIVO_PACKAGE_NAME = "com.bbk.appstore";//vivo
        public static final String HUAWEI_PACKAGE_NAME = "com.huawei.appmarket";//华为
        public static final String HONOR_PACKAGE_NAME = "com.hihonor.appmarket";//荣耀
        public static final String XIAOMI_PACKAGE_NAME = "com.xiaomi.market";//小米
        public static final String MEIZU_PACKAGE_NAME = "com.meizu.mstore";//，魅族
        public static final String LIANXIANG_PACKAGE_NAME = "com.lenovo.leos.appstore";//联想
        public static final String ZTE_PACKAGE_NAME = "zte.com.market";//zte
        public static final String GOOGLE_PACKAGE_NAME = "com.android.vending";//google
        public static final String NIUBIA_PACKAGE_NAME = "com.nubia.neostore";//努比亚
        public static final String SAMSUNG_PACKAGE_NAME = "com.sec.android.app.samsungapps";
    }

    /**
     * /* 启动到应用商店app详情界面
     * <p>
     * /* @param appPkg 目标App的包名
     * <p>
     * /* @param marketPkg 应用商店包名 ,如果为"" 则由系统弹出应用商店
     * <p>
     * /* 列表供用户选择,否则调转到目标市场的应用详情界面，某些应用商店可能会失败
     */
    public static void launchAppDetail(Context context, String appPkg) {
        try {
            if (TextUtils.isEmpty(appPkg)) return;
            Uri uri = Uri.parse("market://details?id=" + appPkg);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            if (!TextUtils.isEmpty(AppUtils.getAppPackageName())) {
                intent.setPackage(AppUtils.getAppPackageName());
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
