package com.business_clean.app.fdd;

import android.annotation.TargetApi;
import android.net.Uri;
import android.os.Build;
import android.util.Log;
import android.webkit.JsPromptResult;
import android.webkit.JsResult;
import android.webkit.PermissionRequest;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebView;


public class H5FaceWebChromeClient extends WebChromeClient {
    private static final String TAG = "H5FaceWebChromeClient";
    private FddWebActivity activity;
    private PermissionRequest request;
    private WebView webView;
    private String acceptType;
    private ValueCallback<Uri> uploadMsg;
    private ValueCallback<Uri[]> filePathCallback;
    private FileChooserParams fileChooserParams;

    public H5FaceWebChromeClient(FddWebActivity mActivity) {
        this.activity = mActivity;
    }

    @Override
    public boolean onJsPrompt(WebView view, String url, String message, String defaultValue, JsPromptResult result) {
        return super.onJsPrompt(view, url, message, defaultValue, result);
    }

    @Override
    public boolean onJsConfirm(WebView view, String url, String message, JsResult result) {
        result.confirm();
        return true;
    }


    /**
     * H5_TRTC 刷脸配置，这里负责处理来自H5页面发出的相机权限申请
     *
     * @param request 来自H5页面的权限请求
     */
    @Override
    public void onPermissionRequest(PermissionRequest request) {
        Log.d(TAG, "onPermissionRequest " + request.getOrigin().toString());
        this.request = request;
        if (activity != null) {
            //申请相机权限，申请权限的代码demo仅供参考，合作方可根据自身业务定制
            activity.requestCameraPermission();
        }
    }

    /**
     * 相机权限申请成功后，拉起TRTC刷脸界面进行实时刷脸验证
     */
    public void enterTrtcFaceVerify() {
        Log.d(TAG, "enterTrtcFaceVerify");
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP) {
            // android sdk 21以上
            if (request != null && request.getOrigin() != null) {
                //根据腾讯域名授权，如果合作方对授权域名无限制的话，这个if条件判断可以去掉，直接进行授权即可。
                Log.d(TAG, "enterTrtcFaceVerify getOrigin()!=null");
//                if (request.getOrigin().toString().contains("https://kyc.qcloud.com")) {
                    //授权
                    request.grant(request.getResources());
                    request.getOrigin();
//                }
            } else {
                if (request == null) {
                    Log.d(TAG, "enterTrtcFaceVerify request==null");
                    if (webView != null && webView.canGoBack()) {
                        webView.goBack();
                    }
                } else {
                    Log.d(TAG, "enterTrtcFaceVerify getOrigin()is null" + request.getOrigin().toString());
                }
            }
        }
    }


    /**
     * For Android >= 4.1  老的录制模式中，收到h5页面发送的录制请求
     */
    public void openFileChooser(ValueCallback<Uri> uploadMsg, String acceptType, String capture) {
        Log.d(TAG, "MainActivity openFileChooser-------");
        this.uploadMsg = uploadMsg;
        this.acceptType = acceptType;
        if (activity != null) {
            //申请系统的相机、录制、sd卡等权限
            activity.requestCameraAndSomePermissions(true);
        }
    }

    /**
     * For Lollipop 5.0+ Devices  老的录制模式中，收到h5页面发送的录制请求
     */
    @TargetApi(21)
    @Override
    public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
        Log.d(TAG, "MainActivity onShowFileChooser-------");
        this.webView = webView;
        this.filePathCallback = filePathCallback;
        this.fileChooserParams = fileChooserParams;
        if (activity != null) {
            //申请系统的相机、录制、sd卡等权限
            activity.requestCameraAndSomePermissions(false);
        }
        return true;
    }

    /**
     * 老的录制模式中，拉起系统相机进行录制视频
     */
    public boolean enterOldModeFaceVerify(boolean belowApi21) {
        Log.d(TAG, "enterOldFaceVerify");
        if (belowApi21) {
            // For Android < 5.0
            if (WBH5FaceVerifySDK.getInstance().recordVideoForApiBelow21(uploadMsg, acceptType, activity)) {
                return true;
            }
        } else { // For Android >= 5.0
            if (WBH5FaceVerifySDK.getInstance().recordVideoForApi21(webView, filePathCallback, activity, fileChooserParams)) {
                return true;
            }
        }
        return false;
    }
}
