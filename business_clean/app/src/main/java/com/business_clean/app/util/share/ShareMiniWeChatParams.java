package com.business_clean.app.util.share;

/**
 * 微信小程序分享参数:
 */
public class ShareMiniWeChatParams {

    private String mMiniTitle = "";
    private String mMiniDescription = "";
    private String mMiniPage = "";
    private String mMiniUserId = "";
    private Object mMiniThumb;
    private boolean integral;
    private boolean MiniPreView;
    private boolean MiniTest;


    public void setIntegral(boolean integral) {
        this.integral = integral;
    }

    public boolean isIntegral() {
        return integral;
    }

    public boolean isMiniPreView() {
        return MiniPreView;
    }

    public boolean isMiniTest() {
        return MiniTest;
    }

    public void setMiniPreView(boolean miniPreView) {
        MiniPreView = miniPreView;
    }

    public void setMiniTest(boolean miniTest) {
        MiniTest = miniTest;
    }

    public String getMiniTitle() {
        return mMiniTitle;
    }

    public void setMiniTitle(String mMiniTitle) {
        this.mMiniTitle = mMiniTitle;
    }

    public String getMiniDescription() {
        return mMiniDescription;
    }

    public void setMiniDescription(String mMiniText) {
        this.mMiniDescription = mMiniText;
    }

    public String getMiniPage() {
        return mMiniPage;
    }

    public void setMiniPage(String mMiniPage) {
        this.mMiniPage = mMiniPage;
    }

    public String getMiniUserId() {
        return mMiniUserId;
    }

    public void setMiniUserId(String mMiniUserId) {
        this.mMiniUserId = mMiniUserId;
    }

    public Object getMiniThumb() {
        return mMiniThumb;
    }

    public void setMiniThumb(Object mMiniThumb) {
        this.mMiniThumb = mMiniThumb;
    }
}
