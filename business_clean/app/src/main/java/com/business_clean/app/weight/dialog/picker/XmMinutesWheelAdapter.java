package com.business_clean.app.weight.dialog.picker;

import com.contrarywind.adapter.WheelAdapter;

public class XmMinutesWheelAdapter implements WheelAdapter {

    private int minValue;
    private int maxValue;
    private int step; // 每个item之间的间隔

    /**
     * Constructor
     *
     * @param minValue the wheel min value
     * @param maxValue the wheel max value
     */
    public XmMinutesWheelAdapter(int minValue, int maxValue, int step) {
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.step = step;
    }

    @Override
    public Object getItem(int index) {
        if (index >= 0 && index < getItemsCount()) {
            int value = minValue + index * step;
            return value;
        }
        return 0;
    }

    @Override
    public int getItemsCount() {
        return (maxValue - minValue + 1) / step;
    }

    @Override
    public int indexOf(Object o) {
        try {
            return (int) o * step - minValue;
        } catch (Exception e) {
            return -1;
        }

    }

    public void setStep(int step) {
        this.step = step;
    }
}
