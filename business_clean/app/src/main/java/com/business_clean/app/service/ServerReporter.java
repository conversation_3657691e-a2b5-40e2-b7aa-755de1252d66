package com.business_clean.app.service;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.App;
import com.business_clean.app.config.Constant;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.UploadFileHelper;
import com.business_clean.data.dao.WatermarkPhotoDatabaseManager;
import com.business_clean.data.dao.WaterPhotoData;
import com.business_clean.data.mode.baseapi.ApiResponse;
import com.business_clean.data.mode.leader.CustomMarkLeaderData;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;

import io.reactivex.rxjava3.functions.Consumer;
import me.hgj.mvvmhelper.ext.LogExtKt;
import okhttp3.Response;
import rxhttp.wrapper.param.RxHttp;

/**
 * 水印图片、视频 后台上报用的工具
 * 需要先把图片上传到七牛云，拿着链接去
 */
public class ServerReporter {
    private String TAG = "ServerReporter";

    private static ServerReporter instance;
    private Context mContext;

    private ServerReporter() {
        // 私有构造函数，避免外部实例化
    }

    public void initialize(Context context) {
        this.mContext = context.getApplicationContext();
    }


    public static synchronized ServerReporter getInstance() {
        if (instance == null) {
            instance = new ServerReporter();
        }
        return instance;
    }


    public void gotoFlutterCreateWorkOrder(boolean isPic, String work_file) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("work_url", "" + work_file);
        hashMap.put("work_type", "" + (isPic ? "1" : "2"));
        if (Constant.ROLE_PROJECT_OWNER) {
            hashMap.put("choose", "1");
        } else {
            hashMap.put("choose", "2");
        }
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("workAddTaskPage")
                .arguments(hashMap)
                .build());
    }


    /**
     * 存储数据到db上，然后通知 水印界面去拍照
     */
    public void reportToServerDb(String projectUuid, String task_uuid, int act_type, int message_type, double lat, double lnt,
                                 String address, String waterUrl, String original, String thumb,
                                 String user_uuid_list, long takePhotoMills) {
        if (mContext == null) {
            LogExtKt.logE("Context is null", TAG);
            return;
        }
        //把数据set到数据库中
        WaterPhotoData daoData = new WaterPhotoData();
        daoData.setProject_uuid(projectUuid);
        daoData.setTask_uuid(task_uuid);
        daoData.setAct_type(act_type);
        daoData.setMessage_type(message_type);
        daoData.setLat(lat);
        daoData.setLnt(lnt);
        daoData.setAddress(address);
        daoData.setPhoto_water_path(waterUrl);
        daoData.setPhoto_origin_path(original);
        daoData.setPhoto_thumb_path(thumb);
        daoData.setUser_uuid_list(user_uuid_list);
        daoData.setNow_current_time_millis(takePhotoMills);
        daoData.setSecurity_code("" + takePhotoMills);
        daoData.setUpload_status(0);
        WatermarkPhotoDatabaseManager.getInstance(mContext).insertWaterPhoto(daoData);
        //通知首页那边，进行上传处理，同时显示上传进度条
        if (act_type == 1) {
            App.getAppViewModelInstance().getRefreshUploadPhoto().setValue(true);
        } else {
            App.getAppViewModelInstance().getRefreshUploadPhoto().setValue(false);
        }
    }

    public void reportToServer(String projectUuid, String task_uuid, int act_type, int message_type, double lat, double lnt,
                               String address, String waterUrl, String original, String thumb,
                               String user_uuid_list, long takePhotoMills,
                               OnUploadWorkFileListener listener) {
        if (mContext == null) {
            LogExtKt.logE("Context is null", TAG);
            return;
        }
        resultReportToServer(projectUuid, task_uuid, act_type, message_type, lat, lnt, address, waterUrl, original, thumb, user_uuid_list, takePhotoMills, listener);
    }

    public void reportToServer(String projectUuid, int act_type, int message_type, double lat, double lnt,
                               String address, String waterUrl, String original, String thumb,
                               String user_uuid_list, long takePhotoMills,
                               OnUploadWorkFileListener listener) {
        if (mContext == null) {
            LogExtKt.logE("Context is null", TAG);
            return;
        }
        resultReportToServer(projectUuid, null, act_type, message_type, lat, lnt, address, waterUrl, original, thumb, user_uuid_list, takePhotoMills, listener);
    }

    /**
     * 标记上报
     */
    public void reportToServer(WaterPhotoData data, OnUploadWorkFileListener listener) {
        if (data == null) {
            LogExtKt.logE("Context is null", TAG);
            return;
        }
        resultReportToServer(data.getProject_uuid(), data.getTask_uuid(), data.getAct_type(), data.getMessage_type(), data.getLat(), data.getLnt(), data.getAddress(), data.getPhoto_water_path(),
                data.getPhoto_origin_path(), data.getPhoto_thumb_path(), data.getUser_uuid_list(), data.getNow_current_time_millis(), listener);
    }

    /**
     * 整合数据，往后台上传图片，存储到数据库后，通知首页更新更新的操作
     *
     * @param task_uuid
     * @param act_type
     * @param message_type
     * @param lat
     * @param lnt
     * @param address
     * @param waterUrl
     * @param original
     * @param thumb
     * @param takePhotoMills
     * @param listener
     */
    private void resultReportToServer(String projectUuid, String task_uuid, int act_type, int message_type, double lat, double lnt,
                                      String address, String waterUrl, String original, String thumb,
                                      String user_uuid_list, long takePhotoMills,
                                      OnUploadWorkFileListener listener) {
        //先回调1点回去
        if (listener != null) {
            listener.onUploadProgress(5);
        }

        if (1 == message_type) {//图片
            if (!TextUtils.isEmpty(waterUrl) && !TextUtils.isEmpty(original)) {
                if (!TextUtils.isEmpty(waterUrl) && !TextUtils.isEmpty(original)) {
                    UploadFileHelper.getInstance().uploadPictures(mContext, waterUrl, UploadFileHelper.PATH_HEADER_CAMERA, new UploadFileHelper.UploadListener() {
                        @Override
                        public void onUploadSuccess(String waterResponse) {
                            if (listener != null) {
                                listener.onUploadProgress(30);
                            }
                            UploadFileHelper.getInstance().uploadPictures(mContext, original, UploadFileHelper.PATH_HEADER_CAMERA, new UploadFileHelper.UploadListener() {
                                @Override
                                public void onUploadSuccess(String originalResponse) {
                                    if (listener != null) {
                                        listener.onUploadProgress(60);
                                    }
                                    UploadFileHelper.getInstance().uploadPictures(mContext, thumb, UploadFileHelper.PATH_HEADER_CAMERA, new UploadFileHelper.UploadListener() {
                                        @Override
                                        public void onUploadSuccess(String thumbResponse) {
                                            if (listener != null) {
                                                listener.onUploadProgress(90);
                                            }
                                            // 分别上传水印图片和原图成功后，再发送请求
                                            requestMark(projectUuid, task_uuid, act_type, message_type, lat, lnt, address, waterResponse, originalResponse, thumbResponse,
                                                    user_uuid_list, takePhotoMills, listener);
                                        }

                                        @Override
                                        public void onUploadFailed(String error) {
                                            if (listener != null) {
                                                listener.onUploadFailed("网络错误，请重试");
                                            }
                                        }

                                        @Override
                                        public void onUploadProgress(int progress) {

                                        }
                                    });
                                }

                                @Override
                                public void onUploadFailed(String error) {
                                    if (listener != null) {
                                        listener.onUploadFailed("网络错误，请重试");
                                    }
                                }

                                @Override
                                public void onUploadProgress(int progress) {

                                }
                            });
                        }

                        @Override
                        public void onUploadFailed(String error) {
                            if (listener != null) {
                                listener.onUploadFailed("网络错误，请重试");
                            }
                        }

                        @Override
                        public void onUploadProgress(int progress) {

                        }
                    });
                }
            }
        } else if (2 == message_type) {
            if (!TextUtils.isEmpty(waterUrl)) {//视频
                //先上传视频
                UploadFileHelper.getInstance().uploadPictures(mContext, waterUrl, UploadFileHelper.PATH_HEADER_CAMERA, new UploadFileHelper.UploadListener() {
                    @Override
                    public void onUploadSuccess(String response) {
                        if (listener != null) {
                            listener.onUploadProgress(30);
                        }
                        //再上传缩略图
                        UploadFileHelper.getInstance().uploadPictures(mContext, thumb, UploadFileHelper.PATH_HEADER_CAMERA, new UploadFileHelper.UploadListener() {
                            @Override
                            public void onUploadSuccess(String thumbResponse) {
                                if (listener != null) {
                                    listener.onUploadProgress(60);
                                }
                                requestMark(projectUuid, task_uuid, act_type, message_type, lat, lnt, address, response, null, thumbResponse,
                                        user_uuid_list, takePhotoMills, listener);
                            }

                            @Override
                            public void onUploadFailed(String error) {
                                if (listener != null) {
                                    listener.onUploadFailed("网络错误，请重试");
                                }
                            }

                            @Override
                            public void onUploadProgress(int progress) {
                            }
                        });
                    }

                    @Override
                    public void onUploadFailed(String error) {
                        if (listener != null) {
                            listener.onUploadFailed("网络错误，请重试");
                        }
                    }

                    @Override
                    public void onUploadProgress(int progress) {

                    }
                });
            }
        }
    }

    /**
     * 标记上报
     */
    private void requestMark(String projectUuid, String task_uuid, int act_type, int message_type, double lat, double lnt, String address, String waterUrl,
                             String original, String thumb, String user_uuid_list, Long takePhotoMills,
                             OnUploadWorkFileListener listener) {
//        LogExtKt.logE("当前的内容:" + message_type, "");
//        LogExtKt.logE("标记图片水印地址:" + waterUrl, "");
//        LogExtKt.logE("标记图片人员:" + attendance_data.size(), "");
//        LogExtKt.logE("标记图片人员json:" + JSON.toJSONString(attendance_data), "");
//        LogExtKt.logE("标记图片取消人员:" + cancel_overtime_data.size(), "");
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("act_type", "" + act_type);//操作类型 1日常工作 2领班打卡
        hashMap.put("message_type", "" + message_type);//消息类型 1图片消息 2视频
        hashMap.put("origin_media_url", "" + original);//消息媒体地址(无水印)
        if (1 == message_type) {//区分类别
            hashMap.put("pic_thumb", "" + thumb);//图片缩略图 图片消息必传
        } else {
            hashMap.put("video_cover_url", "" + thumb);//消息媒体地址(无水印)
        }
        hashMap.put("project_uuid", projectUuid);
        hashMap.put("media_url", "" + waterUrl);//消息媒体地址(有水印)
        hashMap.put("address", "" + address);
        hashMap.put("lnt", "" + lnt);
        hashMap.put("lat", "" + lat);
        hashMap.put("take_photo_time", "" + (takePhotoMills / 1000));//拍照时间
        hashMap.put("security_code", "" + takePhotoMills);//防伪码

        ///地图标识
        hashMap.put("map_code", "gao_de");

        if (!TextUtils.isEmpty(task_uuid)) {
            hashMap.put("uuid", "" + task_uuid);//任务id
            hashMap.put("media_type", "" + message_type);//消息类型 1图片消息 2视频
            RxHttp.get(NetUrl.GET_TODAY_FINISH_TASK).addAll(hashMap).asOkResponse().subscribe(new Consumer<Response>() {
                @Override
                public void accept(Response response) throws Throwable {
                    if (listener != null && response.isSuccessful()) {
                        listener.onUploadProgress(100);
                        listener.onUploadSuccess();
                    }
                }
            });
        } else {
            if (!TextUtils.isEmpty(user_uuid_list)) {
                hashMap.put("user_uuid_list", user_uuid_list);
            }

            //区分列表
            String baseUrl = NetUrl.MARK_PHOTO;

            switch (act_type) {
                case 1://打卡
                    baseUrl = NetUrl.MARK_PHOTO;
                    break;
                case 2://集体打卡
                    baseUrl = NetUrl.MARK_TEAM_PHOTO;
                    break;
                case 3://工作拍照
                    baseUrl = NetUrl.MARK_CAMERA_PHOTO;
                    break;
            }

            RxHttp.postForm(baseUrl)
                    .addAll(hashMap)
                    .asOkResponse()
                    .subscribe(new Consumer<Response>() {
                        @Override
                        public void accept(Response response) throws Throwable {
                            String stringResponse = response.body().string();
                            try {
                                JSONObject jsonObject = new JSONObject(stringResponse);
                                int code = jsonObject.getInt("code");
                                if (code == 0) {
                                    if (listener != null) {
                                        listener.onUploadProgress(100);
                                        listener.onUploadSuccess();
                                    }
                                } else {
                                    String msg = jsonObject.getString("msg");
                                    if (!TextUtils.isEmpty(msg)) {
                                        if (listener != null) {
                                            listener.onUploadFailed(msg);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    });
        }

    }


    public interface OnUploadWorkFileListener {
        void onUploadSuccess();

        void onUploadFailed(String error);

        void onUploadProgress(int progress);
    }
}