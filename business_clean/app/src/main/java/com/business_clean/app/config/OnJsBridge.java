package com.business_clean.app.config;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;
import android.webkit.WebView;

import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.util.MMKVHelper;
import com.just.agentweb.AgentWeb;

import org.json.JSONException;
import org.json.JSONObject;


public class OnJsBridge extends Object {
    private AgentWeb mAgentWeb;
    private WebView mWebView;

    public OnJsBridge(AgentWeb mAgentWeb) {
        this.mAgentWeb = mAgentWeb;
    }

    public OnJsBridge(WebView mWebView) {
        this.mWebView = mWebView;
    }

    @JavascriptInterface
    public void getUserToken() {
        LogUtils.e("getUserToken 交互方法");
        //WebViewJavascriptBridge+getUserToken+CallBack
        if (mAgentWeb != null) {
            mAgentWeb.getJsAccessEntrace().quickCallJs(Constant.JS_SPACE_NAME + "getUserToken" + Constant.JS_SPACE_CALLBACK, MMKVHelper.getString(ConstantMMVK.TOKEN));
        }
    }

    @JavascriptInterface
    public void logout() {
        LogUtils.e("logout 交互方法");
        if (mAgentWeb != null) {
            mAgentWeb.getJsAccessEntrace().quickCallJs(Constant.JS_SPACE_NAME + "logout" + Constant.JS_SPACE_CALLBACK, "");
        }
    }

    @SuppressLint("JavascriptInterface")
    @JavascriptInterface
    public void openNewPromptUrl(String json) {
        LogUtils.e("openNewPromptUrl 交互方法");
        if (TextUtils.isEmpty(json)) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(json);
            String url = jsonObject.optString("url");
            if (TextUtils.isEmpty(url)) {
                return;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
