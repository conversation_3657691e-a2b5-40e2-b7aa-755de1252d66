package com.business_clean.app.util;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;

/**
 * 动画的工具类
 */
public class AnimationUtils {

    /**
     * 执行左右抖动的动画效果
     *
     * @param view 要执行动画的View
     */
    public static void shakeAnimation(View view) {
        Animation shake = new TranslateAnimation(-10, 10, 0, 0);
        shake.setDuration(100);
        shake.setRepeatMode(Animation.REVERSE);
        shake.setRepeatCount(5);
        view.startAnimation(shake);
    }


    public static void slideInFromLeft(final View view) {
        view.setVisibility(View.VISIBLE);
        ObjectAnimator animator = ObjectAnimator.ofFloat(view, "translationX", -view.getWidth(), 0f);
        animator.setDuration(500);
        animator.start();
    }

    public static void slideOutToRight(final View view) {
        ObjectAnimator animator = ObjectAnimator.ofFloat(view, "translationX", 0f, view.getWidth());
        animator.setDuration(500);
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                view.setVisibility(View.GONE);
            }
        });
        animator.start();
    }
}
