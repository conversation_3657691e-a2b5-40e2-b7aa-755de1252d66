package com.business_clean.app.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.business_clean.app.callback.OnLocationStatusChangeListener;
import com.business_clean.app.util.location.LocationUtils;

public class LocationModeChangeReceiver extends BroadcastReceiver {
    private final OnLocationStatusChangeListener mListener;

    public LocationModeChangeReceiver(OnLocationStatusChangeListener listener) {
        this.mListener = listener;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (LocationUtils.LOCATION_MODE_CHANGE_ACTION.equals(intent.getAction())) {
            // 立即检查当前的定位服务状态
            boolean isLocationEnabled = LocationUtils.isLocationServiceEnabled(context);
            if (mListener != null) {
                mListener.onLocationStatusChanged(isLocationEnabled);
            }
        }
    }
}
