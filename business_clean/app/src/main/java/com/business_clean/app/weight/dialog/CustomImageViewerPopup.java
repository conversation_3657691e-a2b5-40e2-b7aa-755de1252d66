package com.business_clean.app.weight.dialog;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.ImageUtils;
import com.business_clean.R;
import com.business_clean.app.callback.GetUrlCacheBitmapListenerWithFail;
import com.business_clean.app.util.GlideUtil;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.share.ShareHelperTools;
import com.business_clean.app.util.share.ShareParams;
import com.business_clean.app.weight.CustomPhotoView;
import com.lxj.xpopup.core.ImageViewerPopupView;

import me.hgj.mvvmhelper.ext.LogExtKt;

public class CustomImageViewerPopup extends ImageViewerPopupView {

    private Context mContext;
    private String waterUrl;//当前水印链接
    private String originalUrl;//当前无水印的图片
    private boolean isHideWater = false;//是否隐藏水印
    private Bitmap mBitmapWaterDownload = null;//记录下载后的 bitmap 下次就不去下载了
    private Bitmap mBitmapNoWaterDownload = null;//记录下载后的 bitmap 下次就不去下载了

    public CustomImageViewerPopup(@NonNull Context context, String waterUrl, String originalUrl) {
        super(context);
        this.mContext = context;
        this.waterUrl = waterUrl;
        this.originalUrl = originalUrl;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_image_viewer_popup;
    }

    @Override
    protected void onCreate() {
        super.onCreate();

        //创建 2 个 View 来展示 有水印跟无水印的图
        CustomPhotoView customPhotoView = findViewById(R.id.iv_photo);
        CustomPhotoView customPhotoViewNoWater = findViewById(R.id.iv_photo_no_water);
        //加载水印图
        customPhotoView.loadImage(waterUrl);
        //加载原图
        customPhotoViewNoWater.loadImage(originalUrl);
        //切换水印View
        ImageView ivWater = findViewById(R.id.iv_dialog_water);
        //切换水印照片
        findViewById(R.id.iv_dialog_water).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!isHideWater) {
                    isHideWater = true;
                    LogExtKt.logE("切换成原图 - " + waterUrl, "");
                    customPhotoView.setVisibility(View.GONE);
                    customPhotoViewNoWater.setVisibility(View.VISIBLE);
                } else {
                    isHideWater = false;
                    customPhotoView.setVisibility(View.VISIBLE);
                    customPhotoViewNoWater.setVisibility(View.GONE);
                    LogExtKt.logE("切换成水印图 - " + originalUrl, "");
                }
                ivWater.setImageResource(isHideWater ? R.mipmap.icon_pre_water_false : R.mipmap.icon_pre_water_true);
                ToastUtil.show(isHideWater ? "已去除水印" : "已添加水印");
            }
        });


        customPhotoView.getPhotoView().setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        customPhotoViewNoWater.getPhotoView().setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        findViewById(R.id.iv_dialog_close).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        //保存图片到本地相册
        findViewById(R.id.iv_dialog_download).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                handlerBeforeBitmap(true);
            }
        });

        //分享图片到微信
        findViewById(R.id.iv_dialog_wechat).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                handlerBeforeBitmap(false);
            }
        });
    }

    private void handlerBeforeBitmap(boolean whoClicked) {
        Bitmap bitmapToHandle = isHideWater ? mBitmapNoWaterDownload : mBitmapWaterDownload;
        if (bitmapToHandle == null) {
            GlideUtil.getUrlCacheBitmapForShareThumbnail((Activity) mContext, isHideWater ? originalUrl : waterUrl, new GetUrlCacheBitmapListenerWithFail() {
                @Override
                public void loadBitmap(Bitmap resource) {
                    if (isHideWater) {
                        mBitmapNoWaterDownload = resource;
                    } else {
                        mBitmapWaterDownload = resource;
                    }
                    handlerBitmap(whoClicked, resource);
                }

                @Override
                public void loadFail() {
                    ToastUtil.show("保存失败，请稍后再试");
                }
            });
        } else {
            handlerBitmap(whoClicked, bitmapToHandle);
        }
    }

    private void handlerBitmap(boolean b, Bitmap bitmap) {
        if (bitmap == null) {
            ToastUtil.show("图片异常");
            return;
        }
        if (b) {
            ImageUtils.save2Album(bitmap, Bitmap.CompressFormat.JPEG);
            ToastUtil.show("保存到相册成功");
        } else {
            ShareParams params = new ShareParams();
            params.setBitmap(bitmap);
            ShareHelperTools.getInstance().shareImage(params, (Activity) mContext);
        }
    }

    @Override
    protected void onShow() {
        super.onShow();
        Log.e("tag", "CustomImageViewerPopup onShow");
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        Log.e("tag", "CustomImageViewerPopup onDismiss");
    }
}

