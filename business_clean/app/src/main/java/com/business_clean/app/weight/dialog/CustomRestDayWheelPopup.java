package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bigkoo.pickerview.adapter.ArrayWheelAdapter;
import com.bigkoo.pickerview.adapter.NumericWheelAdapter;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.R;
import com.business_clean.app.callback.OnDialogRestDayListener;
import com.contrarywind.listener.OnItemSelectedListener;
import com.contrarywind.view.WheelView;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.BottomPopupView;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import me.hgj.mvvmhelper.ext.LogExtKt;


public class CustomRestDayWheelPopup extends BottomPopupView {

    private String title = "请选择";

    private Context context;

    private WheelView wheelView1;
    private WheelView wheelView2;
    private WheelView wheelView3;
    private WheelView wheelView4;
    private WheelView wheelView5;
    private WheelView wheelView6;
    private WheelView wheelView7;

    private int selectedPosition1;
    private int selectedPosition2;
    private int selectedPosition3;
    private int selectedPosition5;
    private int selectedPosition6;
    private int selectedPosition7;

    private OnDialogRestDayListener listener;

    private List<String> days = new ArrayList<>();
    private List<String> hours = new ArrayList<>();
    private List<String> minutes = new ArrayList<>();

    private String invertStart;
    private String invertEnd;
    private int isStartNowDay;
    private int isEndNowDay;

    private boolean needNextDay;


    public CustomRestDayWheelPopup(@NonNull @NotNull Context context, boolean needNextDay, int isStartNowDay, String start, int isEndNowDay, String end, OnDialogRestDayListener onDialogRestDayListener) {
        super(context);
        this.context = context;
        this.needNextDay = needNextDay;
        this.isStartNowDay = isStartNowDay;
        this.invertStart = start;
        this.isEndNowDay = isEndNowDay;
        this.invertEnd = end;
        this.listener = onDialogRestDayListener;
    }


    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_clock_date_more;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        initView();
        initData();
        initClick();
        if (wheelView1 != null) {
            wheelView1.setCurrentItem(isStartNowDay);
            selectedPosition1 = isStartNowDay;
        }
        if (wheelView2 != null && !TextUtils.isEmpty(invertStart) && invertStart.contains(":") && hours != null) {
            wheelView2.setCurrentItem(hours.indexOf(invertStart.substring(0, invertStart.indexOf(":"))));
            selectedPosition2 = hours.indexOf(invertStart.substring(0, invertStart.indexOf(":")));
            if (selectedPosition2 == -1) {
                selectedPosition2 = 0;
            }
        }
        if (wheelView3 != null && !TextUtils.isEmpty(invertStart) && invertStart.contains(":") && hours != null) {
            wheelView3.setCurrentItem(minutes.indexOf(invertStart.substring(invertStart.indexOf(":") + 1)));
            selectedPosition3 = minutes.indexOf(invertStart.substring(invertStart.indexOf(":") + 1));
            if (selectedPosition3 == -1) {
                selectedPosition3 = 0;
            }
        }
        if (wheelView5 != null) {
            wheelView5.setCurrentItem(isEndNowDay);
            selectedPosition5 = isEndNowDay;
        }

        if (wheelView6 != null && !TextUtils.isEmpty(invertEnd) && invertEnd.contains(":") && hours != null) {
            wheelView6.setCurrentItem(hours.indexOf(invertEnd.substring(0, invertEnd.indexOf(":"))));
            selectedPosition6 = hours.indexOf(invertEnd.substring(0, invertEnd.indexOf(":")));
            if (selectedPosition6 == -1) {
                selectedPosition6 = 0;
            }
        }
        if (wheelView7 != null && !TextUtils.isEmpty(invertEnd) && invertEnd.contains(":") && hours != null) {
            wheelView7.setCurrentItem(minutes.indexOf(invertEnd.substring(invertEnd.indexOf(":") + 1)));
            selectedPosition7 = minutes.indexOf(invertEnd.substring(invertEnd.indexOf(":") + 1));
            if (selectedPosition7 == -1) {
                selectedPosition7 = 0;
            }
        }
    }

    private void initView() {
        wheelView1 = findViewById(R.id.wheelView_more_1);
        wheelView2 = findViewById(R.id.wheelView_more_2);
        wheelView3 = findViewById(R.id.wheelView_more_3);
        wheelView4 = findViewById(R.id.wheelView_more_4);
        wheelView5 = findViewById(R.id.wheelView_more_5);
        wheelView6 = findViewById(R.id.wheelView_more_6);
        wheelView7 = findViewById(R.id.wheelView_more_7);

        wheelView1.setCyclic(false);
        wheelView3.setCyclic(false);
        wheelView2.setCyclic(false);
        wheelView4.setCyclic(false);
        wheelView5.setCyclic(false);
        wheelView6.setCyclic(false);
        wheelView7.setCyclic(false);
    }


    private void initData() {
        ArrayList<String> day = new ArrayList<>();

        day.add("当日");
        if (needNextDay) {
            day.add("次日");
        }

        days.add("当日");
        days.add("次日");

        List<String> array2 = new ArrayList<>();
        array2.add("至");
        for (int i = 0; i <= 23; i++) {
            String hour = String.format("%02d", i);
            hours.add(hour);
        }
        for (int i = 0; i <= 55; i++) {
            if (i % 5 == 0) {
                minutes.add(String.format("%02d", i));
            }
        }

        // 设置WheelView的适配器和数据源
        wheelView1.setAdapter(new ArrayWheelAdapter(day));
        wheelView2.setAdapter(new ArrayWheelAdapter(hours));
        wheelView3.setAdapter(new ArrayWheelAdapter(minutes));
        wheelView4.setAdapter(new ArrayWheelAdapter(array2));
        wheelView5.setAdapter(new ArrayWheelAdapter(days));
        wheelView6.setAdapter(new ArrayWheelAdapter(hours));
        wheelView7.setAdapter(new ArrayWheelAdapter(minutes));
    }

    private void initClick() {
        // 设置WheelView的选择监听
        wheelView1.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition1 = index;
            }
        });

        wheelView2.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition2 = index;
            }
        });

        wheelView3.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition3 = index;
            }
        });

        wheelView5.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition5 = index;
            }
        });

        wheelView6.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition6 = index;
            }
        });

        wheelView7.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition7 = index;
            }
        });

        findViewById(R.id.tv_common_dialog_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        TextView tv_common_dialog_center = findViewById(R.id.tv_common_dialog_center);
        tv_common_dialog_center.setText(title);

        TextView btnConfirm = findViewById(R.id.tv_common_dialog_yes);
        btnConfirm.setTextColor(XPopup.getPrimaryColor());
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (listener != null) {
                    LogUtils.e("选择的下表 ===  下表2位 - " + selectedPosition2 + " ; 下标3位 - " + selectedPosition3);
                    //当日  时间段 次日 时间段
                    String startTime = hours.get(selectedPosition2) + ":" + minutes.get(selectedPosition3);
                    String endTime = hours.get(selectedPosition6) + ":" + minutes.get(selectedPosition7);
                    listener.selected(selectedPosition1, startTime, selectedPosition5, endTime);
                }
                LogExtKt.logE("选择的内容下表" + selectedPosition1 + " ; " + selectedPosition3, "");
            }
        });
    }
}
