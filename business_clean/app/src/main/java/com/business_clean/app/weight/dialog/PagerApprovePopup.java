package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.DividerItemDecoration;
import com.business_clean.data.mode.todo.TemplateListEntity;
import com.business_clean.ui.adapter.todo.TemplateAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.lxj.xpopup.core.BottomPopupView;
import com.zhixinhuixue.library.common.ext.DensityExtKt;

import java.util.HashMap;
import java.util.List;


/**
 * 审批记录
 */
public class PagerApprovePopup extends BottomPopupView {

    private RecyclerView recyclerView;
    private TextView tvLookApprove;//查看审批记录
    private Context mContext;

    private TemplateAdapter mAdapter;
    private List<TemplateListEntity> list;

    public PagerApprovePopup(@NonNull Context context, List<TemplateListEntity> list) {
        super(context);
        this.mContext = context;
        this.list = list;
    }


    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_approve_pager;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        recyclerView = findViewById(R.id.rv_dialog);
        tvLookApprove = findViewById(R.id.tv_dialog_look_approve);
        recyclerView.setLayoutManager(new GridLayoutManager(recyclerView.getContext(), 4));
        mAdapter = new TemplateAdapter();
        recyclerView.addItemDecoration(new DividerItemDecoration(mContext));
        recyclerView.setAdapter(mAdapter);


        View empty = View.inflate(mContext, R.layout.layout_empty, null);
        TextView tvEmpty = empty.findViewById(R.id.tv_empty_data);
        tvEmpty.setText("暂无审批项目");
        mAdapter.setEmptyView(empty);


        mAdapter.setList(list);

        if (mAdapter != null) {
            mAdapter.setOnItemClickListener(new OnItemClickListener() {
                @Override
                public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                    dialog.dismiss();
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("uuid", mAdapter.getData().get(position).getUuid());
                    hashMap.put("is_system", mAdapter.getData().get(position).getIs_system());//是否系统
                    hashMap.put("title", mAdapter.getData().get(position).getTemplate_name());//名字
                    switch (mAdapter.getData().get(position).getTemplate_type()) {
                        case "1":
                            App.getAppViewModelInstance().getAgainEntry().setValue(null);
//                            ActivityForwardUtil.startActivity(AddProjectActivity.class);
                            CommonToFlutter.gotoFlutterAddStaffPage("", "", 0);
                            break;
                        case "2":
                            FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                                    .pageName("StaffResignMainListPage")
                                    .arguments(hashMap)
                                    .build());
                            break;
                        case "3":
                            FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                                    .pageName("materialTemplatePage")
                                    .arguments(hashMap)
                                    .build());
                            break;
                        default:
                            FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                                    .pageName("approveTemplatePage")
                                    .arguments(hashMap)
                                    .build());
                            break;
                    }
                }
            });
        }


        //查看审批记录 -
        tvLookApprove.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("ApproveRecordPage")
                        .arguments(new HashMap<>())
                        .build());
            }
        });


        findViewById(R.id.tv_dialog_base_bottom_cancel).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

    }

    @Override
    protected int getMaxHeight() {
        return (int) (DensityExtKt.getScreenHeight() / 1.5);
    }


    @Override
    protected void onShow() {
        super.onShow();
        Log.e("tag", "PagerDrawerPopup onShow");
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        Log.e("tag", "PagerDrawerPopup onDismiss");
    }

}

