package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.business_clean.R;
import com.business_clean.app.callback.OnDialogCancelListener;
import com.business_clean.app.callback.OnDialogSelectListener;
import com.business_clean.app.util.DividerItemDecoration;
import com.business_clean.data.initconfig.popup.BaseBottomListEntity;
import com.business_clean.ui.adapter.dialog.BaseBottomAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.lxj.xpopup.core.BottomPopupView;

import java.util.List;

public class CustomBottomListPopup extends BottomPopupView {

    private BaseBottomAdapter bottomAdapter;

    private List<BaseBottomListEntity> datas;


    private OnDialogCancelListener onDialogCancelListener;

    private OnDialogSelectListener onDialogSelectListener;

    private TextView tvTitle;
    private TextView tvCancel;
    private int position = -1;

    public void setOnDialogListener(OnDialogCancelListener onDialogCancelListener, OnDialogSelectListener onDialogSelectListener) {
        this.onDialogCancelListener = onDialogCancelListener;
        this.onDialogSelectListener = onDialogSelectListener;
    }

    public CustomBottomListPopup(@NonNull Context context, List<BaseBottomListEntity> bottomListEntityList) {
        super(context);
        this.datas = bottomListEntityList;
    }

    public CustomBottomListPopup(@NonNull Context context, int position, List<BaseBottomListEntity> bottomListEntityList) {
        super(context);
        this.datas = bottomListEntityList;
        this.position = position;
    }


    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_base_bottom_string;
    }

    @Override
    protected void onCreate() {
        super.onCreate();

        tvTitle = findViewById(R.id.tv_dialog_base_bottom_title);
        tvCancel = findViewById(R.id.tv_dialog_base_bottom_cancel);

        RecyclerView recyclerView = findViewById(R.id.recycler_base_bottom);

        bottomAdapter = new BaseBottomAdapter(position);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerView.addItemDecoration(new DividerItemDecoration(getContext()));
        recyclerView.setAdapter(bottomAdapter);
        bottomAdapter.setList(datas);

        initBindViewClick();

    }

    /**
     * 更新title
     *
     * @param title
     */
    public void setTitle(String title) {
        if (tvTitle != null) {
            tvTitle.setText(title);
            tvTitle.setVisibility(View.VISIBLE);
        }
    }

    private void initBindViewClick() {
        bottomAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                if (onDialogSelectListener != null) {
                    onDialogSelectListener.onSelect(position, bottomAdapter.getData().get(position).getTitle());
                }
                dismiss();
            }
        });

        tvCancel.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onDialogCancelListener != null) {
                    onDialogCancelListener.onCancel();
                }
                dismiss();
            }
        });
    }


}
