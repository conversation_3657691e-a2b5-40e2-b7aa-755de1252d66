package com.demo.exp.utils;

import com.blankj.utilcode.util.PathUtils;

public interface Contacts {


    String SP_NAME = "RD_SP_NAME";
    String WATER_CAMERA_PATH = PathUtils.getInternalAppDataPath() + "/watercamera/";
    int VISIT_AUDIO_AGAIN  = 101011;

    String[] colorList = {"#ffffff", "#F70200", "#FEB000", "#FEEB00", "#53DE86", "#0EC1E4", "#0078F7", "#9F3AF1", "#3CCDFF", "#FF73C5", "#000000", "#7B7B7B"};

}

