package com.business_clean.app.util;

import android.os.Handler;
import android.os.Looper;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.viewpager2.widget.ViewPager2;

import com.blankj.utilcode.util.LogUtils;

/**
 * Fragment事务安全处理工具类
 * 解决 "FragmentManager is already executing transactions" 问题
 */
public class FragmentTransactionHelper {
    private static final String TAG = "FragmentTransactionHelper";
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    /**
     * 安全地设置ViewPager2的当前页面
     * 
     * @param viewPager2 ViewPager2实例
     * @param position   目标位置
     * @param smoothScroll 是否平滑滚动
     * @param fragment   调用的Fragment实例，用于检查状态
     */
    public static void safeSetCurrentItem(ViewPager2 viewPager2, int position, boolean smoothScroll, Fragment fragment) {
        if (viewPager2 == null) {
            LogUtils.e(TAG, "ViewPager2 is null");
            return;
        }

        if (fragment != null && (!fragment.isAdded() || fragment.isDetached())) {
            LogUtils.e(TAG, "Fragment is not in valid state");
            return;
        }

        // 使用Handler.post确保在主线程的下一个循环中执行
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                try {
                    if (fragment != null && fragment.isAdded() && !fragment.isDetached()) {
                        if (position >= 0 && position < (viewPager2.getAdapter() != null ? viewPager2.getAdapter().getItemCount() : 0)) {
                            viewPager2.setCurrentItem(position, smoothScroll);
                        } else {
                            LogUtils.e(TAG, "Invalid position: " + position);
                        }
                    }
                } catch (Exception e) {
                    LogUtils.e(TAG, "Error setting ViewPager2 current item: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 延迟安全地设置ViewPager2的当前页面
     * 
     * @param viewPager2 ViewPager2实例
     * @param position   目标位置
     * @param smoothScroll 是否平滑滚动
     * @param fragment   调用的Fragment实例
     * @param delayMillis 延迟时间（毫秒）
     */
    public static void safeSetCurrentItemDelayed(ViewPager2 viewPager2, int position, boolean smoothScroll, 
                                                Fragment fragment, long delayMillis) {
        if (viewPager2 == null) {
            LogUtils.e(TAG, "ViewPager2 is null");
            return;
        }

        if (fragment != null && (!fragment.isAdded() || fragment.isDetached())) {
            LogUtils.e(TAG, "Fragment is not in valid state");
            return;
        }

        mainHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                try {
                    if (fragment != null && fragment.isAdded() && !fragment.isDetached()) {
                        if (position >= 0 && position < (viewPager2.getAdapter() != null ? viewPager2.getAdapter().getItemCount() : 0)) {
                            viewPager2.setCurrentItem(position, smoothScroll);
                        } else {
                            LogUtils.e(TAG, "Invalid position: " + position);
                        }
                    }
                } catch (Exception e) {
                    LogUtils.e(TAG, "Error setting ViewPager2 current item delayed: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }, delayMillis);
    }

    /**
     * 检查Fragment是否处于安全状态
     * 
     * @param fragment Fragment实例
     * @return 是否安全
     */
    public static boolean isFragmentSafe(Fragment fragment) {
        if (fragment == null) {
            return false;
        }

        try {
            return fragment.isAdded() 
                && !fragment.isDetached() 
                && !fragment.isRemoving() 
                && fragment.getActivity() != null 
                && !fragment.getActivity().isFinishing()
                && !fragment.getActivity().isDestroyed();
        } catch (Exception e) {
            LogUtils.e(TAG, "Error checking fragment state: " + e.getMessage());
            return false;
        }
    }

    /**
     * 安全地执行Fragment相关操作
     * 
     * @param fragment Fragment实例
     * @param runnable 要执行的操作
     */
    public static void safeExecute(Fragment fragment, Runnable runnable) {
        if (!isFragmentSafe(fragment)) {
            LogUtils.e(TAG, "Fragment is not safe for execution");
            return;
        }

        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                try {
                    if (isFragmentSafe(fragment)) {
                        runnable.run();
                    }
                } catch (Exception e) {
                    LogUtils.e(TAG, "Error executing safe operation: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 延迟安全地执行Fragment相关操作
     * 
     * @param fragment Fragment实例
     * @param runnable 要执行的操作
     * @param delayMillis 延迟时间（毫秒）
     */
    public static void safeExecuteDelayed(Fragment fragment, Runnable runnable, long delayMillis) {
        if (!isFragmentSafe(fragment)) {
            LogUtils.e(TAG, "Fragment is not safe for delayed execution");
            return;
        }

        mainHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                try {
                    if (isFragmentSafe(fragment)) {
                        runnable.run();
                    }
                } catch (Exception e) {
                    LogUtils.e(TAG, "Error executing delayed safe operation: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }, delayMillis);
    }

    /**
     * 检查FragmentManager是否正在执行事务
     * 
     * @param fragmentManager FragmentManager实例
     * @return 是否正在执行事务
     */
    public static boolean isExecutingTransaction(FragmentManager fragmentManager) {
        if (fragmentManager == null) {
            return true; // 如果为null，认为不安全
        }

        try {
            // 通过反射检查FragmentManager的状态
            // 这是一个私有方法，但可以通过反射访问
            return fragmentManager.isStateSaved();
        } catch (Exception e) {
            LogUtils.e(TAG, "Error checking FragmentManager state: " + e.getMessage());
            return true; // 出错时认为不安全
        }
    }

    /**
     * 安全地提交Fragment事务
     * 
     * @param fragmentManager FragmentManager实例
     * @param runnable 包含事务操作的Runnable
     */
    public static void safeCommitTransaction(FragmentManager fragmentManager, Runnable runnable) {
        if (fragmentManager == null) {
            LogUtils.e(TAG, "FragmentManager is null");
            return;
        }

        if (isExecutingTransaction(fragmentManager)) {
            // 如果正在执行事务，延迟执行
            mainHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    safeCommitTransaction(fragmentManager, runnable);
                }
            }, 50);
            return;
        }

        try {
            runnable.run();
        } catch (Exception e) {
            LogUtils.e(TAG, "Error committing transaction: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 清理Handler中的所有待执行任务
     */
    public static void clearAllPendingTasks() {
        mainHandler.removeCallbacksAndMessages(null);
    }
}
