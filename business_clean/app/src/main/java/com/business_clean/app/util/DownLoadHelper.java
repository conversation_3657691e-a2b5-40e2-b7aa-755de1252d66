package com.business_clean.app.util;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;


import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.ext.LoadingDialogExtKt;

import java.io.File;

/**
 * 统一封装的下载，用这个，统一处理文件处理
 */
public class DownLoadHelper {

    public static void downloadFile(Context context, String downloadUrl, String dialogText, String fileName, boolean shareToWeChat) {
        downloadFile(context, downloadUrl, dialogText, fileName, shareToWeChat, null);
    }

    public static void downloadFile(Context context, String downloadUrl, String dialogText, String fileName, boolean shareToWeChat, DownloadUtil.DownloadListener listener) {
        if (context == null || TextUtils.isEmpty(downloadUrl)) {
            LogUtils.e("Context或URL为空，无法下载");
            return;
        }

        // 显示加载对话框
        if (!((Activity) context).isFinishing()) {
            LoadingDialogExtKt.showLoadingExt((Activity) context, TextUtils.isEmpty(dialogText) ? "下载文件中..." : dialogText);
        }

        String suffix = getFileExtension(downloadUrl);
        DownloadUtil.downloadFile(context, downloadUrl, suffix, fileName, true, new DownloadUtil.DownloadListener() {
            @Override
            public void onDownloadProgress(int progress) {
                LogUtils.e("下载进度: " + progress + "%");
            }

            @Override
            public void onDownloadSuccess(File file) {
                LogUtils.e("文件下载成功: " + file.getAbsolutePath());
                if (listener != null) {
                    listener.onDownloadSuccess(file);
                }
                if (shareToWeChat) {
                    shareToWeChat(context, fileName + suffix, file.getAbsolutePath());
                }
                dismissLoadingDialog(context);
            }

            @Override
            public void onDownloadFailed(Exception e) {
                LogUtils.e("文件下载失败: " + e.getMessage());
                ToastUtil.show("文件下载失败，请稍后再试");
                dismissLoadingDialog(context);
            }
        });
    }

    private static String getFileExtension(String url) {
        if (url.contains(".")) {
            return url.substring(url.lastIndexOf("."));
        }
        return ""; // 默认返回空字符串
    }

    private static void dismissLoadingDialog(Context context) {
        if (!((Activity) context).isFinishing()) {
            LoadingDialogExtKt.dismissLoadingExt((Activity) context);
        }
    }

    private static void shareToWeChat(Context context, String fileName, String filePath) {
        LogUtils.e("分享的文件名: " + fileName + "  分享的文件路径: " + filePath);
        ShareUtils.shareWechatFriend(context, new File(filePath));
    }
}
