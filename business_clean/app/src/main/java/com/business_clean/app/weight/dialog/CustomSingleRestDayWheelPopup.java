package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bigkoo.pickerview.adapter.ArrayWheelAdapter;
import com.bigkoo.pickerview.adapter.NumericWheelAdapter;
import com.business_clean.R;
import com.business_clean.app.callback.OnDialogRestDayListener;
import com.business_clean.app.callback.OnDialogSingleRestDayListener;
import com.contrarywind.listener.OnItemSelectedListener;
import com.contrarywind.view.WheelView;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.BottomPopupView;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import me.hgj.mvvmhelper.ext.LogExtKt;


public class CustomSingleRestDayWheelPopup extends BottomPopupView {

    private String title = "请选择";

    private Context context;

    private WheelView wheelView1;
    private WheelView wheelView2;
    private WheelView wheelView3;

    private int selectedPosition1;
    private int selectedPosition2;
    private int selectedPosition3;

    private OnDialogSingleRestDayListener listener;

    private List<String> hours;
    private List<String> minutes;

    private String invertStart;
    private int isStartNowDay;


    public CustomSingleRestDayWheelPopup(@NonNull @NotNull Context context, int isStartNowDay, String start, OnDialogSingleRestDayListener onDialogRestDayListener) {
        super(context);
        this.context = context;
        this.isStartNowDay = isStartNowDay;
        this.invertStart = start;
        this.listener = onDialogRestDayListener;
    }


    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_single_clock_date_more;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        initView();
        initData();
        initClick();
        if (wheelView1 != null) {
            wheelView1.setCurrentItem(isStartNowDay);
            selectedPosition1 = isStartNowDay;
        }
        if (wheelView2 != null && !TextUtils.isEmpty(invertStart) && invertStart.contains(":") && hours != null) {
            wheelView2.setCurrentItem(hours.indexOf(invertStart.substring(0, invertStart.indexOf(":"))));
            selectedPosition2 = hours.indexOf(invertStart.substring(0, invertStart.indexOf(":")));
        }
        if (wheelView3 != null && !TextUtils.isEmpty(invertStart) && invertStart.contains(":") && hours != null) {
            wheelView3.setCurrentItem(minutes.indexOf(invertStart.substring(invertStart.indexOf(":") + 1)));
            selectedPosition3 = minutes.indexOf(invertStart.substring(invertStart.indexOf(":") + 1));
        }
    }

    private void initView() {
        wheelView1 = findViewById(R.id.wheelView_more_1);
        wheelView2 = findViewById(R.id.wheelView_more_2);
        wheelView3 = findViewById(R.id.wheelView_more_3);

        wheelView1.setCyclic(false);
        wheelView3.setCyclic(false);
        wheelView2.setCyclic(false);
    }


    private void initData() {
        ArrayList<String> days = new ArrayList<>();

        days.add("当日");
        days.add("次日");

        hours = new ArrayList<>();
        for (int i = 0; i <= 24; i++) {
            String hour = String.format("%02d", i);
            hours.add(hour);
        }
        minutes = new ArrayList<>();
        for (int i = 0; i <= 60; i++) {
            if (i % 5 == 0) {
                minutes.add(String.format("%02d", i));
            }
        }

        // 设置WheelView的适配器和数据源
        wheelView1.setAdapter(new ArrayWheelAdapter(days));
        wheelView2.setAdapter(new ArrayWheelAdapter(hours));
        wheelView3.setAdapter(new ArrayWheelAdapter(minutes));
    }

    private void initClick() {
        // 设置WheelView的选择监听
        wheelView1.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition1 = index;
            }
        });

        wheelView2.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition2 = index;
            }
        });

        wheelView3.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition3 = index;
            }
        });


        findViewById(R.id.tv_common_dialog_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        TextView tv_common_dialog_center = findViewById(R.id.tv_common_dialog_center);
        tv_common_dialog_center.setText(title);

        TextView btnConfirm = findViewById(R.id.tv_common_dialog_yes);
        btnConfirm.setTextColor(XPopup.getPrimaryColor());
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (listener != null) {
                    //当日  时间段 次日 时间段
                    String startTime = hours.get(selectedPosition2) + ":" + minutes.get(selectedPosition3);
                    listener.selected(selectedPosition1, startTime);
                }
                LogExtKt.logE("选择的内容下表" + selectedPosition1 + " ; " + selectedPosition3, "");
            }
        });
    }
}
