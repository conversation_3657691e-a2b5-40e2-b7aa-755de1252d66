package com.business_clean.app.event

import android.graphics.Bitmap
import com.business_clean.data.initconfig.WaterMarkBitmapData
import com.business_clean.data.mode.BaseUuidEntity
import com.business_clean.data.mode.address.AddressListEntity
import com.business_clean.data.mode.attendance.FilterAttendanceData
import com.business_clean.data.mode.camera.LeaderClockEntity
import com.business_clean.data.mode.classes.ClassesListEntity
import com.business_clean.data.mode.custom.CustomMangerList
import com.business_clean.data.mode.format.FormatChildItemList
import com.business_clean.data.mode.group.GroupMangerListEntity
import com.business_clean.data.mode.login.UserInfo
import com.business_clean.data.mode.members.EntryConditionEntity
import com.business_clean.data.mode.permission.PermissionData
import com.business_clean.data.mode.project.AddCompanyStaffEntity
import com.business_clean.data.mode.project.ProjectManager
import com.business_clean.data.mode.project.ProjectMangerList
import com.business_clean.data.mode.roster.RosterFilterData
import com.business_clean.data.mode.roster.RosterList
import com.business_clean.data.mode.todo.ApproveFilterData
import com.business_clean.data.mode.todo.TodoTotalEntity
import com.kunminx.architecture.ui.callback.UnPeekLiveData
import me.hgj.mvvmhelper.base.BaseViewModel

/**
 * APP全局的ViewModel，可以存放公共数据，当他数据改变时，所有监听他的地方都会收到回调,也可以做发送消息
 * 类似与RxBus
 */
class AppViewModel : BaseViewModel() {

    //App的账户信息
    var userInfo = UnPeekLiveData<UserInfo>()

    //项目的信息
    var projectInfo = UnPeekLiveData<ProjectMangerList>()


    //App主题颜色 中大型项目不推荐以这种方式改变主题颜色，比较繁琐耦合，且容易有遗漏某些控件没有设置主题色
    var appColor = UnPeekLiveData<Int>()

    //App 列表动画
    var appAnimation = UnPeekLiveData<Int>()


    /******************************************数据记录的写在这里面**********************************************************/

    //相机回调 指定首页的界面
    var backPosition = UnPeekLiveData<Int>()

    //临时保存水印图片的地方
    var waterMarkBitmap = UnPeekLiveData<WaterMarkBitmapData>()

    //保存全部项目的
    var allProjectManager = UnPeekLiveData<ProjectManager>()

    //增加项目标记跳转
    var projectPersonnel = UnPeekLiveData<Int>()

    //增加按钮上一份项目标记跳转
    var projectLastPersonnel = UnPeekLiveData<Int>()

    //记录创建UUID
    var createUuid = UnPeekLiveData<String>()

    //记录创建application_no
    var application_no = UnPeekLiveData<String>()

    //关闭是否编辑
    var createFinish = UnPeekLiveData<Boolean>()

    //记录领班打卡的时候，回调给上层的参数
    var leaderData = UnPeekLiveData<LeaderClockEntity>()

    //记录考勤月报筛选
    var filterAttendanceData = UnPeekLiveData<FilterAttendanceData>()

    //记录考勤日报的筛选
    var filterAttendanceDayData = UnPeekLiveData<FilterAttendanceData>()

    //记录花名册筛选
    var filterRosterFilterData = UnPeekLiveData<RosterFilterData>()

    //审批记录的筛选
    var approveFilterData = UnPeekLiveData<ApproveFilterData>()

    //记录跳转代办，我创建的
    var todoMeCreate = UnPeekLiveData<Boolean>()

    //首页点击显示审批弹窗
    var workbenchApprove = UnPeekLiveData<Boolean>()

    //编辑总部成员 来看所属部门
    var belongDepartment = UnPeekLiveData<AddCompanyStaffEntity>()

    // 合同公司
    var contractCompany = UnPeekLiveData<AddCompanyStaffEntity>()

    ///用户UUid
    var rosterFilterAttendanceBaseUuidEntity = UnPeekLiveData<BaseUuidEntity>()

    ///入职是否用沿用了之前的信息
    var beforeDraftEntry = UnPeekLiveData<EntryConditionEntity>()

    //再次入职的uuid
    var againEntry = UnPeekLiveData<String>()

    //记录首页当前选中的项目 是不是全部
    var homeProjectHasAll = UnPeekLiveData<Boolean>()

    /******************************************数据记录的写在这里面**********************************************************/


    /******************************************刷新的写在这里面**********************************************************/

    //客户管理的刷新
    var refreshCustomManagerList = UnPeekLiveData<Boolean>()

    //刷新项目列表
    var refreshProjectManagerList = UnPeekLiveData<Boolean>()

    //班次打卡
    var refreshClassesManagerList = UnPeekLiveData<Boolean>()

    //地址管理
    var refreshAddressManagerList = UnPeekLiveData<Boolean>()

    //考勤规则
    var refreshGroupManagerList = UnPeekLiveData<Boolean>()

    //常用联系人
    var refreshContactManagerList = UnPeekLiveData<Boolean>()

    //创建员工成员成功，调用方刷新
    var refreshMember = UnPeekLiveData<Boolean>()

    //待办的刷新
    var refreshTodo = UnPeekLiveData<Boolean>()

    //工单的详情刷新
    var refreshWorkTask = UnPeekLiveData<Boolean>()

    var refreshTodoTotal = UnPeekLiveData<TodoTotalEntity>()

    //抄送我的刷新
    var refreshCopyRead = UnPeekLiveData<Boolean>()

    //flutter工单的创建状态
    var refreshWorkOrder = UnPeekLiveData<Boolean>()

    //todo的子item 刷新
    var refreshTodoChild = UnPeekLiveData<Any>()

    //拍照图片的上传
    var refreshUploadPhoto = UnPeekLiveData<Boolean>()

    //刷新工作圈首页的列表
    var refreshCircle = UnPeekLiveData<Boolean>()

    //是否更新时间
    var refreshTime = UnPeekLiveData<Boolean>()


    //通知检查集体打卡的选择人数
    var checkTeamNumber = UnPeekLiveData<Boolean>()

    //通知拍照界面中的tag刷新
    var refreshWaterTag = UnPeekLiveData<Boolean>()

    /******************************************刷新的写在这里面**********************************************************/


    /******************************************选择的写在这里面**********************************************************/

    //选择客户返回数据
    var selectCustomMangerList = UnPeekLiveData<CustomMangerList>()

    //选择项目返回数据
    var selectCompanyProjectMangerList = UnPeekLiveData<ProjectMangerList>()

    //选择y业态返回数据
    var selectedFormat = UnPeekLiveData<FormatChildItemList>()

    //权限选择
    var selectPermissionsStatus = UnPeekLiveData<PermissionData>()

    //考勤规则选择
    var selectedGroup = UnPeekLiveData<GroupMangerListEntity>()

    //地址选择
    var selectedAddress = UnPeekLiveData<List<AddressListEntity>>()

    //地址选择不限制
    var selectedLimitationAddress = UnPeekLiveData<Boolean>()

    //班次选择
    var selectedClasses = UnPeekLiveData<ClassesListEntity>()

    //人员选择
    var selectedPersonnel = UnPeekLiveData<List<RosterList>>()

    /******************************************选择的写在这里面**********************************************************/


    //flutter 支付或其他支付的效果都可以写这里
    var payChannel = UnPeekLiveData<String>()

}