package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.text.method.LinkMovementMethod;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.business_clean.R;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.callback.OnDialogCreateFinish;
import com.lxj.xpopup.core.CenterPopupView;

import org.jetbrains.annotations.NotNull;

public class CustomContentHighlightPop extends CenterPopupView {

    private TextView mTvTitle;
    private TextView mTvContent;

    private TextView mTvCancel;
    private TextView mTvConfirm;

    private OnDialogCreateFinish dialogInit;
    private OnDialogConfirmListener dialogConfirmListener;


    public CustomContentHighlightPop(@NonNull @NotNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.common_dialog_custom_content_highlight;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        mTvTitle = findViewById(R.id.tv_title);
        mTvContent = findViewById(R.id.tv_content);
        mTvCancel = findViewById(R.id.tv_cancel);
        mTvConfirm = findViewById(R.id.tv_confirm);

        mTvContent.setMovementMethod(LinkMovementMethod.getInstance());
        mTvContent.setHighlightColor(ContextCompat.getColor(mTvContent.getContext(), R.color.transparent));

        initOnClick();
        if (dialogInit != null) {
            dialogInit.onViewInit();
        }
    }

    private void initOnClick() {
        mTvCancel.setOnClickListener(v -> dialog.dismiss());
        mTvConfirm.setOnClickListener(v -> {
            if (dialogConfirmListener != null) {
                dialogConfirmListener.onConfirm();
            }
            dialog.dismiss();
        });
    }


    public void setListener(OnDialogCreateFinish dialogInit, OnDialogConfirmListener dialogConfirmListener) {
        this.dialogInit = dialogInit;
        this.dialogConfirmListener = dialogConfirmListener;
    }

    public TextView getTvContent() {
        return mTvContent;
    }

    public TextView getTvCancel() {
        return mTvCancel;
    }

    public TextView getTvConfirm() {
        return mTvConfirm;
    }

    public TextView getTvTitle() {
        return mTvTitle;
    }
}
