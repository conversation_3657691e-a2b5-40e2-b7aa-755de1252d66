package com.business_clean.app.callback;

import android.text.TextUtils;
import android.util.Log;

import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.data.mode.login.ALiAuth;
import com.idsmanager.doraemonlibrary.callback.FetchAccessTokenCallBack;
import com.idsmanager.doraemonlibrary.dto.AccessTokenInfo;
import com.idsmanager.doraemonlibrary.dto.AccessTokenInfoResult;
import com.idsmanager.doraemonlibrary.dto.FetchAccessTokenRequest;

import me.hgj.mvvmhelper.ext.LogExtKt;
import rxhttp.wrapper.param.RxHttp;

public class FetchAccessTokenCallBackImpl implements FetchAccessTokenCallBack {
    private static final String TAG = FetchAccessTokenCallBackImpl.class.getSimpleName();

    /**
     * 获取AccessToken回调方法
     *
     * @return
     */
    @Override
    public AccessTokenInfoResult fetchAccessToken(FetchAccessTokenRequest fetchAccessTokenRequest) {
        AccessTokenInfoResult callbackResult = new AccessTokenInfoResult();
        try {
            //需要改成同步 不用异步
            ALiAuth aLiAuth = RxHttp.get(NetUrl.GET_ALI_AUTH_TOKEN)
                    .add("mobile_extend_params_json", fetchAccessTokenRequest.getMobileExtendParamsJson())
                    .add("mobile_extend_params_json_sign", fetchAccessTokenRequest.getMobileExtendParamsJsonSign())
                    .add("op_ip", NetworkUtils.getIPAddress(true))
                    .add("login_device", DeviceUtils.getManufacturer() + DeviceUtils.getModel())
                    .asResponse(ALiAuth.class)
                    .blockingSingle();
            AccessTokenInfo accessTokenInfo = new AccessTokenInfo();
            LogExtKt.logE("FetchAccessTokenCallBackImpl fetchAccessToken 来请求接口了，请求内容 = " + aLiAuth.getAccess_token().toString(), "");

            /**
             * TODO:获取应用授权Token
             * 需要根据文档开发后端接口获取,获取后返回接口
             * Token必须是同步获取的
             * https://help.aliyun.com/zh/idaas/security-authentication/developer-reference/fetchaccesstoken?spm=a2c4g.11186623.0.i1
             */
            if (!TextUtils.isEmpty(aLiAuth.getAccess_token())) {
                accessTokenInfo.setAccess_token(aLiAuth.getAccess_token());
                accessTokenInfo.setRefresh_token(aLiAuth.getAccess_token());
            }
            if (!TextUtils.isEmpty(aLiAuth.getExpire_time())) {
                accessTokenInfo.setExpires_in(Long.valueOf(aLiAuth.getExpire_time()));
            }
            MMKVHelper.putString(ConstantMMVK.ALI_AUTH_UUID, aLiAuth.getAuth_uuid());
            callbackResult.setSuccess(true);
            callbackResult.setMessage("获取accessToken成功。");
            callbackResult.setAccessTokenInfo(accessTokenInfo);

        } catch (Exception e) {
            Log.e(TAG, "getAccessTokenRun exception" + e.getMessage());
            callbackResult.setSuccess(false);
            callbackResult.setMessage(e.getMessage());
        }
        return callbackResult;
    }
}
