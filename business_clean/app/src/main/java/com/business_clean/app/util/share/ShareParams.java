package com.business_clean.app.util.share;

/**
 * 分享参数类:
 * <p>
 * mShareType 分享类型: 1 微信分享，2朋友圈分享 ,3 小程序, 4 短信
 * mTitle 分享标题
 * mText 分享内容
 * mLinkUrl 分享链接
 * mImage 分享的图片，只支持Bitmap
 */
public class ShareParams {
    private int mShareType = 1; //默认为1

    private String mTitle = "";
    private String mDescription = "";
    private String mWWeiXinDescription = "";
    private String mLinkUrl = "";
    private Object mBitmap;
    private boolean integral;

    public void setIntegral(Boolean integral) {
        this.integral = integral;
    }

    public boolean isIntegral() {
        return integral;
    }

    public void setShareType(int shareType) {
        this.mShareType = shareType;
    }


    public void setTitle(String title) {
        this.mTitle = title;
    }

    public void setDescription(String text) {
        this.mDescription = text;
    }


    public void setLinkUrl(String urlString) {
        this.mLinkUrl = urlString;
    }

    public void setBitmap(Object mBitmap) {
        this.mBitmap = mBitmap;
    }

    public int getShareType() {
        return mShareType;
    }

    public String getTitle() {
        return mTitle;
    }

    public String getDescription() {
        return mDescription;
    }

    public String getLinkUrl() {
        return mLinkUrl;
    }

    public Object getBitmap() {
        return mBitmap;
    }

    public void setWWeiXinDescription(String shareContent) {
        this.mWWeiXinDescription = shareContent;
    }

    public String getmWWeiXinDescription() {
        return mWWeiXinDescription;
    }
}
