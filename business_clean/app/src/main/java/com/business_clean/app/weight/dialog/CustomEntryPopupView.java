package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.business_clean.R;
import com.business_clean.app.callback.OnDialogCancelListener;
import com.business_clean.app.callback.OnDialogConfirmCenterListener;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.util.DividerItemDecoration;
import com.business_clean.ui.adapter.BaseStringEntryCenterAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.lxj.xpopup.core.CenterPopupView;

import java.util.List;

public class CustomEntryPopupView extends CenterPopupView {

    private int layout;

    private TextView tvTitle;
    private TextView tvContent;
    private View mBottomViewLine;
    private String title;
    private String msg;
    private OnDialogConfirmCenterListener onConfirmListener;

    private BaseStringEntryCenterAdapter mAdapter;

    private List<String> mlist;

    public CustomEntryPopupView(@NonNull Context context, String title, String msg, List<String> stringList, OnDialogConfirmCenterListener onConfirmListener) {
        super(context);
        this.title = title;
        this.msg = msg;
        this.mlist = stringList;
        this.onConfirmListener = onConfirmListener;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_base_meterial_list;
    }

    @Override
    protected void onCreate() {
        tvTitle = findViewById(R.id.tv_title);
        tvContent = findViewById(R.id.tv_content);
        mBottomViewLine = findViewById(R.id.dialog_material_line);

        RecyclerView recyclerView = findViewById(R.id.recyclerview_base_entry);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerView.addItemDecoration(new DividerItemDecoration(getContext()));
        mAdapter = new BaseStringEntryCenterAdapter();
        recyclerView.setAdapter(mAdapter);
        mAdapter.setList(mlist);

        tvTitle.setText(title);
        tvContent.setText(msg);

        onBindClick();
    }

    private void onBindClick() {
        if (mAdapter != null) {
            mAdapter.setOnItemClickListener(new OnItemClickListener() {
                @Override
                public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                    if (onConfirmListener != null) {
                        onConfirmListener.onConfirm(position);
                    }
                    dialog.dismiss();
                }
            });
        }
    }
}
