package com.business_clean.app.weight.dialog.picker;

import com.bigkoo.pickerview.utils.ChinaDate;
import com.bigkoo.pickerview.utils.LunarCalendar;
import com.contrarywind.adapter.WheelAdapter;

import java.util.Calendar;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * 日期选择组件中 日期 滚轮的适配器
 * 在原来 x日的基础上 增加 周x 展示 ： 12日周五
 */
public class DayWheelAdapter implements WheelAdapter<String> {

    private String[] weekOfDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};

    private boolean sLunar;
    private int sYear;
    private int sMonth;
    private List<String> mItems;

    private boolean isShowWeek;

    public void setYear(int year) {
        sYear = year;
    }

    public void setMonth(int monty) {
        sMonth = monty;
    }

    public void setLunar(boolean isLunar) {
        sLunar = isLunar;
    }

    public DayWheelAdapter setItemsData(List<String> list) {
        mItems = list;
        concatWeek();
        return this;
    }

    public DayWheelAdapter setValues(int minValue, int maxValue) {
        mItems = new LinkedList<>();
        for (int i = minValue; i <= maxValue; i++) {
            mItems.add(String.valueOf(i));
        }
        concatWeek();
        return this;
    }

    public DayWheelAdapter(boolean isShowWeeks) {
        isShowWeek = isShowWeeks;
    }

    @Override
    public int getItemsCount() {
        return mItems == null ? 0 : mItems.size();
    }

    @Override
    public String getItem(int index) {
        if (mItems != null && !mItems.isEmpty() && index >= 0 && index < mItems.size()) {
            return mItems.get(index);
        }
        return "";
    }

    @Override
    public int indexOf(String o) {
        return mItems.indexOf(o);
    }

    private void concatWeek() {
        if (mItems == null || mItems.isEmpty()) {
            return;
        }

//        Date date = new Date();
//        if(sLunar) {
//            int[] solar = getLunarTime();
//            date.setYear(solar[0] - 1900);
//            date.setMonth(solar[1]-1);
//            date.setDate(solar[2]);
//        } else {
//            date.setYear(sYear - 1900);
//            date.setMonth(sMonth-1);
//            date.setDate(1);
//        }
//
//        int weekIndex = getWeekDay(date);
//        if(weekIndex < 0) {
//            return;
//        }

        for (int i = 0; i < mItems.size(); i++) {
            String cache = mItems.get(i) + (sLunar ? "" : isShowWeek ? "日 " + getWeekDay(i + 1) : "日");
            mItems.set(i, cache);
        }
    }

    private boolean isToday(int index) {
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        int currentMonth = calendar.get(Calendar.MONTH) + 1;
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH);
        return currentYear == sYear && currentMonth == sMonth && currentDay == index + 1;
    }


    private String getWeekDay(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(sYear, sMonth - 1, day);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1; // 获取当前星期几，1表示周日，2表示周一，以此类推
        String dayOfWeekString = weekOfDays[dayOfWeek];
        return dayOfWeekString;
    }

    private int getWeekDay(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int week = c.get(Calendar.DAY_OF_WEEK);
        return week - 1;
    }

    private int[] getLunarTime() {
        int year = sYear;
        int month = 1;
        boolean isLeapMonth = false;
        if (ChinaDate.leapMonth(year) == 0) {
            month = sMonth + 1;
        } else {
            if ((sMonth + 1) - ChinaDate.leapMonth(year) <= 0) {
                month = sMonth + 1;
            } else if ((sMonth + 1) - ChinaDate.leapMonth(year) == 1) {
                month = sMonth;
                isLeapMonth = true;
            } else {
                month = sMonth;
            }
        }
        int day = 1;
        int[] solar = LunarCalendar.lunarToSolar(year, month, day, isLeapMonth);
        return solar;
    }
}
