package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.business_clean.app.callback.OnDialogAttendanceResultConfirmListener;
import com.business_clean.app.callback.OnDialogCancelListener;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.SoftHideKeyBoardUtil;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.ui.adapter.BaseStringAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.lxj.xpopup.core.BottomPopupView;
import com.yalantis.ucrop.decoration.GridSpacingItemDecoration;
import com.zhixinhuixue.library.common.ext.DensityExtKt;

import java.util.ArrayList;
import java.util.List;

/**
 * 修改考勤结果
 */
public class CustomAttendanceResultPopup extends BottomPopupView {

    BaseStringAdapter workAdapter = new BaseStringAdapter(1);
    BaseStringAdapter workNoAdapter = new BaseStringAdapter(1);

    private EditText editTextWorkCd;

    private TextView tvWorkCdLeft;
    private TextView tvWorkCdRight;

    private EditText editTextWorkZt;

    private TextView tvWorkZtLeft;
    private TextView tvWorkZtRight;

    private LinearLayout linearLayoutWorkCd;
    private LinearLayout linearLayoutWorkZt;

    private OnDialogAttendanceResultConfirmListener resultConfirmListener;
    private OnDialogCancelListener cancelListener;
    private boolean hideBottomView;

    public CustomAttendanceResultPopup(@NonNull Context context, OnDialogAttendanceResultConfirmListener listener, OnDialogCancelListener cancelListener) {
        super(context);
        this.resultConfirmListener = listener;
        this.cancelListener = cancelListener;
    }

    public CustomAttendanceResultPopup(@NonNull Context context, boolean hideBottomView, OnDialogAttendanceResultConfirmListener listener, OnDialogCancelListener cancelListener) {
        super(context);
        this.resultConfirmListener = listener;
        this.cancelListener = cancelListener;
        this.hideBottomView = hideBottomView;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_attendance_result;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        linearLayoutWorkCd = findViewById(R.id.ll_work_cd);
        linearLayoutWorkZt = findViewById(R.id.ll_work_zt);

        linearLayoutWorkCd.setVisibility(hideBottomView ? View.GONE : View.VISIBLE);
        linearLayoutWorkZt.setVisibility(hideBottomView ? View.GONE : View.VISIBLE);

        editTextWorkCd = findViewById(R.id.dialog_attendance_work_cd_edit);
        tvWorkCdLeft = findViewById(R.id.dialog_attendance_work_cd_left);
        tvWorkCdRight = findViewById(R.id.dialog_attendance_work_cd_right);

        editTextWorkZt = findViewById(R.id.dialog_attendance_work_no_cd_edit);
        tvWorkZtLeft = findViewById(R.id.dialog_attendance_work_no_cd_left);
        tvWorkZtRight = findViewById(R.id.dialog_attendance_work_no_cd_right);

        editTextWorkCd.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    cdLayoutStatus(true);
                }
            }
        });

        editTextWorkZt.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    ztLayoutStatus(true);
                }
            }
        });

        workAdapter.setSingleCancel(true);
        workNoAdapter.setSingleCancel(true);

        //配置RecyclerView
        RecyclerView recyclerViewWork = findViewById(R.id.recycler_work_recycler);
        RecyclerView recyclerViewNoWork = findViewById(R.id.recycler_no_work_recycler);

        recyclerViewWork.setLayoutManager(new GridLayoutManager(getContext(), 2));
        recyclerViewNoWork.setLayoutManager(new GridLayoutManager(getContext(), 2));

        recyclerViewWork.addItemDecoration(new GridSpacingItemDecoration(2, SizeUtils.dp2px(10f), false));
        recyclerViewNoWork.addItemDecoration(new GridSpacingItemDecoration(2, SizeUtils.dp2px(10f), false));

        recyclerViewWork.setAdapter(workAdapter);
        recyclerViewNoWork.setAdapter(workNoAdapter);
        List<String> workStr = new ArrayList<>();
        workStr.add("正常");
        workStr.add("缺卡");
        workAdapter.setList(workStr);
        workNoAdapter.setList(workStr);
        workAdapter.updateItem(0);
        workNoAdapter.updateItem(0);


        workAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                workAdapter.updateItem(position);
                cdLayoutStatus(false);
            }
        });


        workNoAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                workNoAdapter.updateItem(position);
                ztLayoutStatus(false);
            }
        });

        //关闭弹窗
        findViewById(R.id.dialog_choose_classes_close).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });


        //重置
        findViewById(R.id.but_dialog_attendance_re).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                workAdapter.updateItem(-1);
                workNoAdapter.updateItem(-1);
                dismiss();
                if (cancelListener != null) {
                    cancelListener.onCancel();
                }
            }
        });

        //确定
        findViewById(R.id.but_dialog_attendance_ok).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (resultConfirmListener != null) {
                    resultConfirmListener.onDialogAttendanceResultConfirm(workAdapter.getSingleSelect(), editTextWorkCd.getText().toString(),
                            workNoAdapter.getSingleSelect(), editTextWorkZt.getText().toString());
                }
                dismiss();
            }
        });

        //迟到
        linearLayoutWorkCd.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                cdLayoutStatus(true);
            }
        });
        //早退
        linearLayoutWorkZt.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ztLayoutStatus(true);
            }
        });
    }

    private void cdLayoutStatus(boolean click) {
        linearLayoutWorkCd.setBackground(click ? CommonUtils.getBaseSelectedDrawable(linearLayoutWorkCd.getContext()) : CommonUtils.getBaseUnSelectedDrawable(linearLayoutWorkCd.getContext()));
        tvWorkCdLeft.setTextColor(click ? ContextCompat.getColor(tvWorkCdLeft.getContext(), R.color.base_primary) : ContextCompat.getColor(tvWorkCdLeft.getContext(), R.color.base_primary_text_title));
        tvWorkCdRight.setTextColor(click ? ContextCompat.getColor(tvWorkCdRight.getContext(), R.color.base_primary) : ContextCompat.getColor(tvWorkCdRight.getContext(), R.color.base_primary_text_title));
        editTextWorkCd.setTextColor(click ? ContextCompat.getColor(tvWorkCdRight.getContext(), R.color.base_primary) : ContextCompat.getColor(tvWorkCdRight.getContext(), R.color.base_primary_text_title));
        if (click) {
            editTextWorkCd.requestFocus(1000);
            KeyboardUtils.showSoftInput(editTextWorkCd);
            workAdapter.updateItem(2);
        } else {
            editTextWorkCd.clearFocus();
            KeyboardUtils.hideSoftInput(editTextWorkCd);
        }
    }

    private void ztLayoutStatus(boolean click) {
        linearLayoutWorkZt.setBackground(click ? CommonUtils.getBaseSelectedDrawable(linearLayoutWorkZt.getContext()) : CommonUtils.getBaseUnSelectedDrawable(linearLayoutWorkZt.getContext()));
        tvWorkZtLeft.setTextColor(click ? ContextCompat.getColor(tvWorkZtLeft.getContext(), R.color.base_primary) : ContextCompat.getColor(tvWorkZtLeft.getContext(), R.color.base_primary_text_title));
        tvWorkZtRight.setTextColor(click ? ContextCompat.getColor(tvWorkZtRight.getContext(), R.color.base_primary) : ContextCompat.getColor(tvWorkZtRight.getContext(), R.color.base_primary_text_title));
        editTextWorkZt.setTextColor(click ? ContextCompat.getColor(editTextWorkZt.getContext(), R.color.base_primary) : ContextCompat.getColor(editTextWorkZt.getContext(), R.color.base_primary_text_title));
        if (click) {
            editTextWorkZt.requestFocus(1000);
            KeyboardUtils.showSoftInput(editTextWorkZt);
            workNoAdapter.updateItem(2);
        } else {
            editTextWorkZt.clearFocus();
            KeyboardUtils.hideSoftInput(editTextWorkZt);
        }
    }

    @Override
    protected int getMaxHeight() {
        return (int) (DensityExtKt.getScreenHeight() / 1.5);
    }
}
