package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bigkoo.pickerview.adapter.ArrayWheelAdapter;
import com.business_clean.R;
import com.business_clean.app.callback.OnDialogSingleRestDayListener;
import com.contrarywind.listener.OnItemSelectedListener;
import com.contrarywind.view.WheelView;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.BottomPopupView;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import me.hgj.mvvmhelper.ext.LogExtKt;

/**
 * 时分的选择的弹窗
 * 例如：10:25
 */

public class CustomTimeDivisionWheelPopup extends BottomPopupView {

    private String title = "请选择";

    private Context context;

    private WheelView wheelView1;
    private WheelView wheelViewHour;
    private WheelView wheelViewMinute;

    private int selectedPosition1;
    private int selectedPositionHour;
    private int selectedPositionMinute;

    private OnDialogSingleRestDayListener listener;

    private List<String> hours;
    private List<String> minutes;

    private String invertTime;

    private boolean needNextDay;

    public CustomTimeDivisionWheelPopup(@NonNull @NotNull Context context, boolean needNextDay, String invertTime, OnDialogSingleRestDayListener onDialogRestDayListener) {
        super(context);
        this.context = context;
        this.needNextDay = needNextDay;
        this.invertTime = invertTime;
        this.listener = onDialogRestDayListener;
    }


    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_time_division;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        initView();
        initData();
        initClick();
        if (wheelViewHour != null && !TextUtils.isEmpty(invertTime) && invertTime.contains(":") && hours != null) {
            wheelViewHour.setCurrentItem(hours.indexOf(invertTime.substring(0, invertTime.indexOf(":"))));
            selectedPositionHour = hours.indexOf(invertTime.substring(0, invertTime.indexOf(":")));
        }
        if (wheelViewMinute != null && !TextUtils.isEmpty(invertTime) && invertTime.contains(":") && hours != null) {
            wheelViewMinute.setCurrentItem(minutes.indexOf(invertTime.substring(invertTime.indexOf(":") + 1)));
            selectedPositionMinute = minutes.indexOf(invertTime.substring(invertTime.indexOf(":") + 1));
        }
    }

    private void initView() {
        wheelView1 = findViewById(R.id.wheelView_more_1);

        wheelViewHour = findViewById(R.id.wheelView_more_2);
        wheelViewMinute = findViewById(R.id.wheelView_more_3);

        wheelView1.setCyclic(false);
        wheelViewMinute.setCyclic(false);
        wheelViewHour.setCyclic(false);
    }


    private void initData() {
        ArrayList<String> days = new ArrayList<>();
        days.add("当日");
        if (needNextDay) {
            days.add("次日");
        }

        hours = new ArrayList<>();
        for (int i = 0; i <= 23; i++) {
            String hour = String.format("%02d", i);
            hours.add(hour);
        }
        minutes = new ArrayList<>();
        for (int i = 0; i <= 59; i++) {
            minutes.add(String.format("%02d", i));
        }


        // 设置WheelView的适配器和数据源
        wheelView1.setAdapter(new ArrayWheelAdapter(days));
        wheelViewHour.setAdapter(new ArrayWheelAdapter(hours));
        wheelViewMinute.setAdapter(new ArrayWheelAdapter(minutes));
    }

    private void initClick() {


        wheelView1.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPosition1 = index;
            }
        });


        wheelViewHour.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPositionHour = index;
            }
        });

        wheelViewMinute.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(int index) {
                selectedPositionMinute = index;
            }
        });


        findViewById(R.id.tv_common_dialog_cancel).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        TextView tv_common_dialog_center = findViewById(R.id.tv_common_dialog_center);
        tv_common_dialog_center.setText(title);

        TextView btnConfirm = findViewById(R.id.tv_common_dialog_yes);
        btnConfirm.setTextColor(XPopup.getPrimaryColor());
        btnConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (listener != null) {
                    //当日  时间段 次日 时间段
                    String startTime = hours.get(selectedPositionHour) + ":" + minutes.get(selectedPositionMinute);
                    listener.selected(selectedPosition1, startTime);
                }
                LogExtKt.logE("选择的内容下表" + 0 + " ; " + selectedPositionMinute, "");
            }
        });
    }
}
