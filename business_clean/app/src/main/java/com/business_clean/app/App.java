package com.business_clean.app;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStore;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.multidex.MultiDex;

import com.blankj.utilcode.util.LogUtils;
import com.business_clean.BuildConfig;
import com.business_clean.app.config.AppTaskFactory;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.config.InitComm;
import com.business_clean.app.config.InitDefault;
import com.business_clean.app.config.InitMap;
import com.business_clean.app.config.InitNetWork;
import com.business_clean.app.config.InitUtils;
import com.business_clean.app.event.AppViewModel;
import com.business_clean.app.util.MMKVHelper;
import com.effective.android.anchors.AnchorsManager;
import com.effective.android.anchors.Project;
import me.hgj.mvvmhelper.base.Ktx;

public class App extends Application implements ViewModelStoreOwner {

    private static App instance;
    private static AppViewModel appViewModelInstance;

    private ViewModelProvider.Factory mFactory = null;
    private ViewModelStore mAppViewModelStore = new ViewModelStore();

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;

        MultiDex.install(this);
        LogUtils.getConfig().setLogSwitch(true);
        Ktx.INSTANCE.init(this, BuildConfig.DEBUG);

        mAppViewModelStore = new ViewModelStore();

        // 获取并初始化全局 ViewModel
        appViewModelInstance = getAppViewModelProvider().get(AppViewModel.class);

        // 初始化 MMKV
        MMKVHelper.initialize(getApplicationContext(), Constant.INTERNAL_MMKV_PATH);

        // 是否第一次打开 App
        boolean first = MMKVHelper.getBoolean(ConstantMMVK.FIRST_ENTRY, false);
        if (first) {
            onMainProcessInit();
        }
    }

    /**
     * 异步初始化任务
     */
    private void onMainProcessInit() {
        AnchorsManager.getInstance()
                .debuggable(BuildConfig.DEBUG)
                .addAnchor(
                        InitDefault.TASK_ID,
                        InitNetWork.TASK_ID,
                        InitUtils.TASK_ID,
                        InitComm.TASK_ID,
                        InitMap.TASK_ID
                )
                .start(
                        new Project.Builder("business_clean", new AppTaskFactory())
                                .add(InitDefault.TASK_ID)
                                .add(InitNetWork.TASK_ID)
                                .add(InitComm.TASK_ID)
                                .add(InitUtils.TASK_ID)
                                .add(InitMap.TASK_ID)
                                .build()
                );
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        // 销毁 GeoCoderUtil
    }

    public static App getInstance() {
        return instance;
    }

    public static AppViewModel getAppViewModelInstance() {
        return appViewModelInstance;
    }

    private ViewModelProvider getAppViewModelProvider() {
        return new ViewModelProvider(this, getAppFactory());
    }

    private ViewModelProvider.Factory getAppFactory() {
        if (mFactory == null) {
            mFactory = ViewModelProvider.AndroidViewModelFactory.getInstance(this);
        }
        return mFactory;
    }

    private ViewModelStore getCustomViewModelStore() {
        return mAppViewModelStore;
    }

    @NonNull
    @Override
    public ViewModelStore getViewModelStore() {
        return getCustomViewModelStore();
    }
}