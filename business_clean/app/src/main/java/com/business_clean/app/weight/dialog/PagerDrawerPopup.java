package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.callback.OnProjectAllDataCallBack;
import com.business_clean.app.config.AsyncRequestUtil;
import com.business_clean.app.config.Constant;
import com.business_clean.app.util.DividerItemDecoration;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.data.mode.project.ProjectManager;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.business_clean.ui.adapter.custom.CustomMangerNoPaddingAllAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemChildClickListener;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.lxj.xpopup.core.BottomPopupView;
import com.zhixinhuixue.library.common.ext.DensityExtKt;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;


/**
 * 全局 选择 项目的内容
 */
public class PagerDrawerPopup extends BottomPopupView {

    private RecyclerView recyclerView;
    private TextView tvProjectManger;
    private Context mContext;

    private CustomMangerNoPaddingAllAdapter mAdapter;

    private OnSelectProjectListener listener;

    private boolean isChangeAppProject = true;//是否切换app的项目 默认是true

    private boolean isNeedAll = false;//是否显示All

    private EditText etSearch;


    private String project_uuid = "";//是否优先对比uuid 来反选，负责就是通过全局的LiveData 来反选

    //是否需要总部成员
    private boolean isHeadOffice = false;

    public void setHeadOffice(boolean headOffice) {
        isHeadOffice = headOffice;
    }

    public PagerDrawerPopup(@NonNull Context context) {
        super(context);
        this.mContext = context;
    }

    public PagerDrawerPopup(@NonNull Context context, String project_uuid) {
        super(context);
        this.mContext = context;
        this.project_uuid = project_uuid;
    }


    public PagerDrawerPopup(@NonNull Context context, OnSelectProjectListener listener) {
        super(context);
        this.mContext = context;
        this.listener = listener;
    }


    public PagerDrawerPopup(@NonNull Context context, String project_uuid, boolean isChangeAppProject, boolean isNeedAll, OnSelectProjectListener listener) {
        super(context);
        this.mContext = context;
        this.project_uuid = project_uuid;
        this.isNeedAll = isNeedAll;
        this.isChangeAppProject = isChangeAppProject;
        this.listener = listener;
    }

    public PagerDrawerPopup(@NonNull Context context, String project_uuid, boolean isChangeAppProject, OnSelectProjectListener listener) {
        super(context);
        this.mContext = context;
        this.project_uuid = project_uuid;
        this.isChangeAppProject = isChangeAppProject;
        this.listener = listener;
    }

    public PagerDrawerPopup(@NonNull Context context, boolean isChangeAppProject, OnSelectProjectListener listener) {
        super(context);
        this.mContext = context;
        this.isChangeAppProject = isChangeAppProject;
        this.listener = listener;
    }

    public PagerDrawerPopup(@NonNull Context context, boolean isChangeAppProject, boolean isNeedAll, OnSelectProjectListener listener) {
        super(context);
        this.mContext = context;
        this.isChangeAppProject = isChangeAppProject;
        this.isNeedAll = isNeedAll;
        this.listener = listener;
    }

    public PagerDrawerPopup(@NonNull Context context, boolean isChangeAppProject, boolean isNeedAll, String project_uuid, OnSelectProjectListener listener) {
        super(context);
        this.mContext = context;
        this.isChangeAppProject = isChangeAppProject;
        this.isNeedAll = isNeedAll;
        this.project_uuid = project_uuid;
        this.listener = listener;
    }


    @Override
    protected int getImplLayoutId() {
        return R.layout.home_drawer;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        etSearch = findViewById(R.id.et_base_search);
        recyclerView = findViewById(R.id.recyclerview_home_drawer);
        tvProjectManger = findViewById(R.id.tv_project_manager);
        recyclerView.setLayoutManager(new LinearLayoutManager(mContext));
        mAdapter = new CustomMangerNoPaddingAllAdapter();
        recyclerView.addItemDecoration(new DividerItemDecoration(mContext, SizeUtils.dp2px(16f), SizeUtils.dp2px(0f)));
        recyclerView.setAdapter(mAdapter);


        View empty = View.inflate(mContext, R.layout.layout_empty, null);
        TextView tvEmpty = empty.findViewById(R.id.tv_empty_data);
        tvEmpty.setText("暂无可切换的项目");
        mAdapter.setEmptyView(empty);


        AsyncRequestUtil.requestAppointProjectAll(isHeadOffice, new OnProjectAllDataCallBack() {
            @Override
            public void cityDataCallBack(List<ProjectMangerList> list) {
                if (isNeedAll) {
                    List<ProjectMangerList> mangerLists = new ArrayList<>(list != null ? list : Collections.emptyList());

                    if (!Constant.ROLE_CLEANER || !Constant.ROLE_LEADER || !Constant.ROLE_PROJECT_OWNER) {
                        ProjectMangerList data = new ProjectMangerList();
                        data.setUuid("");
                        data.setProject_short_name("全部项目");
                        mangerLists.add(0, data);
                    }
                    mAdapter.setList(mangerLists);
                    mAdapter.updateChoose(project_uuid);
                } else {
                    mAdapter.setList(list);
                    if (!TextUtils.isEmpty(project_uuid)) {
                        mAdapter.updateChoose(project_uuid);
                    } else {
                        // 安全地获取用户信息和项目UUID
                        try {
                            if (App.getAppViewModelInstance() != null &&
                                App.getAppViewModelInstance().getUserInfo() != null &&
                                App.getAppViewModelInstance().getUserInfo().getValue() != null &&
                                App.getAppViewModelInstance().getUserInfo().getValue().getProject() != null &&
                                App.getAppViewModelInstance().getUserInfo().getValue().getProject().getUuid() != null) {
                                mAdapter.updateChoose(App.getAppViewModelInstance().getUserInfo().getValue().getProject().getUuid());
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            // 如果获取失败，使用空字符串作为默认值
                            mAdapter.updateChoose("");
                        }
                    }
                }
            }
        });


        if (Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER || Constant.ROLE_REGIONAL_MANAGER) {
            tvProjectManger.setVisibility(View.VISIBLE);
        } else {
            tvProjectManger.setVisibility(View.GONE);
        }

        //监听项目
        App.getAppViewModelInstance().getAllProjectManager().observe(this, new Observer<ProjectManager>() {
            @Override
            public void onChanged(ProjectManager projectManager) {
                try {
                    if (mAdapter != null && projectManager != null && projectManager.getList() != null) {
                        mAdapter.setList(projectManager.getList());

                        // 安全地获取并设置选中的项目UUID
                        try {
                            if (App.getAppViewModelInstance() != null &&
                                App.getAppViewModelInstance().getUserInfo() != null &&
                                App.getAppViewModelInstance().getUserInfo().getValue() != null &&
                                App.getAppViewModelInstance().getUserInfo().getValue().getProject() != null &&
                                App.getAppViewModelInstance().getUserInfo().getValue().getProject().getUuid() != null) {
                                mAdapter.updateChoose(App.getAppViewModelInstance().getUserInfo().getValue().getProject().getUuid());
                            } else {
                                mAdapter.updateChoose("");
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            mAdapter.updateChoose("");
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        if (mAdapter != null) {
            mAdapter.setOnItemClickListener(new OnItemClickListener() {
                @Override
                public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                    try {
                        // 安全检查：确保适配器数据有效
                        if (mAdapter == null || mAdapter.getData() == null ||
                            position < 0 || position >= mAdapter.getData().size()) {
                            return;
                        }

                        ProjectMangerList selectedProject = mAdapter.getData().get(position);
                        if (selectedProject == null) {
                            return;
                        }

                        dialog.dismiss();

                        if (!isChangeAppProject) { //不切换只返回项目
                            if (listener != null && selectedProject.getUuid() != null && selectedProject.getProject_short_name() != null) {
                                listener.onClick(selectedProject.getUuid(), selectedProject.getProject_short_name());
                            }
                            return;
                        }

                        // 安全显示Toast
                        String projectName = selectedProject.getProject_short_name();
                        if (projectName != null) {
                            ToastUtil.show("成功切换为" + projectName);
                        }

                        // 安全更新选择
                        String projectUuid = selectedProject.getUuid();
                        if (projectUuid != null) {
                            mAdapter.updateChoose(projectUuid);
                        }

                        // 安全设置用户项目信息
                        try {
                            if (App.getAppViewModelInstance() != null &&
                                App.getAppViewModelInstance().getUserInfo() != null &&
                                App.getAppViewModelInstance().getUserInfo().getValue() != null) {
                                App.getAppViewModelInstance().getUserInfo().getValue().setProject(selectedProject);
                                App.getAppViewModelInstance().getProjectInfo().setValue(selectedProject);

                                if (projectUuid != null) {
                                    AsyncRequestUtil.requestChangeProject(projectUuid);
                                }
                                AsyncRequestUtil.requestUserInfoData();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        // 安全回调
                        if (listener != null && projectUuid != null && projectName != null) {
                            listener.onClick(projectUuid, projectName);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        // 发生异常时也要关闭对话框
                        try {
                            dialog.dismiss();
                        } catch (Exception dismissException) {
                            dismissException.printStackTrace();
                        }
                    }
                }
            });

            mAdapter.setOnItemChildClickListener(new OnItemChildClickListener() {
                @Override
                public void onItemChildClick(@NonNull BaseQuickAdapter adapter, @NonNull View view, int position) {
                    if (view.getId() == R.id.ck_item_choose) {
                        try {
                            // 安全检查：确保适配器数据有效
                            if (mAdapter == null || mAdapter.getData() == null ||
                                position < 0 || position >= mAdapter.getData().size()) {
                                return;
                            }

                            ProjectMangerList selectedProject = mAdapter.getData().get(position);
                            if (selectedProject == null) {
                                return;
                            }

                            dialog.dismiss();

                            if (!isChangeAppProject) { //不切换只返回项目
                                if (listener != null && selectedProject.getUuid() != null && selectedProject.getProject_short_name() != null) {
                                    listener.onClick(selectedProject.getUuid(), selectedProject.getProject_short_name());
                                }
                                return;
                            }

                            // 安全设置用户项目信息
                            try {
                                if (App.getAppViewModelInstance() != null &&
                                    App.getAppViewModelInstance().getUserInfo() != null &&
                                    App.getAppViewModelInstance().getUserInfo().getValue() != null) {
                                    App.getAppViewModelInstance().getUserInfo().getValue().setProject(selectedProject);
                                    App.getAppViewModelInstance().getProjectInfo().setValue(selectedProject);

                                    String projectUuid = selectedProject.getUuid();
                                    if (projectUuid != null) {
                                        AsyncRequestUtil.requestChangeProject(projectUuid);
                                    }
                                    AsyncRequestUtil.requestUserInfoData();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            // 安全回调
                            if (listener != null && selectedProject.getUuid() != null && selectedProject.getProject_short_name() != null) {
                                listener.onClick(selectedProject.getUuid(), selectedProject.getProject_short_name());
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            // 发生异常时也要关闭对话框
                            try {
                                dialog.dismiss();
                            } catch (Exception dismissException) {
                                dismissException.printStackTrace();
                            }
                        }
                    }
                }
            });
        }


        findViewById(R.id.tv_dialog_base_bottom_cancel).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        //管理
        tvProjectManger.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {

                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("ProjectManagerPage")
                        .arguments(new HashMap<>())
                        .build());
//                ActivityForwardUtil.startActivity(ProjectManagerActivity.class);
            }
        });

        //输入内容对列表进行搜索
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                try {
                    if (mAdapter != null && s != null) {
                        mAdapter.filter(s.toString());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    protected int getMaxHeight() {
        return (int) (DensityExtKt.getScreenHeight() / 1.5);
    }


    @Override
    protected void onShow() {
        super.onShow();
        Log.e("tag", "PagerDrawerPopup onShow");
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        Log.e("tag", "PagerDrawerPopup onDismiss");
    }


    public interface OnSelectProjectListener {
        void onClick(String project_uuid, String project_name);
    }


}

