package com.business_clean.app.util;

import androidx.annotation.ArrayRes;
import androidx.annotation.StringRes;

import com.blankj.utilcode.util.Utils;

import java.util.Arrays;
import java.util.List;
import java.util.MissingFormatArgumentException;

public final class ResourceUtils {


    /**
     * 根据字符串id进行格式化对应样式
     *
     * @param id   对应的strings文件中的id
     * @param args 格式化的参数
     * @return 格式化后的文本
     */
    public static String getStringById(@StringRes int id, Object... args) {
        if (args.length == 0) {
            return Utils.getApp().getResources().getString(id);
        }
        try {
            return String.format(Utils.getApp().getResources().getString(id), args);
        } catch (MissingFormatArgumentException e) {
            return "";
        }
    }

    /**
     * 根据字符串id进行格式化对应样式
     *
     * @param id 对应的strings文件中的id
     * @return 格式化后的文本
     */
    public static List<CharSequence> getArrayById(@ArrayRes int id) {
        CharSequence[] textArray = Utils.getApp().getResources().getTextArray(id);
        return Arrays.asList(textArray);
    }

}