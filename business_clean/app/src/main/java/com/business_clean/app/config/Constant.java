package com.business_clean.app.config;

import com.blankj.utilcode.util.PathUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import me.hgj.mvvmhelper.base.Ktx;

/**
 * 其他的存储
 */
public class Constant {

    public static final String WECHAT_APP_ID = "wxbd7707b650ef5060";

    //企业微信的包名
    public static final String WWEIXIN_PACKAGE = "com.tencent.wework";

    public static final int TYPE_INIT_DATA = 1;
    public static final int TYPE_INIT_LOAD_MORE = 2;
    public static final int PAGE_SIZE = 20;

    //服务协议
    public static final String SERVICE_AGREEMENT = "https://m2.jiazhengye.cn/help/listDetail?number=6724072763435721";
    //隐私政策
    public static final String PRIVACY_POLICY = "https://m2.jiazhengye.cn/help/listDetail?number=6724072759259768";
    public static final String USER_OFF = "https://m2.jiazhengye.cn/help/listDetail?number=6725010834765071";
    ///联系方式
    public static final String CONTACT = "https://m2.jiazhengye.cn/help/listDetail?number=6725011470171309";

    public static boolean AUTH_ALI = false;

    //经纬度
    public static Double LATITUDE = 0.0;
    public static Double LONGITUDE = 0.0;
    //地址
    public static String ADDRESS = "无定位地址";

    /****************************************************************/
    //记录当前账号的身份 角色 1管理员 2项目负责人 3人事 4领班 5保洁
    public static String ROLE_ID = "";
    public static boolean ROLE_SUPER_MANGER = false;//超级管理员
    public static boolean ROLE_MANGER = false;//管理员
    public static boolean ROLE_PROJECT_OWNER = false;//项目负责人

    public static boolean ROLE_HR = false;//人事
    public static boolean ROLE_LEADER = false;//领班
    public static boolean ROLE_CLEANER = false;//保洁员
    public static boolean ROLE_REGIONAL_MANAGER = false;//大区经理

    //对比角色id的时候用
    public static String ROLE_SUPER_MANGER_ID = "-1";//超级管理员
    public static String ROLE_MANGER_ID = "1";//管理员
    public static String ROLE_PROJECT_OWNER_ID = "2";//项目负责人
    public static String ROLE_HR_ID = "3";//人事
    public static String ROLE_LEADER_ID = "4";//领班
    public static String ROLE_CLEAN_ID = "5";//保洁
    public static String ROLE_REGIONAL_MANAGER_ID = "6";//大区经理

    /****************************************************************/

    //是否可以集体打卡
    public static boolean IS_COLLECTIVE_CLOCK_IN = false;


    //记录相机用户操作权限
    public static boolean CAMERA_MARK_PHOTO = false;
    //是否保存 视频、照片到系统相册
    public static boolean CAMERA_SAVE_PHOTO_VIDEO = false;
    //是否保存原始照片
    public static boolean CAMERA_SAVE_ORIGINAL_PHOTO = false;

    //图片存储的路径 外部`
    public static final String EXTERNAL_PHOTO_PATH = PathUtils.getExternalPicturesPath() + "/熊猫清洁云/";
    //视频存储的路径 外部
    public static final String EXTERNAL_VIDEO_PATH = PathUtils.getExternalMoviesPath() + "/熊猫清洁云/";
    //内部路径
    public static final String INTERNAL_PHOTO_PATH = PathUtils.getInternalAppDataPath() + "/images/";
    public static final String INTERNAL_VIDEO_PATH = PathUtils.getInternalAppDataPath() + "/video/";

    //本地文件的路径
    public static final String INTERNAL_MMKV_PATH = PathUtils.getInternalAppDataPath() + "/mmkv/";

    //压缩的路径
    public static final String INTERNAL_LUBAN_PATH = PathUtils.getExternalAppCachePath();


    //内部签名存储路径
    public static final String INTERNAL_SIGN_HAND_PATH = PathUtils.getInternalAppDataPath();

    //外部的下载路径
    public static final String EXTERNAL_DOWNLOAD_PATH = PathUtils.getExternalDownloadsPath();

    //app更新的通知栏
    public static final String channelDownload = "channelDownload";


    // 与js 交互的命名空间以及固定写法
    public static final String JS_JAVA_SCRIPT = "javascript:";
    public static final String JS_SPACE_NAME = "WebViewJavascriptBridge";
    public static final String JS_SPACE_CALLBACK = "CallBack";

    // 设置重启的开关的标签
    public static final String CLEAN_TAG_REBOOT = "net.clean.reboot";//只要net.开头就好,剩下的可以自定义
    public static final String IS_REBOOT_SET = "true";//根据自己喜好定义
}
