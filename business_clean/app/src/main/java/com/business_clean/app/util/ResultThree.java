package com.business_clean.app.util;

public class ResultThree<R1, R2, R3> extends ResultDouble<R1, R2> {
    public final R3 returnValue3;

    public ResultThree(R1 returnValue1, R2 returnValue2, R3 returnValue3) {
        super(returnValue1, returnValue2);
        this.returnValue3 = returnValue3;
    }

    @Override
    public String toString() {
        return super.toString() + " , " + returnValue3.toString();
    }
}
