package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.business_clean.ui.adapter.BaseStringAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.lxj.xpopup.core.BottomPopupView;
import com.yalantis.ucrop.decoration.GridSpacingItemDecoration;

import java.util.ArrayList;
import java.util.List;

/**
 * 修改考勤结果
 */
public class CustomAttendanceHolidayPopup extends BottomPopupView {

    public CustomAttendanceHolidayPopup(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_attendance_holiday;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        //关闭弹窗
        findViewById(R.id.dialog_choose_classes_close).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

}
