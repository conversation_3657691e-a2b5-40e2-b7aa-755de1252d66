package com.business_clean.app.fdd;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.util.Log;
import android.webkit.WebResourceRequest;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import com.blankj.utilcode.util.LogUtils;
import com.business_clean.R;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.flutter.BoostEventListener;
import com.business_clean.app.util.ToastUtil;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.idlefish.flutterboost.FlutterBoost;
import com.qiniu.android.utils.LogUtil;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import java.io.File;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2022/7/28
 * @description
 */
public class FddWebActivity extends AppCompatActivity {

    private static final String TAG = "FddWebActivity";
    private H5FaceWebChromeClient webViewClient;
    private static final int PERMISSION_QUEST_TRTC_CAMERA_VERIFY = 12;
    private static final int PERMISSION_QUEST_CAMERA_RECORD_VERIFY = 11;
    private AlertDialog dialog;
    // android 5.0以下系统
    private boolean belowApi21;

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_fdd);
        WebView mWebView = findViewById(R.id.webview);

        String url = getIntent().getExtras().getString("URL");
        LogUtil.d("---url111111----" + url);

        mWebView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                return super.shouldOverrideUrlLoading(view, request);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
            }
        });

        webViewClient = new H5FaceWebChromeClient(FddWebActivity.this);
        mWebView.setWebChromeClient(webViewClient);
        WBH5FaceVerifySDK.getInstance().setWebViewSettings(this, mWebView, getApplicationContext());
        //加载
        mWebView.loadUrl(url);
    }


    @Override
    protected void onRestart() {
        super.onRestart();
        //重新构建一下activity 如果有权限，就不构建了
        XXPermissions.with(this).permission(Permission.CAMERA, Permission.ACCESS_FINE_LOCATION,
                        getExternalStorage(), Permission.RECORD_AUDIO)
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                        if (allGranted) {
                        } else {
                            recreate();
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        Log.e(TAG, "onActivityResult --------" + requestCode);
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 0x11) {
            //收到录制模式调用系统相机录制完成视频的结果
            Log.e(TAG, "onActivityResult recordVideo");
            if (WBH5FaceVerifySDK.getInstance().receiveH5FaceVerifyResult(requestCode, resultCode, data)) {
                return;
            }
        } else if (requestCode == PERMISSION_QUEST_TRTC_CAMERA_VERIFY) {
            Log.e(TAG, "onActivityResult camera");
            requestCameraPermission();
        } else if (requestCode == PERMISSION_QUEST_CAMERA_RECORD_VERIFY) {
            Log.e(TAG, "onActivityResult cameraAndSome");
            requestCameraAndSomePermissions(false);
        } else {
            WBH5FaceVerifySDK.getInstance().aa(requestCode, resultCode, data);
        }
    }

    /**
     * 针对trtc录制模式，申请相机权限
     */
    public void requestCameraPermission() {
        if (checkSdkPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "checkSelfPermission false");
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                //23+的情况
                if (ActivityCompat.shouldShowRequestPermissionRationale(FddWebActivity.this, Manifest.permission.CAMERA)) {
                    //用户之前拒绝过，这里返回true
                    Log.e(TAG, "shouldShowRequestPermissionRationale true");
                } else {
                    Log.e(TAG, "shouldShowRequestPermissionRationale false");
                }
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
            } else {
                Log.e(TAG, "23以下没法系统弹窗动态申请权限，只能用户跳转设置页面，自己打开");
                openAppDetail(PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
            }
        } else {
            Log.e(TAG, "checkSelfPermission true");
            webViewClient.enterTrtcFaceVerify();
        }
    }

    private int checkSdkPermission(String permission) {
        int permissionResult = 0;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (permission.equals(Permission.MANAGE_EXTERNAL_STORAGE)) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    permissionResult = Environment.isExternalStorageManager() ? 0 : -1;
                }
            } else {
                permissionResult = ContextCompat.checkSelfPermission(this, permission);
            }
            Log.e(TAG, "checkSdkPermission >=23 " + permissionResult + " permission=" + permission);
        } else {
            permissionResult = getPackageManager().checkPermission(permission, getPackageName());
            Log.e(TAG, "checkSdkPermission <23 =" + permissionResult + " permission=" + permission);
        }
        return permissionResult;
    }

    /**
     * 针对老的录制模式，申请相机等其他权限
     */
    public void requestCameraAndSomePermissions(boolean belowApi21) {
        Log.e(TAG, "requestCameraAndSomePermissionsNew");
        this.belowApi21 = belowApi21;
        if (checkSdkPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED ||
                checkSdkPermission(Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED ||
                checkSdkPermission(getExternalStorage()) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "checkSelfPermissionNew false");
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                //23+的情况：用户之前拒绝过，这个返回true
                if (ActivityCompat.shouldShowRequestPermissionRationale(FddWebActivity.this, Manifest.permission.CAMERA) ||
                        ActivityCompat.shouldShowRequestPermissionRationale(FddWebActivity.this, Manifest.permission.RECORD_AUDIO) ||
                        ActivityCompat.shouldShowRequestPermissionRationale(FddWebActivity.this, getExternalStorage())) {
                    //用户同时点选了拒绝开启权限和不再提醒后才会true
                    Log.e(TAG, "shouldShowRequestPermissionRationale true");
                } else {
                    Log.e(TAG, "shouldShowRequestPermissionRationale false");
                }
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO, getExternalStorage()}, PERMISSION_QUEST_CAMERA_RECORD_VERIFY);
            } else {
                //23以下没法系统弹窗动态申请权限，只能用户跳转设置页面，自己打开
                openAppDetail(PERMISSION_QUEST_CAMERA_RECORD_VERIFY);
            }
        } else {
            webViewClient.enterOldModeFaceVerify(belowApi21);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case PERMISSION_QUEST_TRTC_CAMERA_VERIFY:
                // trtc 模式
                if (grantResults.length > 0) {
                    if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                        Log.e(TAG, "onRequestPermissionsResult grant");
                        webViewClient.enterTrtcFaceVerify();
                    } else if (grantResults[0] == PackageManager.PERMISSION_DENIED && ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.CAMERA) == false) {
                        Log.e(TAG, "onRequestPermissionsResult deny");
                        openAppDetail(PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
                    } else {
                        Log.e(TAG, "拒绝权限并且之前没有点击不再提醒");
                        //权限被拒绝
                        askPermissionError();
                    }
                }
                break;
            case PERMISSION_QUEST_CAMERA_RECORD_VERIFY:
                if (grantResults.length > 0) {
                    if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                        if (grantResults[1] == PackageManager.PERMISSION_GRANTED) {
                            if (grantResults[2] == PackageManager.PERMISSION_GRANTED) {
                                Log.e(TAG, "onRequestPermissionsResult all grant");
                                webViewClient.enterOldModeFaceVerify(belowApi21);
                            } else {
                                if (ActivityCompat.shouldShowRequestPermissionRationale(this, getExternalStorage()) == false) {
                                    if (getExternalStorage().equals(Permission.MANAGE_EXTERNAL_STORAGE)) {
                                        requestManageExternalStoragePermission(FddWebActivity.this, true);
                                    } else {
                                        Toast.makeText(FddWebActivity.this, "请前往设置->应用->权限中打开存储权限，否则功能无法正常运行", Toast.LENGTH_LONG).show();
                                        Log.e(TAG, "onRequestPermissionsResult  sdcard shouldShowRequest false");
                                        //权限被拒绝
                                        openAppDetail(PERMISSION_QUEST_CAMERA_RECORD_VERIFY);
                                    }
                                } else {
                                    Log.e(TAG, "onRequestPermissionsResult  sdcard deny");
                                    askPermissionError();
                                }
                            }
                        } else {
                            if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.RECORD_AUDIO) == false) {
                                Toast.makeText(FddWebActivity.this, "请前往设置->应用->权限中打开录制权限，否则功能无法正常运行", Toast.LENGTH_LONG).show();
                                //权限被拒绝
                                openAppDetail(PERMISSION_QUEST_CAMERA_RECORD_VERIFY);
                            } else {
                                Log.e(TAG, "onRequestPermissionsResult  record deny");
                                askPermissionError();
                            }
                        }
                    } else {
                        if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.CAMERA) == false) {
                            Toast.makeText(FddWebActivity.this, "请前往设置->应用->权限中打开相机权限，否则功能无法正常运行", Toast.LENGTH_LONG).show();
                            //权限被拒绝
                            openAppDetail(PERMISSION_QUEST_CAMERA_RECORD_VERIFY);
                        } else {
                            Log.e(TAG, "onRequestPermissionsResult  camera deny");
                            askPermissionError();
                        }
                    }
                }
                break;
            default:
                break;
        }
    }

    private void openAppDetail(int requestCode) {
        showWarningDialog(requestCode);
    }

    private void showWarningDialog(final int requestCode) {
        dialog = new AlertDialog.Builder(this)
                .setTitle("权限申请提示")
                .setMessage("请前往设置->应用->权限中打开相关权限，否则功能无法正常运行！")
                .setPositiveButton("确定", (dialogInterface, which) -> {
                    // 一般情况下如果用户不授权的话，功能是无法运行的，做退出处理,合作方自己根据自身产品决定是退出还是停留
                    if (dialog != null && dialog.isShowing()) {
                        dialog.dismiss();
                    }
                    dialog = null;
                    enterSettingActivity(requestCode);
                }).setNegativeButton("取消", (dialogInterface, which) -> {
                    if (dialog != null && dialog.isShowing()) {
                        dialog.dismiss();
                    }
                    dialog = null;
                    if (!isFinishing()) {
                        finish();
                    }
                }).setCancelable(false).show();
    }

    private void askPermissionError() {
        Toast.makeText(FddWebActivity.this, "用户拒绝了权限,退出刷脸", Toast.LENGTH_SHORT).show();
        if (!isFinishing()) {
            finish();
        }
    }

    private void enterSettingActivity(int requestCode) {
        //部分插件化框架中用Activity.getPackageName拿到的不一定是宿主的包名，所以改用applicationContext获取
        String packageName = getApplicationContext().getPackageName();
        Uri uri = Uri.fromParts("package", packageName, null);
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, uri);
        ResolveInfo resolveInfo = getPackageManager().resolveActivity(intent, 0);
        if (resolveInfo != null) {
            startActivityForResult(intent, requestCode);
        }
    }

    public String getExternalStorage() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // 检查 MANAGE_EXTERNAL_STORAGE 权限
            LogUtils.e("这是新版本获取权限");
            return Permission.MANAGE_EXTERNAL_STORAGE;   //
        }
        // 对于低于 Android 11 的版本，权限会有所不同
        return Permission.WRITE_EXTERNAL_STORAGE; // 默认返回 true，因为在老版本中不需要这个权限
    }


    //Android11以上 请求管理外部存储的权限。
    public void requestManageExternalStoragePermission(Context context, boolean isShowDialog) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (isShowDialog) {
                CommonUtils.showGeneralDialog(context, "授权提示", "为了您可正常使用该功能，请先打开文件访问权限", "暂不授权", "去授权", () -> {
                }, () -> {
                    // 跳转到设置页面，以请求用户授予权限
                    Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                    Uri uri = Uri.fromParts("package", context.getPackageName(), null);
                    intent.setData(uri);
                    context.startActivity(intent);
                });
            } else {
                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                Uri uri = Uri.fromParts("package", context.getPackageName(), null);
                intent.setData(uri);
                context.startActivity(intent);
            }
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        FlutterBoost.instance().sendEventToFlutter("refresh", null);
        FlutterBoost.instance().sendEventToFlutter("ContractFddAuth", new HashMap<String, Object>());
        if (dialog != null) {
            if (dialog.isShowing()) {
                dialog.dismiss();
            }
            dialog = null;
        }
    }
}