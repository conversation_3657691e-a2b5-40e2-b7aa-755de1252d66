package com.business_clean.app.base

import android.os.Bundle
import androidx.databinding.ViewDataBinding
import com.business_clean.app.ext.hideSoftKeyboard
import me.hgj.mvvmhelper.base.BaseVbFragment
import me.hgj.mvvmhelper.base.BaseViewModel

/**
 * 作者　: hega<PERSON>jian
 * 时间　: 2019/12/21
 * 描述　: 你项目中的Fragment基类，在这里实现显示弹窗，吐司，还有自己的需求操作 ，如果不想用Databind，请继承
 * BaseVmFragment例如
 * abstract class BaseFragment<VM : BaseViewModel> : BaseVmFragment<VM>() {
 */
abstract class BaseFragment<VM : BaseViewModel, DB : ViewDataBinding> : BaseVbFragment<VM, DB>() {


    abstract override fun initView(savedInstanceState: Bundle?)


    override fun onPause() {
        super.onPause()
        hideSoftKeyboard(activity)
    }

}