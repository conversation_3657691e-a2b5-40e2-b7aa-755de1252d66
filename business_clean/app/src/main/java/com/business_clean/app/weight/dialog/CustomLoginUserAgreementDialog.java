package com.business_clean.app.weight.dialog;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.business_clean.R;
import com.business_clean.app.config.Constant;
import com.business_clean.app.ext.CommonUtils;
import com.lxj.xpopup.core.CenterPopupView;
import com.lxj.xpopup.interfaces.OnConfirmListener;

/**
 * 打卡成功要去显示的内容
 */
public class CustomLoginUserAgreementDialog extends CenterPopupView {


    private OnConfirmListener listener;

    private TextView tvDialogDesc;

    public CustomLoginUserAgreementDialog(@NonNull Context context, OnConfirmListener listener) {
        super(context);
        this.listener = listener;
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_login_user_agreement;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        tvDialogDesc = findViewById(R.id.tv_dialog_login_desc);


        findViewById(R.id.tv_dialog_login_confirm).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (listener != null) {
                    listener.onConfirm();
                }
            }
        });

        findViewById(R.id.tv_dialog_login_cancel).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        initClickText();
    }

    private void initClickText() {
        String agreementText = "我已阅读并同意《服务协议》和《隐私协议》";
        SpannableString spannableString = new SpannableString(agreementText);

        ClickableSpan serviceAgreementSpan = new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                // 处理点击服务协议事件
                CommonUtils.gotoBaseWebActivity(Constant.SERVICE_AGREEMENT);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                ds.setColor(Color.parseColor("#1890FF")); // 设置红色
                ds.setUnderlineText(false); // 取消下划线
            }
        };

        ClickableSpan privacyPolicySpan = new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                // 处理点击隐私协议事件
                CommonUtils.gotoBaseWebActivity(Constant.PRIVACY_POLICY);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                ds.setColor(Color.parseColor("#1890FF")); // 设置红色
                ds.setUnderlineText(false); // 取消下划线
            }
        };

        //运营商协议
        ClickableSpan OperatorSpan = new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                // 处理点击隐私协议事件
                CommonUtils.gotoBaseWebActivity(Constant.PRIVACY_POLICY);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                ds.setColor(Color.parseColor("#1890FF")); // 设置红色
                ds.setUnderlineText(false); // 取消下划线
            }
        };

        spannableString.setSpan(serviceAgreementSpan, 8, 12, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE); // 设置服务协议的点击事件和颜色
        spannableString.setSpan(privacyPolicySpan, 15, agreementText.length() - 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE); // 设置隐私协议的点击事件和颜色

        tvDialogDesc.setText(spannableString);
        tvDialogDesc.setMovementMethod(LinkMovementMethod.getInstance());
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
    }
}
