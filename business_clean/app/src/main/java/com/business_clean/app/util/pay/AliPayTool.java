package com.business_clean.app.util.pay;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;


import com.alipay.sdk.app.PayTask;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.data.initconfig.PayDataInfo;

import java.lang.ref.WeakReference;
import java.util.Map;


/**
 * 支付宝支付
 */
public class AliPayTool {
    private AliPayCallBack aliPayCallBack;
    private static final int SDK_PAY_FLAG = 1;
    private MyHandler myHandler;

    public interface AliPayCallBack {
        void succeedCallBack();

        void failCallBack();
    }

    private Activity mActivity;

    @SuppressLint("HandlerLeak")
    private static class MyHandler extends Handler {

        private final WeakReference<AliPayTool> aliPayToolWeakReference;

        public MyHandler(AliPayTool aliPayTool) {
            aliPayToolWeakReference = new WeakReference<>(aliPayTool);
        }

        @SuppressWarnings("unused")
        public void handleMessage(Message msg) {
            AliPayTool aliPayTool = aliPayToolWeakReference.get();
            if (aliPayTool != null) {
                aliPayTool.handleMsg(msg);
            }
        }
    }

    private void handleMsg(Message msg) {
        switch (msg.what) {
            case SDK_PAY_FLAG: {
                @SuppressWarnings("unchecked")
                PayResult payResult = new PayResult((Map<String, String>) msg.obj);
                /**
                 对于支付结果，请商户依赖服务端的异步通知结果。同步通知结果，仅作为支付结束的通知。
                 */
                String resultInfo = payResult.getResult();// 同步返回需要验证的信息
                String resultStatus = payResult.getResultStatus();
                LogUtils.e("支付宝支付---> " + payResult.toString());
                // 判断resultStatus 为9000则代表支付成功
                if (TextUtils.equals(resultStatus, "9000")) {
                    // 该笔订单是否真实支付成功，需要依赖服务端的异步通知。
                    ToastUtil.show("支付成功");
                    if (aliPayCallBack != null) {
                        aliPayCallBack.succeedCallBack();
                    }

                } else {
                    // 该笔订单真实的支付结果，需要依赖服务端的异步通知。
                    ToastUtil.show("未能完成支付 请重新尝试");
                    if (aliPayCallBack != null) {
                        aliPayCallBack.failCallBack();
                    }
                }
                break;
            }
            default:
                break;
        }
    }

    public AliPayTool(Activity activity, AliPayCallBack aliPayCallBack) {
        myHandler = new MyHandler(this);
        mActivity = activity;
        this.aliPayCallBack = aliPayCallBack;
    }

    public void aliPay(PayDataInfo payDataInfo) {
        final String orderInfo = payDataInfo.getAlipay_data();
        if (TextUtils.isEmpty(orderInfo)) {
            return;
        }
        Runnable payRunnable = new Runnable() {
            @Override
            public void run() {
                PayTask alipay = new PayTask(mActivity);
                Map<String, String> result = alipay.payV2(orderInfo, true);
                Log.i("msp", result.toString());

                Message msg = new Message();
                msg.what = SDK_PAY_FLAG;
                msg.obj = result;
                myHandler.sendMessage(msg);
            }
        };

        Thread payThread = new Thread(payRunnable);
        payThread.start();
    }
}
