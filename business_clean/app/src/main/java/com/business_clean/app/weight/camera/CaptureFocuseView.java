package com.business_clean.app.weight.camera;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.View;

import com.business_clean.R;

public class CaptureFocuseView extends View {

    private Paint paint;
    private final int stopY = 10;

    public CaptureFocuseView(Context context) {
        super(context);
        commonInit();
    }

    public CaptureFocuseView(Context context, AttributeSet attrs) {
        super(context, attrs);
        commonInit();
    }

    public CaptureFocuseView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        commonInit();
    }

    private void commonInit() {
        paint = new Paint();
        paint.setStrokeWidth(6.0f);
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int width = getWidth();
        int height = getHeight();
        int w_2 = width / 2;
        int h_2 = height / 2;
        if (isLongClick) {
            paint.setColor(Color.parseColor("#80FFD300"));
        } else {
            paint.setColor(Color.parseColor("#FFD300"));
        }
        canvas.drawLine(0.0f, 0.0f, width, 0.0f, paint);
        canvas.drawLine(width, 0.0f, width, height, paint);
        canvas.drawLine(width, height, 0.0f, height, paint);
        canvas.drawLine(0.0f, height, 0.0f, 0.0f, paint);

        canvas.drawLine(w_2, 0.0f, w_2, stopY, paint);
        canvas.drawLine(w_2, height, w_2, height - stopY, paint);
        canvas.drawLine(0.0f, h_2, stopY, h_2, paint);
        canvas.drawLine(width, h_2, width - stopY, h_2, paint);

        if (isLongClick) {
            Bitmap bitmap = BitmapFactory.decodeResource(getContext().getResources(), R.mipmap.icon_focuse_lock);
            Rect dstrect = new Rect(0, 0, width, height);
            Rect srcrect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());

            canvas.drawBitmap(bitmap, srcrect, dstrect, paint);
        } else {

        }


    }

    private boolean isLongClick;

    public void setIslongClick(boolean isLongClick) {
        if (this.isLongClick == isLongClick) {
            return;
        }
        this.isLongClick = isLongClick;
        invalidate();
    }
}
