package com.business_clean.app.ext;

import static com.blankj.utilcode.util.ActivityUtils.startActivity;

import android.app.Activity;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.RemoteException;
import android.text.SpannableString;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;

import com.amap.api.location.AMapLocationClient;
import com.amap.api.maps.MapsInitializer;
import com.amap.apis.utils.core.api.AMapUtilCoreApi;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.bumptech.glide.Glide;
import com.business_clean.BuildConfig;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.callback.OnDialogCancelListener;
import com.business_clean.app.callback.OnDialogCityIdPickerListener;
import com.business_clean.app.callback.OnDialogCityPickerListener;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.callback.OnDialogCreateFinish;
import com.business_clean.app.callback.OnDialogDoubleDateConfirmListener;
import com.business_clean.app.callback.OnDialogSelectListener;
import com.business_clean.app.callback.OnDialogTimePickerListener;
import com.business_clean.app.config.AppTaskFactory;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.config.InitMap;
import com.business_clean.app.config.InitComm;
import com.business_clean.app.config.InitDefault;
import com.business_clean.app.config.InitNetWork;
import com.business_clean.app.config.InitUtils;
import com.business_clean.app.flutter.FlutterManager;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.weight.dialog.CustomBottomListPopup;
import com.business_clean.app.weight.dialog.CustomCityPickerPopup;
import com.business_clean.app.weight.dialog.CustomConfirmPopupView;
import com.business_clean.app.weight.dialog.CustomContentHighlightPop;
import com.business_clean.app.weight.dialog.CustomDoubleTimePickerPopup;
import com.business_clean.app.weight.dialog.CustomSystemAlertPop;
import com.business_clean.app.weight.dialog.CustomTimePickerPopup;
import com.business_clean.app.weight.dialog.PagerDrawerPopup;
import com.business_clean.data.initconfig.popup.BaseBottomListEntity;
import com.business_clean.data.mode.appupdate.DialogAlertNoticeInfo;
import com.business_clean.data.mode.camera.CameraConfigData;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.data.mode.project.WorkRulesEntity;
import com.business_clean.ui.activity.BaseH5Activity;
import com.business_clean.ui.activity.StartActivity;
import com.business_clean.ui.activity.login.LoginActivity;
import com.effective.android.anchors.AnchorsManager;
import com.effective.android.anchors.Project;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.enums.PopupAnimation;
import com.lxj.xpopup.interfaces.OnSelectListener;
import com.lxj.xpopup.util.SmartGlideImageLoader;
import com.lxj.xpopup.util.XPopupUtils;
import com.noober.background.drawable.DrawableCreator;

import java.io.File;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import com.business_clean.app.weight.dialog.CustomDialogSlideSingleList;

import me.hgj.mvvmhelper.base.KtxKt;
import me.hgj.mvvmhelper.util.LogXmManager;


/**
 * 全部业务共同使用的类
 */
public class CommonUtils {

    /**
     * 切换环境
     *
     * @param position
     */
    public static void changeEnvironment(int position) {
        switch (position) {
            case 0:
                changeReleaseEnvironment();
                break;
            case 1:
                changeTestEnvironment();
                break;
            case 2:
                changeDevEnvironment();
                break;
        }
        //重启Aap
        restartLogin(KtxKt.getAppContext());
    }


    /**
     * 重启App
     *
     * @param context
     */
    public static void restartApp(Activity activity) {
//        Intent intent = activity.getPackageManager().getLaunchIntentForPackage(activity.getPackageName());
//        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
//        startActivity(intent);
//        activity.finish();
//        System.exit(0);
        ActivityForwardUtil.startActivityAndClearOther(StartActivity.class, new Bundle());
    }

    /**
     * 使用 AlarmManager 来帮助重启
     *
     * @param context
     * @param cls
     */
    public static void restartByAlarm(Context context, Class<?> cls) {
        Intent mStartActivity = new Intent(context, cls);
        int mPendingIntentId = 321456;
        PendingIntent pIntent = PendingIntent.getActivity(context, mPendingIntentId, mStartActivity, PendingIntent.FLAG_CANCEL_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        alarmManager.set(AlarmManager.RTC, System.currentTimeMillis() + 3000, pIntent);

        System.exit(0);
    }


    /**
     * 重启App
     *
     * @param context
     */
    public static void restartLogin(Context context) {
        ActivityForwardUtil.startActivity(LoginActivity.class);
    }

    /**
     * 切换到正式环境
     */
    private static void changeReleaseEnvironment() {
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_URL, NetUrl.SERVER_URL_RELEASE);
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_URL_M, NetUrl.WEB_BASE_RELEASE_URL);
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_URL_M2, NetUrl.WEB_BASE_RELEASE_URL_M2);
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_WEB_URL, NetUrl.WEB_BASE_RELEASE_URL);
        NetUrl.defaultUrl = NetUrl.SERVER_URL_RELEASE;
        NetUrl.WEB_BASE_URL = NetUrl.WEB_BASE_RELEASE_URL;
        FlutterManager.sServerType = FlutterManager.SERVER_TYPE_RELEASE;
    }

    /**
     * 测试环境
     */
    private static void changeTestEnvironment() {
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_URL, NetUrl.SERVER_URL_TEST);
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_URL_M, NetUrl.WEB_BASE_TEST_URL);
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_URL_M2, NetUrl.WEB_BASE_TEST_URL_M2);
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_WEB_URL, NetUrl.WEB_BASE_TEST_URL);
        NetUrl.defaultUrl = NetUrl.SERVER_URL_TEST;
        NetUrl.WEB_BASE_URL = NetUrl.WEB_BASE_TEST_URL;
        FlutterManager.sServerType = FlutterManager.SERVER_TYPE_TEST;
    }

    /**
     * 开发环境
     */
    private static void changeDevEnvironment() {
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_URL, NetUrl.SERVER_URL_DEV);
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_URL_M, NetUrl.WEB_BASE_DEV_URL);
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_URL_M2, NetUrl.WEB_BASE_DEV_URL_M2);
        MMKVHelper.putString(ConstantMMVK.ENVIRONMENT_BASE_WEB_URL, NetUrl.WEB_BASE_DEV_URL);
        NetUrl.defaultUrl = NetUrl.SERVER_URL_DEV;
        NetUrl.WEB_BASE_URL = NetUrl.WEB_BASE_DEV_URL;
        FlutterManager.sServerType = FlutterManager.SERVER_TYPE_DEV;
    }

    /**
     * 用户更新全局的数据处理
     *
     * @param userInfo
     */
    public static void updateLocalUserData(UserInfo userInfo) {
        if (userInfo == null) {
            return;
        }
        LogUtils.e("初始化进来拿到的数据 - " + userInfo.toString());
        MMKVHelper.putString(ConstantMMVK.USER_PHONE, userInfo.getUser().getMobile());
        //项目的 因为新增了全部 所以要处理 取数据的时候把uuid 弄null
        LogUtils.e("初始化进来拿到的数据 - " + userInfo.getProject().toString());
        if (userInfo.getProject() != null && TextUtils.isEmpty(userInfo.getProject().getUuid())) {
            userInfo.getProject().setProject_short_name("全部项目");
            userInfo.getProject().setProject_name("全部项目");
        }
        App.getAppViewModelInstance().getProjectInfo().setValue(userInfo.getProject());
        App.getAppViewModelInstance().getUserInfo().setValue(userInfo);
        Constant.ROLE_ID = userInfo.getUser().getRole_id();
        LogUtils.e("当前的账号角色是 - " + Constant.ROLE_ID);
        // -1超级管理员 1管理员 2项目负责人 3人事 4领班 5保洁 6大区经理
        switch (userInfo.getUser().getRole_id()) {
            case "-1"://超管
                Constant.ROLE_SUPER_MANGER = true;
                //如果是超管、人事、管理员、大区经理默认都是开启的
                MMKVHelper.putBoolean(ConstantMMVK.ROSTER_MENU_STATUS, true);
                break;
            case "1"://管理员
                Constant.ROLE_MANGER = true;
                //如果是超管、人事、管理员、大区经理默认都是开启的
                MMKVHelper.putBoolean(ConstantMMVK.ROSTER_MENU_STATUS, true);
                break;
            case "2"://项目负责人
                Constant.ROLE_PROJECT_OWNER = true;
                break;
            case "3"://人事
                Constant.ROLE_HR = true;
                //如果是超管、人事、管理员、大区经理默认都是开启的
                MMKVHelper.putBoolean(ConstantMMVK.ROSTER_MENU_STATUS, true);
                break;
            case "4"://领班
                Constant.ROLE_LEADER = true;
                break;
            case "5"://保洁
                Constant.ROLE_CLEANER = true;
                break;
            case "6"://大区经历
                Constant.ROLE_REGIONAL_MANAGER = true;
                //如果是超管、人事、管理员、大区经理默认都是开启的
                MMKVHelper.putBoolean(ConstantMMVK.ROSTER_MENU_STATUS, true);
                break;
        }
    }

    /**
     * 设置企业配置
     *
     * @param workRulesEntity
     */
    public static void updateCompanyConfig(WorkRulesEntity workRulesEntity) {
        if ("1".equals(workRulesEntity.getIs_entry_sign())) {
            MMKVHelper.putBoolean(ConstantMMVK.IS_ENTRY_SIGN, true);
        } else {
            MMKVHelper.putBoolean(ConstantMMVK.IS_ENTRY_SIGN, false);
        }

        if ("1".equals(workRulesEntity.getIs_upload_not_guilty())) {
            MMKVHelper.putBoolean(ConstantMMVK.IS_UPLOAD_NOT_GUILTY, true);
        } else {
            MMKVHelper.putBoolean(ConstantMMVK.IS_UPLOAD_NOT_GUILTY, false);
        }

        //是否不允许补入今日之前的入职信息 1是 2否
        if (!"1".equals(workRulesEntity.getIs_entry_before_today())) {
            MMKVHelper.putBoolean(ConstantMMVK.IS_ENTRY_BEFORE_TODAY, true);
        } else {
            MMKVHelper.putBoolean(ConstantMMVK.IS_ENTRY_BEFORE_TODAY, false);
        }

        //打卡未知范围(米)
        MMKVHelper.putString(ConstantMMVK.CLOCK_IN_RANGE, workRulesEntity.getClock_in_range());

        //是否允许员工查看考勤
        if ("1".equals(workRulesEntity.getIs_see_kq())) {
            MMKVHelper.putBoolean(ConstantMMVK.IS_SEE_KQ, true);
        } else {
            MMKVHelper.putBoolean(ConstantMMVK.IS_SEE_KQ, false);
        }

        ///是否允许员工查看个人相册
        if ("1".equals(workRulesEntity.getIs_see_photo())) {
            MMKVHelper.putBoolean(ConstantMMVK.IS_SEE_PHOTO, true);
        } else {
            MMKVHelper.putBoolean(ConstantMMVK.IS_SEE_PHOTO, false);
        }

        //is_minimalism是否开启极简模式 是否开启极简模式 1是2否
        if ("1".equals(workRulesEntity.getIs_minimalism())) {
            MMKVHelper.putBoolean(ConstantMMVK.IS_MINIMALISM, true);
        } else {
            MMKVHelper.putBoolean(ConstantMMVK.IS_MINIMALISM, false);
        }

        //是否保存相片到手机 1是 2否  只在登陆后获取一次企业的配置
        if (MMKVHelper.getInt(ConstantMMVK.SAVE_PHOTO_ALBUM, 0) == 0) {
            if (!TextUtils.isEmpty(workRulesEntity.getIs_save_photo_mobile())) {
                MMKVHelper.putInt(ConstantMMVK.SAVE_PHOTO_ALBUM, Integer.parseInt(workRulesEntity.getIs_save_photo_mobile()));
            }
            // 因为这个值用的地方太多了， 所以就不动这个值了
            if ("1".equals(workRulesEntity.getIs_save_photo_mobile())) {
                Constant.CAMERA_SAVE_PHOTO_VIDEO = true;
            } else {
                Constant.CAMERA_SAVE_PHOTO_VIDEO = false;
            }

        } else {
            int savePhoto = MMKVHelper.getInt(ConstantMMVK.SAVE_PHOTO_ALBUM, 0);
            if (1 == savePhoto) {
                Constant.CAMERA_SAVE_PHOTO_VIDEO = true;
            } else {
                Constant.CAMERA_SAVE_PHOTO_VIDEO = false;
            }
        }

        //是否开启工作拍照 1是 2否'  只在登陆后获取一次企业的配置
        if (MMKVHelper.getInt(ConstantMMVK.CLEAN_OPEN_WORK_CAMERA, 0) == 0) {
            if (!TextUtils.isEmpty(workRulesEntity.getIs_open_work_photo())) {
                MMKVHelper.putInt(ConstantMMVK.CLEAN_OPEN_WORK_CAMERA, Integer.parseInt(workRulesEntity.getIs_open_work_photo()));
            }
        }
        //是否开启打卡确认 1是 2否  只在登陆后获取一次企业的配置
        if (MMKVHelper.getInt(ConstantMMVK.CLEAN_OPEN_SELF_CLOCK, 0) == 0) {
            if (!TextUtils.isEmpty(workRulesEntity.getIs_dk_confirm())) {
                MMKVHelper.putInt(ConstantMMVK.CLEAN_OPEN_SELF_CLOCK, Integer.parseInt(workRulesEntity.getIs_dk_confirm()));
            }
        }


        //判断当前项目是不是集体打卡的项目
        if (App.getAppViewModelInstance().getProjectInfo().getValue() != null && !TextUtils.isEmpty(App.getAppViewModelInstance().getProjectInfo().getValue().getUuid())) {
            String nowProjectUUID = App.getAppViewModelInstance().getProjectInfo().getValue().getUuid();
            boolean isMatched = false; // 添加一个标志来记录是否匹配

            for (int i = 0; i < workRulesEntity.getProject_list().size(); i++) {
                if (nowProjectUUID.equals(workRulesEntity.getProject_list().get(i).getUuid())) {
                    //匹配UUID，找到uuid 说明当前项目是可以集体打卡的项目
                    isMatched = true;
                    Constant.IS_COLLECTIVE_CLOCK_IN = true;
                    break;
                }
            }
            // 如果没有匹配成功，设置为false
            if (!isMatched) {
                Constant.IS_COLLECTIVE_CLOCK_IN = false;
            }
        } else {
            Constant.IS_COLLECTIVE_CLOCK_IN = false;
        }

    }


    /**
     * 更新个人方案
     */
    public static void updateUserOneConfig(CameraConfigData CameraConfigData) {
        //是否允许员工查看考勤
        if ("1".equals(CameraConfigData.getIs_see_kq())) {
            MMKVHelper.putBoolean(ConstantMMVK.IS_SEE_KQ, true);
        } else {
            MMKVHelper.putBoolean(ConstantMMVK.IS_SEE_KQ, false);
        }

        ///是否允许员工查看个人相册
        if ("1".equals(CameraConfigData.getIs_see_photo())) {
            MMKVHelper.putBoolean(ConstantMMVK.IS_SEE_PHOTO, true);
        } else {
            MMKVHelper.putBoolean(ConstantMMVK.IS_SEE_PHOTO, false);
        }

        //is_minimalism是否开启极简模式 是否开启极简模式 1是2否
        if ("1".equals(CameraConfigData.getIs_minimalism())) {
            MMKVHelper.putBoolean(ConstantMMVK.IS_MINIMALISM, true);
        } else {
            MMKVHelper.putBoolean(ConstantMMVK.IS_MINIMALISM, false);
        }

        // 因为这个值用的地方太多了， 所以就不动这个值了 是否保存图片到本地
        if ("1".equals(CameraConfigData.getSave_photo_mobile())) {
            Constant.CAMERA_SAVE_PHOTO_VIDEO = true;
        } else {
            Constant.CAMERA_SAVE_PHOTO_VIDEO = false;
        }

        //是否开启工作拍照 1是 2否'
        if (!TextUtils.isEmpty(CameraConfigData.getOpen_work_photo())) {
            MMKVHelper.putInt(ConstantMMVK.CLEAN_OPEN_WORK_CAMERA, Integer.parseInt(CameraConfigData.getOpen_work_photo()));
        }
        //是否开启打卡确认 1是 2否
        if (!TextUtils.isEmpty(CameraConfigData.getDk_confirm())) {
            MMKVHelper.putInt(ConstantMMVK.CLEAN_OPEN_SELF_CLOCK, Integer.parseInt(CameraConfigData.getDk_confirm()));
        }

        //设置是否显示 app 信息 这个值存到本地
        if ("1".equals(CameraConfigData.getIs_show_app())) {
            MMKVHelper.putBoolean(ConstantMMVK.IS_SHOW_APP_INFO_FOR_BITMAP, true);
        } else {
            MMKVHelper.putBoolean(ConstantMMVK.IS_SHOW_APP_INFO_FOR_BITMAP, false);
        }
    }

    /**
     * 不要频频繁写域名了，直接从这里拿
     *
     * @return
     */
    public static String getWebUrlHost() {
        return MMKVHelper.getString(ConstantMMVK.ENVIRONMENT_BASE_WEB_URL, NetUrl.WEB_BASE_RELEASE_URL);
    }

    /**
     * 显示一般的普通的提示框  有取消和确定 确定按钮高亮 按返回键不关闭探矿
     */
    public static void showGeneralDialogNoDismissOnBackPressed(Context context, String title, String msg, String negationText, String positiveText, OnDialogCancelListener onCancelListener, OnDialogConfirmListener onConfirmListener) {
        showReal(context, title, msg, negationText, positiveText, true, false, onCancelListener, onConfirmListener);
    }

    /**
     * 显示一般的普通的提示框   有取消和确定  确定按钮不高亮
     */
    public static void showGeneralDialogNoHighLight(Context context, String title, String msg, String negativiText, String positiveText, OnDialogCancelListener onCancelListener, OnDialogConfirmListener onConfirmListener) {
        showReal(context, title, msg, negativiText, positiveText, false, true, onCancelListener, onConfirmListener);
    }

    /**
     * 显示一般的普通的提示框  有取消和确定 确定按钮高亮
     */
    public static void showGeneralDialog(Context context, String title, String msg, String negativiText, String positiveText, OnDialogCancelListener onCancelListener, OnDialogConfirmListener onConfirmListener) {
        showReal(context, title, msg, negativiText, positiveText, true, true, onCancelListener, onConfirmListener);
    }

    /**
     * 显示一般的普通的提示框 没有取消按钮，只有确定，确定按钮高亮
     */
    public static void showGeneralDialogNoCancel(Context context, String title, String msg, String positiveText, OnDialogConfirmListener onConfirmListener) {
        showReal(context, title, msg, "", positiveText, true, true, null, onConfirmListener);
    }

    /**
     * 显示一般普通的提示框，内容部分高亮
     */
    public static void showGeneralDialogContentHighlightCancel(Context context, String title, SpannableString spannableString, String positiveText, boolean isHignLight, boolean isHideNegativi, OnDialogConfirmListener onConfirmListener) {
        showRealContentHighlight(context, title, spannableString, "", positiveText, isHignLight, isHideNegativi, true, null, onConfirmListener);
    }

    /**
     * dialog 内容可点击 内容左对齐
     */
    private static void showRealContentHighlight(Context context, String title, SpannableString msg, String negativiText, String positiveText,
                                                 boolean isHignLight, boolean isHideNegativi, boolean dismissOnBackPressed, OnDialogCancelListener onCancelListener, OnDialogConfirmListener onConfirmListener) {

        CustomContentHighlightPop highlightPop = new CustomContentHighlightPop(context);

        highlightPop.setListener(new OnDialogCreateFinish() {
            @Override
            public void onViewInit() {
                highlightPop.getTvTitle().setText(title);
                highlightPop.getTvContent().setText(msg);
                highlightPop.getTvConfirm().setTextColor(ContextCompat.getColor(context, isHignLight ? R.color.base_primary : R.color.base_primary_text_title));
                highlightPop.getTvCancel().setText(negativiText);
                highlightPop.getTvConfirm().setText(positiveText);
                highlightPop.getTvCancel().setVisibility(isHideNegativi ? View.GONE : View.VISIBLE);
            }
        }, onConfirmListener);

        new XPopup.Builder(context)
                .asCustom(highlightPop)
                .show();
    }


    /**
     * 默认弹窗 确定 取消
     */
    private static void showReal(Context context, String title, String msg, String negationText, String positiveText, boolean isBrightLight, boolean dismissOnBackPressed, OnDialogCancelListener onCancelListener, OnDialogConfirmListener onConfirmListener) {
        new XPopup.Builder(context)
                .dismissOnBackPressed(dismissOnBackPressed)
                .dismissOnTouchOutside(true)
                .hasNavigationBar(false)
                .isDestroyOnDismiss(true)
                .popupAnimation(PopupAnimation.NoAnimation)
                .asCustom(new CustomConfirmPopupView(context, isBrightLight ? R.layout.dialog_base_meterial : R.layout.dialog_base_meterial_no_hign_light,
                        title, msg, negationText, positiveText, onCancelListener, onConfirmListener)).show();

    }


    /**
     * 显示一般的普通的提示框  有取消和确定  内容左对齐
     */
    public static void showGeneralDialogContentLeftGravityWithHighLight(Context context, String title, String msg, String negationText, String positiveText, OnDialogCancelListener onCancelListener, OnDialogConfirmListener onConfirmListener) {
        new XPopup.Builder(context)
                .dismissOnTouchOutside(true)
                .hasNavigationBar(false)
                .isDestroyOnDismiss(true)
                .popupAnimation(PopupAnimation.NoAnimation)
                .asCustom(new CustomConfirmPopupView(context, R.layout.dialog_base_meterial_content_left_gravity,
                        title, msg, negationText, positiveText, onCancelListener, onConfirmListener)).show();
    }

    /**
     * 显示从底部弹出，带手势拖拽的列表弹窗,带选中效果
     */
    public static void showBottomListNoCheckedWith(Context context, int radius, String title, String[] strings, int checkedPosition, OnSelectListener onSelectListener) {
        new XPopup.Builder(context)
                .isViewMode(true)
                .borderRadius(XPopupUtils.dp2px(context, radius))
                .asBottomList(title, strings, null, checkedPosition, onSelectListener)
                .show();
    }

    /**
     * 显示从底部弹出，带手势拖拽的列表弹窗, 没用选中效果
     */
    public static void showBottomListWith(Context context, int radius, String title, String[] strings, OnSelectListener onSelectListener) {
        new XPopup.Builder(context)
                .isViewMode(true)
                .maxHeight(ScreenUtils.getScreenHeight() / 2)
                .borderRadius(XPopupUtils.dp2px(context, radius))
                .asBottomList(title, strings, onSelectListener)
                .show();
    }

    public static void showCenterListWith(Context context, int radius, String title, String[] strings, OnSelectListener onSelectListener) {
        new XPopup.Builder(context)
                .isViewMode(true)
                .dismissOnBackPressed(false)
                .dismissOnTouchOutside(false)
                .borderRadius(XPopupUtils.dp2px(context, radius))
                .asCenterList(title, strings, onSelectListener)
                .show();
    }


    /**
     * 显示从底部弹出，自定义的 Bottom 分主标题跟次标题
     */
    public static void showBottomListCustomWith(Context context, int radius, String title, List<BaseBottomListEntity> datas, OnDialogSelectListener onSelectListener) {
        CustomBottomListPopup popup = new CustomBottomListPopup(context, datas);
        if (!TextUtils.isEmpty(title)) {
            popup.setTitle(title);
        }
        popup.setOnDialogListener(new OnDialogCancelListener() {
            @Override
            public void onCancel() {

            }
        }, onSelectListener);
        new XPopup.Builder(context)
                .isViewMode(true)
                .maxHeight(ScreenUtils.getScreenHeight() / 2)
                .borderRadius(XPopupUtils.dp2px(context, radius))
                .asCustom(popup)
                .show();
    }

    /**
     * 显示从底部弹出，自定义的 Bottom 分主标题跟次标题
     */
    public static void showBottomListCustomWith(Context context, int radius, String title, int inversePosition, List<BaseBottomListEntity> datas, OnDialogSelectListener onSelectListener) {
        CustomBottomListPopup popup = new CustomBottomListPopup(context, inversePosition, datas);
        if (!TextUtils.isEmpty(title)) {
            popup.setTitle(title);
        }
        popup.setOnDialogListener(new OnDialogCancelListener() {
            @Override
            public void onCancel() {

            }
        }, onSelectListener);
        new XPopup.Builder(context)
                .isViewMode(true)
                .maxHeight(ScreenUtils.getScreenHeight() / 2)
                .borderRadius(XPopupUtils.dp2px(context, radius))
                .asCustom(popup)
                .show();
    }


    /**
     * 双日期选择-选择日期
     */
    public static void showDialogDoubleTimePickerPopup(Context context, String title, String dialogConfirm, String dialogCancel, int startYear, int endYear, int firstYear, int firstMonth, int firstDay, int secondYear, int secondMonth, int secondDay, OnDialogDoubleDateConfirmListener listener) {
        CustomDoubleTimePickerPopup popup = new CustomDoubleTimePickerPopup(context);
        popup.setListener(new OnDialogCreateFinish() {
            @Override
            public void onViewInit() {
                popup.setCenterText(title);
                popup.setCancelText(dialogCancel);
                popup.setConfirmText(dialogConfirm);
                popup.setDateRange(startYear, endYear, 0, 0, 0);
                popup.setFirstRange(firstYear, firstMonth, firstDay);
                popup.setSecondRange(secondYear, secondMonth, secondDay);
                popup.initWheelDate(); // 必须调用这行代码，不然界面什么都不显示
            }
        }, listener);
        new XPopup.Builder(context)
                .asCustom(popup)
                .show();
    }


    /**
     * 单排列表，传值
     */
    public static void showDialogWheelSingleList(Context context, String content, ArrayList<String> arrList, String inverseText, OnDialogSelectListener onSelectListener) {
        CustomDialogSlideSingleList popup = new CustomDialogSlideSingleList(context);
        popup.setListener(new OnDialogCreateFinish() {
            @Override
            public void onViewInit() {
                popup.setCurrentItem(0);
                popup.setDataList(arrList);
                popup.setCenterText(content);
                popup.setWheelCyclic(false);
                popup.setWheelInverseText(inverseText);
                if (popup.getWheelView() == null) {
                    popup.getWheelView().setLineSpacingMultiplier(2.0F);//设置间距倍数,但是只能在1.0-4.0f之间
                }
            }
        }, () -> {

        }, (position, text) -> {
            if (onSelectListener != null) {
                onSelectListener.onSelect(position, text);
            }
        });
        new XPopup.Builder(context)
                .asCustom(popup)
                .show();
    }


    /**
     * 显示 日历 自定义年月日
     */
    public static void showDialogTimePickerView(Context context, String title, Calendar defDate, int startYear, int endYear, Calendar startDateRang, Calendar endDateRang, CustomTimePickerPopup.Mode mode, OnDialogTimePickerListener listener) {
        Calendar defaultDate = defDate != null ? defDate : Calendar.getInstance();

        CustomTimePickerPopup popup = new CustomTimePickerPopup(context)
                .setTitle(title)
                .setMinutesStep(1)
                .setDefaultDate(defaultDate)
                .setYearRange(startYear, endYear)
                .setDateRang(startDateRang, endDateRang)
                .setMode(mode)
                .setTimePickerListener(listener);

        new XPopup.Builder(context)
                .asCustom(popup)
                .show();
    }

    /**
     * 选择城市
     *
     * @param context
     * @param title
     * @param mode
     * @param curProvice
     * @param curCity
     * @param curArea
     * @param listener
     */
    public static void showDialogCityPickerView(Context context, String title, CustomCityPickerPopup.Mode mode, String curProvice, String curCity, String curArea, OnDialogCityPickerListener listener) {
        CustomCityPickerPopup popup = new CustomCityPickerPopup(context)
                .setMode(mode)
                .setTitle(title)
                .setDefData(curProvice, curCity, curArea)
                .setCityPickerListener(listener);
        new XPopup.Builder(context)
                .asCustom(popup)
                .show();
    }


    /**
     * 选择城市
     *
     * @param context
     * @param title
     * @param mode
     * @param curProvice
     * @param curCity
     * @param curArea
     * @param listener
     */
    public static void showDialogCityPickerView(Context context, String title, CustomCityPickerPopup.Mode mode, String curProvice, String curCity, String curArea, OnDialogCityPickerListener listener, OnDialogCityIdPickerListener cityIdPickerListener) {
        CustomCityPickerPopup popup = new CustomCityPickerPopup(context)
                .setMode(mode)
                .setTitle(title)
                .setDefData(curProvice, curCity, curArea)
                .setCityPickerListener(listener)
                .setCityIdPickerListener(cityIdPickerListener);
        new XPopup.Builder(context)
                .asCustom(popup)
                .show();
    }


    public static void showSystemAlertPop(Context context, DialogAlertNoticeInfo noticeInfo, OnDialogCancelListener onCancelListener, OnDialogConfirmListener onConfirmListener) {
        CustomSystemAlertPop popup = new CustomSystemAlertPop(context);
        popup.setListener(new OnDialogCreateFinish() {
            @Override
            public void onViewInit() {
                popup.setTitle(noticeInfo.getTitle() != null ? noticeInfo.getTitle() : "");
                popup.setSubTitle(noticeInfo.getDescribe() != null ? noticeInfo.getDescribe() : "");
                popup.setContent(noticeInfo.getContent() != null ? noticeInfo.getContent() : "");
                popup.setRightWeight(noticeInfo.getRight_weight());
                if (noticeInfo.getImg_url() != null) {
                    popup.setImgUrl(noticeInfo.getImg_url());
                }
                popup.setLeftButton(noticeInfo.getClose_button() != null ? noticeInfo.getClose_button() : "");
                popup.setRightButton(noticeInfo.getConfirm_button() != null ? noticeInfo.getConfirm_button() : "");
                if (noticeInfo.getSub_title_color() != 0) {
                    popup.setSubTitleColor(noticeInfo.getSub_title_color());
                }
                if (noticeInfo.getContent_bg_color() != 0) {
                    popup.setContentBgColor(noticeInfo.getContent_bg_color());
                }
            }

        }, onCancelListener, onConfirmListener);

        new XPopup.Builder(context)
                .dismissOnBackPressed(noticeInfo.isDismissOnBackPressed())
                .dismissOnTouchOutside(noticeInfo.isDismissOnTouchOutside())
                .asCustom(popup)
                .show();
    }


    public static void showSystemUpdateAlertPop(Context context, DialogAlertNoticeInfo noticeInfo, OnDialogCancelListener onCancelListener, OnDialogConfirmListener onConfirmListener) {
        CustomSystemAlertPop popup = new CustomSystemAlertPop(context);
        popup.setListener(new OnDialogCreateFinish() {
            @Override
            public void onViewInit() {
                popup.setConfirmClickDismiss(true);
                popup.setTitle(noticeInfo.getTitle() != null ? noticeInfo.getTitle() : "");
                popup.setSubTitle(noticeInfo.getDescribe() != null ? noticeInfo.getDescribe() : "");
                popup.setContent(noticeInfo.getContent() != null ? noticeInfo.getContent() : "");
                popup.setRightWeight(noticeInfo.getRight_weight());
                if (noticeInfo.getImg_url() != null) {
                    popup.setImgUrl(noticeInfo.getImg_url());
                }
                popup.setLeftButton(noticeInfo.getClose_button() != null ? noticeInfo.getClose_button() : "");
                popup.setRightButton(noticeInfo.getConfirm_button() != null ? noticeInfo.getConfirm_button() : "");
                if (noticeInfo.getSub_title_color() != 0) {
                    popup.setSubTitleColor(noticeInfo.getSub_title_color());
                }
                if (noticeInfo.getContent_bg_color() != 0) {
                    popup.setContentBgColor(noticeInfo.getContent_bg_color());
                }
            }

        }, onCancelListener, onConfirmListener);

        new XPopup.Builder(context)
                .dismissOnBackPressed(noticeInfo.isDismissOnBackPressed())
                .dismissOnTouchOutside(noticeInfo.isDismissOnTouchOutside())
                .asCustom(popup)
                .show();
    }


    /**
     * Xpopup种的图片预览
     */
    public static void showPreviewPhoto(Context context, ImageView imageView, int position, List<String> list) {

    }

    /**
     * 默认选中的 效果 单个View adapter 使用
     */
    public static Drawable getBaseSelectedDrawable(Context context) {
        Drawable drawableSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(4))
                .setSolidColor(ContextCompat.getColor(context, R.color.base_primary_select))
                .setStrokeColor(ContextCompat.getColor(context, R.color.base_primary))
                .setStrokeWidth(SizeUtils.dp2px(1))
                .build();
        return drawableSelected;
    }

    /**
     * 默认选中的 效果 单个View adapter 使用
     */
    public static Drawable getBaseSelectedDrawable(Context context, int radius) {
        Drawable drawableSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(radius))
                .setSolidColor(ContextCompat.getColor(context, R.color.base_primary_select))
                .setStrokeColor(ContextCompat.getColor(context, R.color.base_primary))
                .setStrokeWidth(SizeUtils.dp2px(1))
                .build();
        return drawableSelected;
    }


    /**
     * 默认选中的 效果 单个View adapter 使用
     */
    public static Drawable getBaseSelectedDrawable(Context context, int radius, int solid, int stokeColor, int strokeWidth) {
        Drawable drawableSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(radius))
                .setSolidColor(ContextCompat.getColor(context, solid))
                .setStrokeColor(ContextCompat.getColor(context, stokeColor))
                .setStrokeWidth(SizeUtils.dp2px(strokeWidth))
                .build();
        return drawableSelected;
    }


    /**
     * 默认选中的 效果 单个View adapter 使用 不要边框的
     */
    public static Drawable getBaseSelectedNoStrokeDrawable(Context context) {
        Drawable drawableSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(4))
                .setSolidColor(ContextCompat.getColor(context, R.color.base_primary_select))
                .build();
        return drawableSelected;
    }


    /**
     * 默认选中的 效果 单个View adapter 使用 不要边框的
     */
    public static Drawable getBaseSelectedNoStrokeDrawable(Context context, int radius) {
        Drawable drawableSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(radius))
                .setSolidColor(ContextCompat.getColor(context, R.color.base_primary_select))
                .build();
        return drawableSelected;
    }


    /**
     * 默认选中的 效果 单个View adapter 使用 不要边框的
     */
    public static Drawable getBaseSelectedNoStrokeDrawable(Context context, int radius, int color) {
        Drawable drawableSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(radius))
                .setSolidColor(ContextCompat.getColor(context, color))
                .build();
        return drawableSelected;
    }

    /**
     * 默认选中的 效果 单个View adapter 使用
     */
    public static Drawable getBaseUnSelectedDrawable(Context context) {
        Drawable drawableUnSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(4))
                .setSolidColor(ContextCompat.getColor(context, R.color.base_primary_un_select))
                .build();
        return drawableUnSelected;
    }

    /**
     * 默认选中的 效果 单个View adapter 使用
     */
    public static Drawable getBaseUnSelectedDrawable(Context context, int radius) {
        Drawable drawableUnSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(radius))
                .setSolidColor(ContextCompat.getColor(context, R.color.base_primary_un_select))
                .build();
        return drawableUnSelected;
    }


    /**
     * View 的点击切换
     */
    public static Drawable getBaseViewSelectedDrawable(Context context, int dpValue) {
        Drawable drawableSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(dpValue))
                .setSolidColor(ContextCompat.getColor(context, R.color.base_primary))
                .build();
        return drawableSelected;
    }


    public static Drawable getBaseViewDisableDrawable(Context context, int color, int dpValue) {
        Drawable drawableSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(dpValue))
                .setSolidColor(ContextCompat.getColor(context, color))
                .build();
        return drawableSelected;
    }


    /**
     * 默认选中的 效果 单个View adapter 使用
     */
    public static Drawable getBaseUnSelectedDrawable(Context context, int dpValue, int solid) {
        Drawable drawableUnSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(dpValue))
                .setSolidColor(ContextCompat.getColor(context, solid))
                .build();
        return drawableUnSelected;
    }


    /**
     * 默认选中的 效果 单个View adapter 使用
     */
    public static Drawable getBaseUnSelectedDrawable(Context context, int dpValue, int solid, int stokeColor, int strokeWidth) {
        Drawable drawableUnSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(dpValue))
                .setSolidColor(ContextCompat.getColor(context, solid))
                .setStrokeColor(ContextCompat.getColor(context, stokeColor))
                .setStrokeWidth(SizeUtils.dp2px(strokeWidth))
                .build();
        return drawableUnSelected;
    }


    /**
     * View 的点击切换
     */
    public static Drawable getBaseViewSelectedDrawable(Context context, int dpValue, int solid) {
        Drawable drawableSelected = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(dpValue))
                .setSolidColor(ContextCompat.getColor(context, solid))
                .build();
        return drawableSelected;
    }

    /**
     * 展示大图
     *
     * @param context
     * @param imageView
     * @param url
     */
    public static void showPreviewBig(Context context, ImageView imageView, String url) {
        new XPopup.Builder(context)
                .asImageViewer(imageView, url, new SmartGlideImageLoader())
                .isShowSaveButton(false)//隐藏保存按钮
                .show();
    }

    /**
     * 退出 app
     */
    public static void logoutApp() {
        //清除所有的内存缓存
        Glide.get(KtxKt.getAppContext()).clearMemory();
        //清楚 token、用户信息、首页数据等，其他的元数据可以保留
        Constant.ROLE_ID = "";
        Constant.ROLE_SUPER_MANGER = false;
        Constant.ROLE_MANGER = false;
        Constant.ROLE_HR = false;
        Constant.ROLE_PROJECT_OWNER = false;
        Constant.ROLE_LEADER = false;
        Constant.ROLE_CLEANER = false;
        Constant.ROLE_REGIONAL_MANAGER = false;
        Constant.IS_COLLECTIVE_CLOCK_IN = false;


//        MMKVHelper.clearAll();
        MMKVHelper.remove(ConstantMMVK.TOKEN);
        MMKVHelper.remove(ConstantMMVK.COMPANY_UUID);
        MMKVHelper.remove(ConstantMMVK.USER_INFO);
        MMKVHelper.remove(ConstantMMVK.INIT_DATA);
        MMKVHelper.remove(ConstantMMVK.ALL_PROJECT_LIST);
        MMKVHelper.remove(ConstantMMVK.ROSTER_MENU_STATUS);
        MMKVHelper.remove(ConstantMMVK.IS_UPLOAD_NOT_GUILTY);
        MMKVHelper.remove(ConstantMMVK.IS_ENTRY_SIGN);
        MMKVHelper.remove(ConstantMMVK.IS_ENTRY_BEFORE_TODAY);
        //删除保洁员的操作设置
        MMKVHelper.remove(ConstantMMVK.CLEAN_OPEN_SELF_CLOCK);
        MMKVHelper.remove(ConstantMMVK.CLEAN_OPEN_WORK_CAMERA);
        MMKVHelper.remove(ConstantMMVK.CLOCK_IN_RANGE);
        MMKVHelper.remove(ConstantMMVK.IS_SEE_KQ);
        MMKVHelper.remove(ConstantMMVK.IS_SEE_PHOTO);
        MMKVHelper.remove(ConstantMMVK.IS_MINIMALISM);
        MMKVHelper.remove(ConstantMMVK.SAVE_PHOTO_ALBUM);

        App.getAppViewModelInstance().getUserInfo().setValue(null);
        App.getAppViewModelInstance().getProjectInfo().setValue(null);

        restartLogin(KtxKt.getAppContext());
    }


    public static String getBrandIdByManufacturer(String manufacturer) {
        switch (manufacturer) {
            case "xiaomi":
                return "1"; // 小米
            case "oppo":
                return "2"; // OPPO
            case "oneplus":
                return "3"; // 一加
            case "realme":
                return "4"; // 真我
            case "vivo":
                return "5"; // vivo
            case "huawei":
                return "6"; // 华为
            case "honor":
                return "7"; // 荣耀
            default:
                return "-1"; // 未知厂商
        }
    }


    // 判断是否已忽略当前版本
    public static boolean isVersionIgnored(int versionCode) {
        // 从 MMKV 中读取忽略的版本号
        int ignoredVersionCode = MMKVHelper.getInt(ConstantMMVK.APP_UPDATE_VERSION_CODE, 0);
        return ignoredVersionCode == versionCode;
    }

    public static void gotoBaseWebActivity(String url) {
        Bundle bundle = new Bundle();
        bundle.putString("url", url);
        ActivityForwardUtil.startActivity(BaseH5Activity.class, bundle);
    }


    /**
     * 打开外部链接
     *
     * @param url
     */
    public static void gotoLinkInBrowser(String url) {
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
        startActivity(intent);
    }

    /**
     * 格式化小数点后两位
     *
     * @param value
     * @return
     */
    public static String formatDecimal(String value) {
        try {
            // 将字符串转换为 Double
            Double number = Double.parseDouble(value);

            // 使用 DecimalFormat 格式化
            DecimalFormat df = new DecimalFormat("#.##");
            String formatted = df.format(number);

            // 如果结果为空，返回 "0"
            return formatted.isEmpty() ? "0" : formatted;
        } catch (NumberFormatException e) {
            // 如果无法解析，返回原始值
            return value;
        }
    }

    public static void gotoBaseWebActivity(Bundle bundle) {
        ActivityForwardUtil.startActivity(BaseH5Activity.class, bundle);
    }

    public static void callPhone(String phoneNumber) {
        Intent Intent = new Intent(android.content.Intent.ACTION_DIAL, Uri.parse("tel:" + phoneNumber));
        startActivity(Intent);
    }

    /**
     * 对手机号码 中间4位 进行隐藏
     *
     * @param phoneNumber
     * @return
     */
    public static String maskPhoneNumber(String phoneNumber) {
        if (TextUtils.isEmpty(phoneNumber)) {
            return "";
        }

        if (phoneNumber.length() != 11) {
            return phoneNumber;
        }

        String maskedNumber = phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7);
        return maskedNumber;
    }

    //复制内容
    public static void copyToClipboard(Context context, String text) {
        ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("Copied Text", text);
        clipboard.setPrimaryClip(clip);
        ToastUtil.show("复制成功");
    }


    //获取当前web的域名
    public static String getWebHost() {
        if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_RELEASE)) {
            return NetUrl.WEB_BASE_RELEASE_URL;
        } else if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_TEST)) {
            return NetUrl.WEB_BASE_TEST_URL;
        } else {
            return NetUrl.WEB_BASE_DEV_URL;
        }
    }


    public static String getWebHostM2() {
        if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_RELEASE)) {
            return NetUrl.WEB_BASE_RELEASE_URL_M2;
        } else if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_TEST)) {
            return NetUrl.WEB_BASE_TEST_URL_M2;
        } else {
            return NetUrl.WEB_BASE_DEV_URL_M2;
        }
    }


    /**
     * 延迟初始化Application的东西
     */
    public static void onDelayMainProcessInit() {
        AnchorsManager.getInstance().debuggable(BuildConfig.DEBUG) //设置锚点
                .addAnchor(InitDefault.TASK_ID,
                        InitNetWork.TASK_ID,
                        InitUtils.TASK_ID,
                        InitComm.TASK_ID,
                        InitMap.TASK_ID
                ).start(
                        new Project.Builder("business_clean", new AppTaskFactory())
                                .add(InitDefault.TASK_ID)
                                .add(InitNetWork.TASK_ID)
                                .add(InitComm.TASK_ID)
                                .add(InitUtils.TASK_ID)
                                .add(InitMap.TASK_ID)
                                .build()
                );
    }

    public static void onDelayMapInit(Context appContext) {
        // 获取本地的key
        try {
            String value = MMKVHelper.getString(ConstantMMVK.A_MAP_KEY, "");
            if (!TextUtils.isEmpty(value)) {
                // 动态设置 key
                MapsInitializer.setApiKey(value);
                AMapLocationClient.setApiKey(value);

                /**
                 * 基础库设置是否允许采集个人及设备信息
                 * @param collectEnable: true 允许采集 false 不允许采集
                 */
                AMapUtilCoreApi.setCollectInfoEnable(true);
                LogUtils.e("Application 高德初始化了，隐私政策也是ok");
                LogXmManager.log("Application 高德初始化了，隐私政策也是ok");

                // 高德地图的隐私政策
                MapsInitializer.initialize(appContext);
                MapsInitializer.updatePrivacyShow(appContext, true, true);
                MapsInitializer.updatePrivacyAgree(appContext, true);
            } else {
                LogXmManager.log("Application 没 key 没初始化，隐私政策也是ok");

                LogUtils.e("Application 没 key 没初始化，隐私政策也是ok");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 选择项目的弹窗
     */
    public static void showSelectProjectDialog(Context context, String projectUuid, boolean isChangeAppProject, boolean isNeedAll, boolean isHeadOffice, PagerDrawerPopup.OnSelectProjectListener listener) {
        PagerDrawerPopup pagerDrawerPopup = new PagerDrawerPopup(context, projectUuid, isChangeAppProject, isNeedAll, listener);
        pagerDrawerPopup.setHeadOffice(isHeadOffice);
        new XPopup.Builder(context)
                .autoFocusEditText(false)
                .asCustom(pagerDrawerPopup).show();
    }

    public static boolean checkRoleHeadOffice() {
        return Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER || Constant.ROLE_HR || Constant.ROLE_REGIONAL_MANAGER;
    }


    /**
     * 根据不同的版本来切换保存路径
     */
    public static String getSavePath() {
        String path;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) { // Android 10 及以上
            path = App.getInstance().getExternalFilesDir(null).getAbsolutePath() + "/file/download/";
        } else {
            path = Environment.getExternalStorageDirectory().getPath() + "/file/download/";
        }

        // 创建目录，如果不存在的话
        File directory = new File(path);
        if (!directory.exists()) {
            directory.mkdirs(); // 创建目录
        }

        return path;
    }
}


