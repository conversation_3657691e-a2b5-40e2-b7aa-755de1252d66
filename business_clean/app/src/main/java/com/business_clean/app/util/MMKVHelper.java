package com.business_clean.app.util;

import android.content.Context;

import com.tencent.mmkv.MMKV;

import org.jetbrains.annotations.Nullable;

public class MMKVHelper {
    private static MMKV mmkv;

    public static void initialize(Context context, String dir) {
        MMKV.initialize(dir);
        mmkv = MMKV.defaultMMKV();
    }

    public static void putString(String key, String value) {
        mmkv.encode(key, value);
    }

    public static @Nullable String getString(@Nullable String key) {
        return mmkv.decodeString(key);
    }

    public static @Nullable String getString(@Nullable String key, @Nullable String defValue) {
        return mmkv.decodeString(key, defValue);
    }

    public static void putInt(String key, int value) {
        mmkv.encode(key, value);
    }

    public static int getInt(String key, int defaultValue) {
        return mmkv.decodeInt(key, defaultValue);
    }

    public static void putBoolean(String key, boolean value) {
        mmkv.encode(key, value);
    }

    public static boolean getBoolean(String key, boolean defaultValue) {
        return mmkv.decodeBool(key, defaultValue);
    }

    // 添加其他类型的put和get方法，根据需要自行扩展

    public static void remove(String key) {
        mmkv.removeValueForKey(key);
    }

    public static void clearAll() {
        mmkv.clearAll();
    }
}