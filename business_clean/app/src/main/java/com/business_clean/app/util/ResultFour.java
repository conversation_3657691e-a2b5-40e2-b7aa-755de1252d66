package com.business_clean.app.util;

public class ResultFour<R1, R2, R3, R4> extends ResultThree<R1, R2, R3> {
    public final R4 returnValue4;

    public ResultFour(R1 returnValue1, R2 returnValue2, R3 returnValue3, R4 returnValue4) {
        super(returnValue1, returnValue2, returnValue3);
        this.returnValue4 = returnValue4;
    }

    @Override
    public String toString() {
        return super.toString() + " , " + returnValue4.toString();
    }
}
