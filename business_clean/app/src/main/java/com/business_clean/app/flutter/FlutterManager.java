package com.business_clean.app.flutter;

import android.app.Application;

import com.idlefish.flutterboost.FlutterBoost;

/**
 * Flutter boost 管理
 */
public class FlutterManager {

    public static final String SERVER_TYPE_DEV = "dev";
    public static final String SERVER_TYPE_TEST = "test";
    public static final String SERVER_TYPE_RELEASE = "release";

    public static String sServerType = SERVER_TYPE_RELEASE;

    public static void initEngine(Application app) {
        FlutterBoost.instance().setup(app, new BoostDelegate(), engine -> {});
        FlutterBoost.instance().addEventListener("native_CommonEvent", new BoostEventListener());
    }
}
