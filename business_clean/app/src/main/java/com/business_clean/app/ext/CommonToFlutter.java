package com.business_clean.app.ext;

import static com.blankj.utilcode.util.ActivityUtils.startActivity;

import android.app.Activity;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.bumptech.glide.Glide;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.callback.OnDialogCancelListener;
import com.business_clean.app.callback.OnDialogCityIdPickerListener;
import com.business_clean.app.callback.OnDialogCityPickerListener;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.callback.OnDialogCreateFinish;
import com.business_clean.app.callback.OnDialogDoubleDateConfirmListener;
import com.business_clean.app.callback.OnDialogSelectListener;
import com.business_clean.app.callback.OnDialogTimePickerListener;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.flutter.FlutterManager;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.weight.dialog.CustomBottomListPopup;
import com.business_clean.app.weight.dialog.CustomCityPickerPopup;
import com.business_clean.app.weight.dialog.CustomConfirmPopupView;
import com.business_clean.app.weight.dialog.CustomContentHighlightPop;
import com.business_clean.app.weight.dialog.CustomDialogSlideSingleList;
import com.business_clean.app.weight.dialog.CustomDoubleTimePickerPopup;
import com.business_clean.app.weight.dialog.CustomSystemAlertPop;
import com.business_clean.app.weight.dialog.CustomTimePickerPopup;
import com.business_clean.data.initconfig.popup.BaseBottomListEntity;
import com.business_clean.data.mode.appupdate.DialogAlertNoticeInfo;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.data.mode.project.WorkRulesEntity;
import com.business_clean.ui.activity.BaseH5Activity;
import com.business_clean.ui.activity.StartActivity;
import com.business_clean.ui.activity.login.LoginActivity;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.enums.PopupAnimation;
import com.lxj.xpopup.interfaces.OnSelectListener;
import com.lxj.xpopup.util.SmartGlideImageLoader;
import com.lxj.xpopup.util.XPopupUtils;
import com.noober.background.drawable.DrawableCreator;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import me.hgj.mvvmhelper.base.KtxKt;


/**
 * 原生跳转Flutter的公共
 */
public class CommonToFlutter {


    /**
     * 跳转员工新建
     */
    public static void gotoFlutterAddStaffPage(String uuid, String applicationNo, int channel) {
        HashMap<String, Object> params = new HashMap<>();
        if (!TextUtils.isEmpty(uuid)) {
            params.put("uuid", uuid);
        }
        if (!TextUtils.isEmpty(applicationNo)) {
            params.put("application_no", applicationNo);
        }
        if (!TextUtils.isEmpty(uuid) || !TextUtils.isEmpty(applicationNo)) {
            params.put("channel", channel);
        }
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("AddStaffPage")
                .arguments(params)
                .build());
    }


    /**
     * 再次入职
     */
    public static void gotoFlutterAgainAddStaffPage(String uuid) {
        HashMap<String, Object> params = new HashMap<>();
        if (!TextUtils.isEmpty(uuid)) {
            params.put("uuid", uuid);
        }
        params.put("again_entry", true);
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("AddStaffPage")
                .arguments(params)
                .build());
    }


    /**
     * 跳转到Flutter的员工的详情
     *
     * @param uuid
     */
    public static void gotoFlutterStaffWebOnePage(String uuid) {
        HashMap<String, Object> params = new HashMap<>();
        if (!TextUtils.isEmpty(uuid)) {
            params.put("uuid", uuid);
        }
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("StaffOneWebPage")
                .arguments(params)
                .build());
    }

    /**
     * 跳转员工新建
     */
    public static void gotoFlutterAddStaffPage(String uuid, String applicationNo, int channel, int jump_index) {
        HashMap<String, Object> params = new HashMap<>();
        if (!TextUtils.isEmpty(uuid)) {
            params.put("uuid", uuid);
        }
        if (!TextUtils.isEmpty(applicationNo)) {
            params.put("application_no", applicationNo);
        }
        if (!TextUtils.isEmpty(uuid) || !TextUtils.isEmpty(applicationNo)) {
            params.put("channel", channel);
        }
        params.put("jump_index", jump_index);
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("AddStaffPage")
                .arguments(params)
                .build());
    }


    /**
     * 跳转员工新建
     */
    public static void gotoFlutterAddStaffPage(String uuid, String applicationNo, int channel, int jump_index, String userName) {
        HashMap<String, Object> params = new HashMap<>();
        if (!TextUtils.isEmpty(uuid)) {
            params.put("uuid", uuid);
        }
        if (!TextUtils.isEmpty(applicationNo)) {
            params.put("application_no", applicationNo);
        }
        if (!TextUtils.isEmpty(uuid) || !TextUtils.isEmpty(applicationNo)) {
            params.put("channel", channel);
        }
        params.put("jump_index", jump_index);
        params.put("user_name", userName);
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("AddStaffPage")
                .arguments(params)
                .build());
    }

    /**
     * 跳转计划工单详情
     *
     * @param uuid
     * @param taskType
     * @param openCamera
     */
    public static void gotoFlutterTaskOnePage(String uuid, String taskType, boolean openCamera) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("uuid", uuid);
        if (!TextUtils.isEmpty(taskType)) {
            hashMap.put("task_type", taskType);
        }
        hashMap.put("open_camera", openCamera);
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("WorkPlanTaskOnePage")
                .arguments(hashMap)
                .build());
    }

    /**
     * 跳转快速离职
     *
     * @param uuid
     * @param application_no
     */
    public static void gotoFlutterQuickDepartPage(String uuid, String application_no) {
        Map<String, Object> hashMap = new HashMap<>();
        if (!TextUtils.isEmpty(uuid)) {
            hashMap.put("uuid", uuid);
        }
        if (!TextUtils.isEmpty(application_no)) {
            hashMap.put("application_no", application_no);
        }
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("quickDeparkPage")
                .arguments(hashMap)
                .build());
    }


    /**
     * 物料申请
     *
     * @param uuid
     * @param applicationNo
     */
    public static void gotoFlutterMaterialTemplatePage(String uuid, String applicationNo) {
        Map<String, Object> hashMap = new HashMap<>();
        if (!TextUtils.isEmpty(uuid)) {
            hashMap.put("uuid", uuid);
        }
        if (!TextUtils.isEmpty(applicationNo)) {
            hashMap.put("application_no", applicationNo);
        }
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("materialTemplatePage")
                .arguments(hashMap)
                .build());
    }

    /**
     * 自定义审批
     *
     * @param uuid
     * @param applicationNo
     */
    public static void gotoFlutterCustomTemplatePage(String uuid, String applicationNo) {
        Map<String, Object> hashMap = new HashMap<>();
        if (!TextUtils.isEmpty(uuid)) {
            hashMap.put("uuid", uuid);
        }
        if (!TextUtils.isEmpty(applicationNo)) {
            hashMap.put("application_no", applicationNo);
        }
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("approveTemplatePage")
                .arguments(hashMap)
                .build());
    }

    /**
     * 考勤统计
     */
    public static void gotoFlutterAttendanceStatPage(String user_uuid, String user_name) {
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("user_uuid", user_uuid);
        hashMap.put("user_name", user_name);
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("AttendanceOnePage")
                .arguments(hashMap)
                .build());
    }

    /**
     * 跳转信用
     */
    public static void gotoCredit(String uuid, String id_number, String user_name) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("uuid", uuid);
        hashMap.put("id_number", id_number);
        hashMap.put("user_name", user_name);
        hashMap.put("type", "2");//类型 1入职查询 2员工查询
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("creditInquiryPage")
                .arguments(hashMap)
                .build());
    }

    /**
     * 跳转消息通知
     */
    public static void gotoFlutter(HashMap<String, Object> hashMap, String pageName) {
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName(pageName)
                .arguments(hashMap)
                .build());
    }
}


