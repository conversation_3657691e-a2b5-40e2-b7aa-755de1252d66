package com.business_clean.app.weight;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.noober.background.drawable.DrawableCreator;

public class WithBaseItemView extends LinearLayout {

    private ImageView iconImageView;
    private TextView leftTextView;
    private EditText inputEditText;
    private TextView rightTextView;
    private ImageView arrowImageView;
    private TextView starTextView;
    private LinearLayout relativeLayout;//整体的
    private LinearLayout llLayout;//看 xml
    private View viewline;

    public WithBaseItemView(Context context) {
        super(context);
        init();
    }

    public WithBaseItemView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
        setupAttributes(attrs);
    }

    public WithBaseItemView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
        setupAttributes(attrs);
    }

    private void init() {
        LayoutInflater.from(getContext()).inflate(R.layout.form_input_view, this, true);
        relativeLayout = findViewById(R.id.rl_layout);
        llLayout = findViewById(R.id.ll_item_layout);
        iconImageView = findViewById(R.id.iconImageView);
        leftTextView = findViewById(R.id.labelTextView);
        inputEditText = findViewById(R.id.inputEditText);
        inputEditText.setHint("请输入");
        rightTextView = findViewById(R.id.infoTextView);
        arrowImageView = findViewById(R.id.arrowImageView);
        starTextView = findViewById(R.id.starTextView);
        viewline = findViewById(R.id.view_line);
    }

    private void setupAttributes(AttributeSet attrs) {
        TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.FormInputView);

        Drawable icon = a.getDrawable(R.styleable.FormInputView_left_icon);
        if (icon != null) {
            setIcon(icon);
        }

        String label = a.getString(R.styleable.FormInputView_left_title);
        if (label != null) {
            setLabel(label);
        }

        String info = a.getString(R.styleable.FormInputView_right_text);
        if (info != null && !TextUtils.isEmpty(info)) {
            setTvRightText(info);
        }

        boolean rightVisible = a.getBoolean(R.styleable.FormInputView_rightVisible, false);
        setInfoVisible(rightVisible);

        boolean arrowVisible = a.getBoolean(R.styleable.FormInputView_arrowVisible, true);
        setArrowVisible(arrowVisible);

        boolean starVisible = a.getBoolean(R.styleable.FormInputView_starVisible, false);
        setStarVisible(starVisible);

        boolean inputVisible = a.getBoolean(R.styleable.FormInputView_inputVisible, false);
        setInputVisible(inputVisible);

        boolean viewLineVisible = a.getBoolean(R.styleable.FormInputView_viewLineVisible, true);
        setViewLineVisible(viewLineVisible);

        String inputHint = a.getString(R.styleable.FormInputView_inputHint);
        if (inputHint != null) {
            inputEditText.setHint(inputHint);
        }
        a.recycle();
    }

    public void setIcon(Drawable icon) {
        iconImageView.setImageDrawable(icon);
        iconImageView.setVisibility(VISIBLE);
    }


    public TextView getLeftTextView() {
        return leftTextView;
    }


    public TextView getRightTextView() {
        return rightTextView;
    }

    public void setLabel(String label) {
        leftTextView.setText(label);
    }

    public EditText getInputEdit() {
        return inputEditText;
    }

    public void setInputText(String text) {
        inputEditText.setText(text);
    }

    public String getInputText() {
        return inputEditText.getText().toString();
    }

    //获取 right 的情况
    public String getTvRight() {
        if ("请选择".equals(rightTextView.getText().toString())) {
            return "";
        }
        return rightTextView.getText().toString();
    }

    public ImageView getArrowImageView() {
        return arrowImageView;
    }

    public void setTvRightText(String info) {
        if (!TextUtils.isEmpty(info)) {
            rightTextView.setText(info);
            rightTextView.setTextColor(ContextCompat.getColor(getContext(), R.color.base_primary_text_title));
        } else {
            rightTextView.setText("请选择");
            rightTextView.setTextColor(ContextCompat.getColor(getContext(), R.color.base_primary_text_hint));
        }
    }


    /**
     * 设置身份证的限制
     */
    public void setInputIDCardNumber() {
        inputEditText.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS);
        InputFilter filter = new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                StringBuilder filteredStringBuilder = new StringBuilder();
                for (int i = start; i < end; i++) {
                    char currentChar = source.charAt(i);
                    if (Character.isDigit(currentChar) || (currentChar == 'X' && dstart == dest.length())) {
                        filteredStringBuilder.append(currentChar);
                    }
                }
                return filteredStringBuilder.toString();
            }
        };
        inputEditText.setFilters(new InputFilter[]{filter});
    }

    public LinearLayout getLlLayout() {
        return llLayout;
    }

    /**
     * 设置手机号的限制
     */
    public void setInputPhone() {
        inputEditText.setInputType(InputType.TYPE_CLASS_NUMBER);
        inputEditText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(11)});
    }

    /**
     * 只能输入数字
     * 最多只能输入6位
     */
    public void setInputNumber() {
        inputEditText.setInputType(InputType.TYPE_CLASS_NUMBER);
    }


    /**
     * 只能输入数字
     * 最多只能输入6位
     */
    public void setInputNumberDecimal(int maxLength) {
        inputEditText.setInputType(InputType.TYPE_NUMBER_FLAG_DECIMAL);
        inputEditText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxLength)});
    }

    /**
     * 只能输入数字
     * 最多只能输入6位
     */
    public void setInputNumber(int maxLength) {
        inputEditText.setInputType(InputType.TYPE_CLASS_NUMBER);
        inputEditText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxLength)});
    }


    /**
     * 只能输入数字
     * 最多只能输入6位
     */
    public void setInputMaxLength(int maxLength) {
        inputEditText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxLength)});
    }


    /**
     * 设置布局的圆角
     */
    public void setLayoutRadius(int bottomLeftRadius, int bottomRightRadius, int topLeftRadius, int topRightRadius) {
        Drawable drawable = new DrawableCreator.Builder()
                .setCornersRadius(SizeUtils.dp2px(bottomLeftRadius), SizeUtils.dp2px(bottomRightRadius),
                        SizeUtils.dp2px(topLeftRadius), SizeUtils.dp2px(topRightRadius))
                .build();
        relativeLayout.setBackground(drawable);
    }


    public void setInfoVisible(boolean visible) {
        rightTextView.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public void setArrowVisible(boolean visible) {
        arrowImageView.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public void setStarVisible(boolean visible) {
        starTextView.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
    }

    public void setViewLineVisible(boolean visible) {
        viewline.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public void setInputVisible(boolean visible) {
        inputEditText.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
    }
}
