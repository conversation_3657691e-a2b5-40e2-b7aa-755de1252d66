package com.business_clean.app.weight.dialog

import android.content.Context
import android.text.TextUtils
import android.text.method.ScrollingMovementMethod
import android.view.View
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import com.business_clean.R
import com.business_clean.app.callback.OnDialogCancelListener
import com.business_clean.app.callback.OnDialogConfirmListener
import com.business_clean.app.callback.OnDialogCreateFinish
import com.business_clean.app.util.GlideUtil
import com.business_clean.databinding.DialogSystemAlertPopBinding
import com.lxj.xpopup.core.CenterPopupView


/**
 * 系统弹窗
 * 升级弹窗
 * 营销弹窗
 * 主标题
 * 副标题
 * 内容
 * 图片
 * 左按钮
 * 右按钮
 */
class CustomSystemAlertPop(context: Context) : CenterPopupView(context) {
    private var onDialogCancelListener: OnDialogCancelListener? = null
    private var onDialogInit: OnDialogCreateFinish? = null
    private var onDialogConfirmListener: OnDialogConfirmListener? = null
    private var mDialogSystemAlertPopDataBind: DialogSystemAlertPopBinding? = null

    private var isConfirmClickDismiss: Boolean = false//是否点击按钮后关闭弹窗

    override fun onCreate() {
        super.onCreate()
        mDialogSystemAlertPopDataBind = DataBindingUtil.bind(popupImplView)
        onBindViewClick()
        onDialogInit?.onViewInit()
    }

    override fun getImplLayoutId(): Int {
        return R.layout.dialog_system_alert_pop
    }

    private fun onBindViewClick() {
        mDialogSystemAlertPopDataBind?.tvLeft?.setOnClickListener {
            if (onDialogCancelListener != null) {
                onDialogCancelListener?.onCancel()
            }
            dialog?.dismiss()
        }
        mDialogSystemAlertPopDataBind?.ivImg?.setOnClickListener(OnClickListener {
            if (onDialogConfirmListener != null) {
                onDialogConfirmListener?.onConfirm()
            }
            dialog?.dismiss()
        })
        mDialogSystemAlertPopDataBind?.tvRight?.setOnClickListener {
            if (onDialogConfirmListener != null) {
                onDialogConfirmListener?.onConfirm()
            }
            if (isConfirmClickDismiss) {
                return@setOnClickListener
            }
            dialog?.dismiss()
        }
    }

    fun setListener(onDialogInit: OnDialogCreateFinish, onDialogCancelListener: OnDialogCancelListener, onSelectListener: OnDialogConfirmListener) {
        this.onDialogCancelListener = onDialogCancelListener
        this.onDialogInit = onDialogInit
        this.onDialogConfirmListener = onSelectListener
    }

    fun setConfirmClickDismiss(isConfirmClickDismiss: Boolean) {
        this.isConfirmClickDismiss = isConfirmClickDismiss
    }

    fun setTitle(text: String) {
        if (!TextUtils.isEmpty(text)) {
            mDialogSystemAlertPopDataBind?.tvMainTitle?.text = text
            mDialogSystemAlertPopDataBind?.tvMainTitle?.visibility = View.VISIBLE
        } else {
            mDialogSystemAlertPopDataBind?.tvMainTitle?.visibility = View.GONE
        }
    }

    fun setSubTitle(text: String) {
        if (!TextUtils.isEmpty(text)) {
            mDialogSystemAlertPopDataBind?.tvSubTitle?.text = text
            mDialogSystemAlertPopDataBind?.tvSubTitle?.visibility = View.VISIBLE
        } else {
            mDialogSystemAlertPopDataBind?.tvSubTitle?.visibility = View.GONE
        }
    }

    fun setSubTitleColor(color: Int) {
        mDialogSystemAlertPopDataBind?.tvSubTitle?.setTextColor(color)
    }

    fun setLeftButton(text: String) {
        if (!TextUtils.isEmpty(text)) {
            mDialogSystemAlertPopDataBind?.tvLeft?.text = text
            mDialogSystemAlertPopDataBind?.tvLeft?.visibility = View.VISIBLE
        } else {
            mDialogSystemAlertPopDataBind?.tvLeft?.visibility = View.GONE
        }
    }

    fun setRightButton(text: String) {
        if (!TextUtils.isEmpty(text)) {
            mDialogSystemAlertPopDataBind?.tvRight?.text = text
            mDialogSystemAlertPopDataBind?.tvRight?.visibility = View.VISIBLE
        } else {
            mDialogSystemAlertPopDataBind?.tvRight?.visibility = View.GONE
        }
    }

    fun setRightWeight(rightWeight: Int) {
        val layoutParams = mDialogSystemAlertPopDataBind?.tvRight?.layoutParams as LinearLayout.LayoutParams
        layoutParams.weight = rightWeight.toFloat()
        mDialogSystemAlertPopDataBind?.tvRight?.layoutParams = layoutParams
    }

    fun setContent(text: String) {
        if (!TextUtils.isEmpty(text)) {
            mDialogSystemAlertPopDataBind?.tvContent?.text = text
            mDialogSystemAlertPopDataBind?.tvContent?.visibility = View.VISIBLE
            //设置TextView 太长滚动，结合maxLines和scrollbars
            mDialogSystemAlertPopDataBind?.tvContent?.setMovementMethod(ScrollingMovementMethod.getInstance());
        } else {
            mDialogSystemAlertPopDataBind?.tvContent?.visibility = View.GONE
        }
    }

    fun setImgUrl(url: Any) {
        if (url is String) {
            if (!TextUtils.isEmpty(url)) {
                mDialogSystemAlertPopDataBind?.ivImg?.visibility = View.VISIBLE
                GlideUtil.loadPic(context, url, mDialogSystemAlertPopDataBind?.ivImg)
            } else {
                mDialogSystemAlertPopDataBind?.ivImg?.visibility = View.GONE
            }
        } else if (url is Int) {
            if (url != 0) {
                mDialogSystemAlertPopDataBind?.ivImg?.visibility = View.VISIBLE
                mDialogSystemAlertPopDataBind?.ivImg?.setImageResource(url)
            } else {
                mDialogSystemAlertPopDataBind?.ivImg?.visibility = View.GONE
            }
        }
    }

    fun setContentBgColor(contentBgColor: Int) {
        mDialogSystemAlertPopDataBind?.tvContent?.setBackgroundColor(ContextCompat.getColor(context, contentBgColor))
    }
}