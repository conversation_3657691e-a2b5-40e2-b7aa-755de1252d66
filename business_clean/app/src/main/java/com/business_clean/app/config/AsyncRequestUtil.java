package com.business_clean.app.config;

import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.business_clean.app.App;
import com.business_clean.app.callback.OnClassesCallBack;
import com.business_clean.app.callback.OnNationListener;
import com.business_clean.app.callback.OnProjectAllDataCallBack;
import com.business_clean.app.callback.ProviceCityAreaDataCallBack;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.data.initconfig.city.ProviceCityAreaListData;
import com.business_clean.data.initconfig.NationEntity;
import com.business_clean.data.mode.classes.ClassesEntity;
import com.business_clean.data.mode.init.InitDataEntity;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.data.mode.project.ProjectManager;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.google.android.gms.tasks.OnSuccessListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import me.hgj.mvvmhelper.ext.LogExtKt;
import rxhttp.wrapper.param.RxHttp;

/**
 * 需要在后台 请求的操作
 */
public class AsyncRequestUtil {

    /**
     * 请求城市列表
     *
     * @param callBack
     */
    public static void requestProviceCityArea(final ProviceCityAreaDataCallBack callBack) {
        RxHttp.get(NetUrl.INIT_CITY)
                .asResponse(ProviceCityAreaListData.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<ProviceCityAreaListData>() {
                    @Override
                    public void accept(ProviceCityAreaListData list) throws Throwable {
                        MMKVHelper.putString(ConstantMMVK.PROVICE_CITY_AREA_DATA, JSON.toJSONString(list.getList()));
                        if (callBack != null) {
                            callBack.cityDataCallBack(list.getList());
                        }
                    }
                });
    }

    /**
     * 请求源数据
     */
    public static void requestInitData() {
        RxHttp.get(NetUrl.GET_INIT_DATA)
                .asResponse(InitDataEntity.class)
                .subscribe(new Consumer<InitDataEntity>() {
                    @Override
                    public void accept(InitDataEntity initDataEntity) throws Throwable {
                        LogExtKt.logI("请求回来的源数据", "");
                        MMKVHelper.putString(ConstantMMVK.INIT_DATA, JSON.toJSONString(initDataEntity));
                        MMKVHelper.putString(ConstantMMVK.IN_CLASS_TIME_LIST, JSON.toJSONString(initDataEntity.getIn_class_time_list()));
                        MMKVHelper.putString(ConstantMMVK.OUT_CLASS_TIME_LIST, JSON.toJSONString(initDataEntity.getOut_class_time_list()));
                    }
                });
    }


    /**
     * 通知后台切换了接口
     */
    public static void requestChangeProject(String uuid) {
        RxHttp.get(NetUrl.CHANGE_PROJECT)
                .add("project_uuid", uuid)
                .asResponse(Object.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe();
    }

    /**
     * 后台更新个人信息
     */
    public static void requestUserInfoData() {
        RxHttp.get(NetUrl.GET_USER_INFO)
                .asResponse(UserInfo.class)
                .subscribe(new Consumer<UserInfo>() {
                    @Override
                    public void accept(UserInfo userInfo) throws Throwable {
                        MMKVHelper.putString(ConstantMMVK.USER_INFO, JSON.toJSONString(userInfo));
                        App.getAppViewModelInstance().getProjectInfo().setValue(userInfo.getProject());
                        App.getAppViewModelInstance().getUserInfo().setValue(userInfo);
                    }
                });
    }


    /**
     * 请求民族列表
     */
    public static void requestNationData(OnNationListener listener) {
        RxHttp.get(NetUrl.GET_NATION_LIST)
                .asResponse(NationEntity.class)
                .subscribe(new Consumer<NationEntity>() {
                    @Override
                    public void accept(NationEntity data) throws Throwable {
                        MMKVHelper.putString(ConstantMMVK.NATION_LIST, JSON.toJSONString(data.getList()));
                        if (listener != null) {
                            listener.nationCallBack(data.getList());
                        }
                    }
                });
    }


    /**
     * 刷新token
     */
    public static void requestRefreshToken() {
        RxHttp.get(NetUrl.REGRESH_TOKEN)
                .asResponse(Object.class)
                .subscribe(new Consumer<Object>() {
                    @Override
                    public void accept(Object data) throws Throwable {
                    }
                });
    }


    /**
     * 更新所有项目
     */
    public static void requestProjectAll() {
        RxHttp.get(NetUrl.GET_PROJECT_MANGER_ALL)
                .asResponse(ProjectManager.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<ProjectManager>() {
                    @Override
                    public void accept(ProjectManager data) throws Throwable {
                        App.getAppViewModelInstance().getAllProjectManager().setValue(data);
                    }
                });
    }


    /**
     * 获取指定项目列表（获取就行，后端自己判断了角色筛选）
     *
     * @param callBack
     */
    public static void requestAppointProjectAll(boolean isHeadOffice, final OnProjectAllDataCallBack callBack) {
        RxHttp.get(NetUrl.GET_DEPART_GET_PROJECT_ALL)
                //是否需要总部项目 1是2否
                .add("is_head_office", isHeadOffice ? "1" : "2")
                .asResponse(ProjectManager.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<ProjectManager>() {
                    @Override
                    public void accept(ProjectManager list) throws Throwable {
                        if (callBack != null) {
                            callBack.cityDataCallBack(list.getList());
                        }
                    }
                });

    }


    /**
     * 获取班次的列表
     */
    public static void requestClassesAll(String project_uuid, final OnClassesCallBack callBack) {
        RxHttp.get(NetUrl.GET_CLASSES_ALL)
                .add("page", "1")
                .add("size", "100")
                .add("project_uuid", "" + project_uuid)
                .asResponse(ClassesEntity.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<ClassesEntity>() {
                    @Override
                    public void accept(ClassesEntity list) throws Throwable {
                        if (callBack != null) {
                            callBack.onDataCallBack(list.getList());
                        }
                    }
                });
    }


    /**
     * 删除图片
     */
    public static void requestDeleteImage(String uuid, final OnSuccessListener onSuccessListener) {
        RxHttp.get(NetUrl.DELETE_WORK_CIRILE_PHOTO)
                .add("uuid", uuid)
                .asResponse(Object.class)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<Object>() {
                    @Override
                    public void accept(Object list) throws Throwable {
                        if (onSuccessListener != null) {
                            onSuccessListener.onSuccess(null);
                        }
                    }
                });
    }
}
