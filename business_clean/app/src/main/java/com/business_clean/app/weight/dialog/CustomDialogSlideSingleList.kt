package com.business_clean.app.weight.dialog

import android.content.Context
import android.text.TextUtils
import android.widget.TextView
import com.bigkoo.pickerview.adapter.ArrayWheelAdapter
import com.business_clean.R
import com.business_clean.app.callback.OnDialogCancelListener
import com.business_clean.app.callback.OnDialogCreateFinish
import com.business_clean.app.callback.OnDialogSelectListener
import com.contrarywind.view.WheelView
import com.lxj.xpopup.core.BottomPopupView

/**
 * 配置了Wheel的列表
 */
class CustomDialogSlideSingleList(context: Context) : BottomPopupView(context) {

    private var mCancel: TextView? = null
    private var mConfirm: TextView? = null
    private var mCenterText: TextView? = null
    private var mWheelView: WheelView? = null

    private var onDialogCancelListener: OnDialogCancelListener? = null
    private var onDialogInit: OnDialogCreateFinish? = null
    private var onDialogSelectListener: OnDialogSelectListener? = null
    private var mDataList = ArrayList<String>()

    override fun getImplLayoutId() = R.layout.common_dialog_slide_list

    override fun onCreate() {
        super.onCreate()
        mCenterText = findViewById(R.id.tv_common_dialog_center)
        mCancel = findViewById(R.id.tv_common_dialog_cancel)
        mConfirm = findViewById(R.id.tv_common_dialog_yes)
        mWheelView = findViewById(R.id.wv_common_dialog_slide_wheel)
        onBindViewClick()
        onDialogInit?.onViewInit()
    }

    private fun onBindViewClick() {
        mCancel?.setOnClickListener {
            onDialogCancelListener?.onCancel()
            dialog?.dismiss()
        }
        mConfirm?.setOnClickListener {
            mWheelView?.currentItem?.let { it1 -> onDialogSelectListener?.onSelect(it1, mWheelView?.adapter?.getItem(it1).toString()) }
            dialog?.dismiss()
        }
    }

    fun setListener(onDialogInit: OnDialogCreateFinish, onDialogCancelListener: OnDialogCancelListener, onSelectListener: OnDialogSelectListener) {
        this.onDialogCancelListener = onDialogCancelListener
        this.onDialogInit = onDialogInit
        this.onDialogSelectListener = onSelectListener
    }

    fun setDataList(mList: ArrayList<String>) {
        this.mDataList = mList
        mWheelView?.adapter = ArrayWheelAdapter(mList)
    }

    fun getWheelView(): WheelView? {
        return mWheelView
    }

    // 字体大小
    fun setWheelTextSize(size: Float) {
        mWheelView?.setTextSize(size)
    }

    // 默认选择那个
    fun setCurrentItem(index: Int) {
        mWheelView?.currentItem = index
    }

    // 是否循环
    fun setWheelCyclic(isCyclic: Boolean) {
        mWheelView?.setCyclic(isCyclic)
    }

    // 反选
    fun setWheelInverseText(text: String? = "") {
        if (!TextUtils.isEmpty(text)) {
            mWheelView?.currentItem = mDataList.indexOf(text)
        }
    }


    fun getCancelView(): TextView? {
        return mCancel
    }

    fun getCenterTextView(): TextView? {
        return mCenterText
    }

    fun setCenterText(text: String? = "") {
        mCenterText?.text = text
    }
}