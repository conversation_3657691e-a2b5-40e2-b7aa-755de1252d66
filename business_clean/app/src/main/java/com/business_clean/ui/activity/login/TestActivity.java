package com.business_clean.ui.activity.login;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import android.os.Bundle;

import com.business_clean.R;
import com.business_clean.app.base.BaseActivity;
import com.business_clean.databinding.ActivityTestBinding;
import com.business_clean.viewmodel.request.LoginVideModel;


public class TestActivity extends BaseActivity<LoginVideModel, ActivityTestBinding> {

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {

    }


}