package com.business_clean.ui.activity.company;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.alibaba.fastjson.JSON;
import com.business_clean.app.base.BaseActivity;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.DividerItemDecoration;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.data.mode.project.WorkRulesEntity;
import com.business_clean.databinding.ActivitySelectCompanyBinding;
import com.business_clean.ui.activity.CheckPermissionActivity;
import com.business_clean.ui.activity.login.LoginActivity;
import com.business_clean.ui.activity.main.MainActivity;
import com.business_clean.ui.adapter.company.CompanyAdapter;
import com.business_clean.viewmodel.request.LoginVideModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;

import java.util.HashMap;

import me.hgj.mvvmhelper.ext.AppExtKt;

public class SelectCompanyActivity extends BaseActivity<LoginVideModel, ActivitySelectCompanyBinding> {

    private UserInfo userInfo;
    private CompanyAdapter mAdapter;

    private String company_uuid;
    private String company_name;

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        mToolbar.setTitle("请选择企业");
        if (getIntent() != null && getIntent().getExtras() != null) {
            userInfo = (UserInfo) getIntent().getExtras().getSerializable("user_info");
        } else {
            finish();
        }
        mToolbar.setBackButtonVisible(false);
        mAdapter = new CompanyAdapter();
        mDatabind.recycler.setLayoutManager(new LinearLayoutManager(this));
        mDatabind.recycler.addItemDecoration(new DividerItemDecoration(this));
        mDatabind.recycler.setAdapter(mAdapter);
        if (userInfo != null) {
            mAdapter.setList(userInfo.getCompany_list());
        }
    }

    @Override
    public void onBindViewClick() {
        mAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                company_uuid = mAdapter.getData().get(position).getUuid();//拿到企业的uuid
                company_name = mAdapter.getData().get(position).getCompany_name();//拿到企业的uuid
                HashMap<String, String> hashMap = new HashMap<>();
                hashMap.put("company_uuid", "" + mAdapter.getData().get(position).getUuid());
                if (userInfo != null && !TextUtils.isEmpty(userInfo.getLogin_user_uuid())) {
                    hashMap.put("login_user_uuid", "" + userInfo.getLogin_user_uuid());
                }
                mViewModel.requestCompany(hashMap);
            }
        });
    }


    @Override
    public void onRequestSuccess() {
        mViewModel.getSelectUserInfo().observe(this, new Observer<UserInfo>() {
            @Override
            public void onChanged(UserInfo userInfo) {
                MMKVHelper.putString(ConstantMMVK.COMPANY_UUID, company_uuid);
                MMKVHelper.putString(ConstantMMVK.COMPANY_NAME, company_name);
                MMKVHelper.putString(ConstantMMVK.TOKEN, userInfo.getToken());
                MMKVHelper.putString(ConstantMMVK.USER_INFO, JSON.toJSONString(userInfo));
                CommonUtils.updateLocalUserData(userInfo);
                //再这里请求企业配置，然后再重新获取以下内容
                mViewModel.requestWorkRules();

            }
        });

        //i企业配置
        mViewModel.getWorkRulesEntity().observe(this, new Observer<WorkRulesEntity>() {
            @Override
            public void onChanged(WorkRulesEntity workRulesEntity) {
                CommonUtils.updateCompanyConfig(workRulesEntity);
                if (Constant.ROLE_CLEAN_ID.equals(Constant.ROLE_ID)) {
                    //加一层判断，是否开启极简模式 默认是false
                    if (!MMKVHelper.getBoolean(ConstantMMVK.IS_MINIMALISM, true)) {
                        Bundle bundle = new Bundle();
                        bundle.putBoolean("openCamera", true);
                        ActivityForwardUtil.startActivity(MainActivity.class, bundle);
                    } else {
                        ActivityForwardUtil.startActivity(CheckPermissionActivity.class);
                    }
                } else {
                    ActivityForwardUtil.startActivity(MainActivity.class);
                }
                AppExtKt.finishActivity(SelectCompanyActivity.class);
                AppExtKt.finishActivity(LoginActivity.class);
            }
        });
    }

    @Override
    public void onBackPressed() {
    }


}