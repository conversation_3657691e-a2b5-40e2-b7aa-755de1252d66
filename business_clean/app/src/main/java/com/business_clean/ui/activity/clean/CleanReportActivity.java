package com.business_clean.ui.activity.clean;

import androidx.annotation.NonNull;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import com.blankj.utilcode.util.LogUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseAgentWebActivity;
import com.business_clean.app.config.OnJsBridge;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.DownLoadHelper;
import com.business_clean.app.util.share.ShareHelperTools;
import com.business_clean.app.util.share.ShareParams;
import com.business_clean.app.weight.dialog.PagerDrawerPopup;
import com.business_clean.databinding.ActivityCleanReportBinding;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.XXPermissions;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.just.agentweb.AgentWeb;
import com.lxj.xpopup.XPopup;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;

import me.hgj.mvvmhelper.base.BaseViewModel;
import me.hgj.mvvmhelper.ext.AppExtKt;

/**
 * 清洁报告
 */
public class CleanReportActivity extends BaseAgentWebActivity<BaseViewModel, ActivityCleanReportBinding> {

    private String stat_month;
    private String url;

    private String projectUuid = "";

    @Override
    public void initView(Bundle savedInstanceState) {
        if (getIntent() != null && getIntent().getExtras() != null) {
            stat_month = getIntent().getExtras().getString("stat_month");
            url = getIntent().getExtras().getString("url");
        }
        projectUuid = App.getAppViewModelInstance().getProjectInfo().getValue().getUuid();
        mToolbar.setSubTitle(App.getAppViewModelInstance().getProjectInfo().getValue().getProject_short_name());
        mToolbar.setRightIcon(R.mipmap.icon_change);
    }


    @Override
    public String getBarTitle() {
        return "清洁报告";
    }


    @Override
    public void onBindViewClick() {
        mToolbar.setOnImageRightTextClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //针对权限做处理 只有管理员跟人事可以切
                new XPopup.Builder(CleanReportActivity.this)
                        .autoFocusEditText(false)
                        .asCustom(new PagerDrawerPopup(CleanReportActivity.this, projectUuid, false, new PagerDrawerPopup.OnSelectProjectListener() {
                            @Override
                            public void onClick(String project_uuid, String project_name) {
                                mToolbar.setSubTitle(project_name);
                                projectUuid = project_uuid;
                                if (mAgentWeb != null && mAgentWeb.get() != null) {
                                    mAgentWeb.get().getUrlLoader().loadUrl(CommonUtils.getWebUrlHost() + NetUrl.WEB_PROJECT_CLEAN_REPORT_URL + "status=3&project_uuid=" + project_uuid + "&stat_month=" + stat_month);
                                }
                            }
                        })).show();
            }
        });
    }

    @Override
    public void initObserver() {

    }

    @Override
    protected void onRestart() {
        super.onRestart();
        if (mAgentWeb != null && mAgentWeb.get() != null) {
            mAgentWeb.get().getUrlLoader().reload();
        }
    }


    @Override
    protected ViewGroup getAgentWebParent() {
        return mDatabind.llLayout;
    }

    @Override
    protected String getUrl() {
        if (!TextUtils.isEmpty(url)) {
            return url;
        }
        return CommonUtils.getWebUrlHost() + NetUrl.WEB_PROJECT_CLEAN_REPORT_URL + "status=3&project_uuid=" + projectUuid + "&stat_month=" + stat_month;
    }

    @Override
    protected OnJsBridge getOnJsBridge() {
        return new JsBridge(mAgentWeb.get());
    }

    class JsBridge extends OnJsBridge {
        JsBridge(AgentWeb mAgentWeb) {
            super(mAgentWeb);
        }

        @JavascriptInterface
        public void backPage(String json) {
            LogUtils.e("backPage json --> " + json);
            try {
                CleanReportActivity.this.finish();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @JavascriptInterface
        public void goFlutter(String json) {
            LogUtils.e("json --> " + json);
            try {
                if (!TextUtils.isEmpty(json)) {
                    JSONObject jsonObject = new JSONObject(json);
                    String uuid = jsonObject.getString("uuid");
                    String type = jsonObject.getString("type");

                    if ("3".equals(type)) {
                        HashMap<String, Object> hashMap = new HashMap<>();
                        hashMap.put("uuid", "" + uuid);
                        hashMap.put("type", "1");
                        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                                .pageName("workPlanDetailPage")
                                .arguments(hashMap)
                                .build());
                    } else {
                        CommonToFlutter.gotoFlutterTaskOnePage(uuid, null, false);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @JavascriptInterface
        public void shareUrl(String json) {
            LogUtils.e("json --> " + json);
            try {
                if (!TextUtils.isEmpty(json)) {
                    JSONObject jsonObject = new JSONObject(json);
                    String url = jsonObject.getString("url");
                    String stat_month = jsonObject.getString("stat_month");
                    ShareParams params = new ShareParams();
                    params.setLinkUrl(url);
                    params.setTitle(stat_month + "-清洁报告");
                    params.setDescription("" + App.getAppViewModelInstance().getProjectInfo().getValue().getProject_short_name());
                    ShareHelperTools.getInstance().shareCardLink(params, CleanReportActivity.this);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @JavascriptInterface
        public void shareWord(String json) {
            LogUtils.e("json --> " + json);
            try {
                if (!TextUtils.isEmpty(json)) {
                    JSONObject jsonObject = new JSONObject(json);
                    String downloadUrl = jsonObject.getString("url");
                    String stat_month = jsonObject.getString("stat_month");
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            XXPermissions.with(CleanReportActivity.this)
                                    .permission(AppExtKt.getExternalStorage())
                                    .request(new OnPermissionCallback() {
                                        @Override
                                        public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                                            if (allGranted) {
                                                String fileName = App.getAppViewModelInstance().getProjectInfo().getValue().getProject_short_name() + stat_month + "清洁报告";
                                                DownLoadHelper.downloadFile(CleanReportActivity.this, downloadUrl, null, fileName, true);
                                            }
                                        }
                                    });
                        }
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


        @JavascriptInterface
        public void openNewHtml(String json) {
            LogUtils.e("json --> " + json);
            try {
                if (!TextUtils.isEmpty(json)) {
                    JSONObject jsonObject = new JSONObject(json);
                    String url = jsonObject.getString("url");
                    Bundle bundle = new Bundle();
                    bundle.putString("url", CommonUtils.getWebHost() + url);
                    ActivityForwardUtil.startActivity(CleanReportActivity.class, bundle);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}