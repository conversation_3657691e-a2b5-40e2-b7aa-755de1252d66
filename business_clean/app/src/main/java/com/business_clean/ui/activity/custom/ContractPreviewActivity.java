package com.business_clean.ui.activity.custom;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.base.BaseAgentWebActivity;
import com.business_clean.app.config.OnJsBridge;
import com.business_clean.app.util.share.ShareHelperTools;
import com.business_clean.app.util.share.ShareParams;
import com.business_clean.app.util.share.ShareType;
import com.business_clean.databinding.ActivityContractPreviewBinding;
import com.business_clean.viewmodel.request.ContractViewModel;
import com.just.agentweb.AgentWeb;

import org.json.JSONObject;

public class ContractPreviewActivity extends BaseAgentWebActivity<ContractViewModel, ActivityContractPreviewBinding> {

    private String url;
    private String share_title;

    @Override
    public void initView(Bundle savedInstanceState) {
        mToolbar.setTitle("预览电子合同");
        if (getIntent() != null && getIntent().getExtras() != null) {
            url = getIntent().getExtras().getString("url");
            share_title = getIntent().getExtras().getString("share_title");
        }
    }

    @Override
    public void onBindViewClick() {

    }

    @Override
    public void initObserver() {

    }

    @Override
    protected ViewGroup getAgentWebParent() {
        return mDatabind.flLayout;
    }

    @Override
    protected String getUrl() {
        return url;
    }


    @Override
    protected OnJsBridge getOnJsBridge() {
        return new JsBridge(mAgentWeb.get());
    }


    class JsBridge extends OnJsBridge {
        JsBridge(AgentWeb mAgentWeb) {
            super(mAgentWeb);
        }

        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void backPage() {
            finish();
        }

        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void shareContract(String json) {
            LogUtils.e("json =----shareContract----" + json);
            try {
                JSONObject jsonObject = new JSONObject(json);
                String url = jsonObject.getString("share_url");
                int shareWX = Integer.parseInt(jsonObject.getString("shareType"));
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ShareParams params = new ShareParams();
                        if (shareWX == 2) {
                            params.setShareType(ShareType.WEIXIN);
                        } else {
                            params.setShareType(ShareType.WWEIXIN);
                        }
                        if (!TextUtils.isEmpty(share_title)) {
                            params.setTitle(share_title);
                        } else {
                            params.setTitle("电子合同详情");
                        }
                        params.setWWeiXinDescription(url);
                        params.setLinkUrl(url);
                        ShareHelperTools.getInstance().shareCardLink(params, ContractPreviewActivity.this);
                    }
                });

            } catch (Exception e) {
                e.printStackTrace();
            }

        }

    }

}