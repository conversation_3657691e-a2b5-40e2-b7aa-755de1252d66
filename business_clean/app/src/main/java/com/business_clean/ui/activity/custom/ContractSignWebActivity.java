package com.business_clean.ui.activity.custom;

import androidx.lifecycle.Observer;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.base.BaseAgentWebActivity;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.config.OnJsBridge;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.share.ShareHelperTools;
import com.business_clean.app.util.share.ShareParams;
import com.business_clean.app.util.share.ShareType;
import com.business_clean.app.weight.dialog.ContractSharePopup;
import com.business_clean.data.mode.contract.ContractShareInfoEntity;
import com.business_clean.databinding.ActivityContractSignWebBinding;
import com.business_clean.viewmodel.request.ContractViewModel;
import com.idlefish.flutterboost.FlutterBoost;
import com.just.agentweb.AgentWeb;
import com.lxj.xpopup.XPopup;

import org.json.JSONObject;

import java.io.Serializable;
import java.util.HashMap;

public class ContractSignWebActivity extends BaseAgentWebActivity<ContractViewModel, ActivityContractSignWebBinding> {

    public static final int TYPE_ENTRY = 100;
    public static final int TYPE_SEPARATION = 200;
    private String url;
    private String contract_uuid;
    private String customer_id;

    private String shareTitle;

    private boolean gotoMain = true;

    //默认是2 默认分享到微信
    private int shareWX = 2;


    @Override
    public void initView(Bundle savedInstanceState) {
        mToolbar.setTitle("合同编辑");
        if (getIntent() != null && getIntent().getExtras() != null) {
            url = getIntent().getExtras().getString("url");
            gotoMain = getIntent().getExtras().getBoolean("goto_main", true);
            contract_uuid = getIntent().getExtras().getString("contract_uuid");
            customer_id = getIntent().getExtras().getString("customer_id");
            shareTitle = getIntent().getExtras().getString("share_title");
        }
        shareWX = MMKVHelper.getInt(ConstantMMVK.SHARE_MODE_TYPE, 2);
        LogUtils.e("分享方式 shareWX =" + shareWX);
    }

    @Override
    public void onBindViewClick() {

    }

    @Override
    public void initObserver() {
        mViewModel.getShareContractInfo().observe(this, new Observer<ContractShareInfoEntity>() {
            @Override
            public void onChanged(ContractShareInfoEntity contractShareInfoEntity) {
                shareWX = MMKVHelper.getInt(ConstantMMVK.SHARE_MODE_TYPE, 2);

                if (!TextUtils.isEmpty(contractShareInfoEntity.getSign_url())) {

                    ///看是跳转到企业微信还是微信
                    ShareParams params = new ShareParams();
                    if (shareWX == 2) {
                        params.setShareType(ShareType.WEIXIN);
                    } else {
                        params.setShareType(ShareType.WWEIXIN);
                    }
                    params.setWWeiXinDescription(contractShareInfoEntity.getSign_url());
                    params.setLinkUrl(contractShareInfoEntity.getSign_url());
                    if (!TextUtils.isEmpty(shareTitle)) {
                        params.setTitle(shareTitle);
                    } else {
                        params.setTitle("签署电子合同");
                    }
                    ShareHelperTools.getInstance().shareCardLink(params, ContractSignWebActivity.this);


                    new XPopup.Builder(ContractSignWebActivity.this)
                            .borderRadius(10)
                            .dismissOnTouchOutside(false)
                            .dismissOnBackPressed(false)
                            .autoDismiss(false)
                            .asCustom(new ContractSharePopup(ContractSignWebActivity.this, contractShareInfoEntity.getSign_url(), new ContractSharePopup.SharePopupListener() {
                                @Override
                                public void onXpopupDismiss() {
                                    //这里才是最烦的，要给flutter发个消息，让他列表刷新
                                    FlutterBoost.instance().sendEventToFlutter("refresh_contract_records", new HashMap<>());
                                    ContractSignWebActivity.this.finish();
//                                    if (!gotoMain) { //不跳转就单独关闭某个
//                                        ContractSignWebActivity.this.finish();
//                                        return;
//                                    }
//                                    MMKVHelper.putBoolean(ConstantMMVK.JUMP_TODO_PAGE, true);
//                                    ActivityForwardUtil.startActivityAndClearOther(MainActivity.class, new Bundle());
                                }
                            }))
                            .show();
                }
            }
        });


        //离职申请
        mViewModel.getDepart().

                observe(this, new Observer<Object>() {
                    @Override
                    public void onChanged(Object o) {
                        //成功后，去获取链接
                        mViewModel.requestContractShareInfo(customer_id, contract_uuid);
                    }
                });
    }

    @Override
    protected OnJsBridge getOnJsBridge() {
        return new JsBridge(mAgentWeb.get());
    }

    class JsBridge extends OnJsBridge {
        JsBridge(AgentWeb mAgentWeb) {
            super(mAgentWeb);
        }

        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void backPage() {
            finish();
        }

        @JavascriptInterface
        public void doContractSign(String json) {
            LogUtils.e("json =" + json);////目前可能没用，现在的阶段是直接弹出分享按钮 ，获取分享的链接
//            mViewModel.requestContractShareInfo(customer_id, contract_uuid);///先别请求，后置

            try {

                if (getIntent() != null && getIntent().getExtras() != null) {
                    Serializable hashMap = getIntent().getSerializableExtra("hashMap");
                    int type = getIntent().getIntExtra("type", 0);
                    if (!TextUtils.isEmpty(json)) {
                        JSONObject jsonObject = new JSONObject(json);
                        LogUtils.e("-----hashMap---" + hashMap);
                        LogUtils.e("-----type---" + type);
                        if (hashMap != null) {
                            //拿到分享的内容 shareType:     1 企业微信          2微信
                            MMKVHelper.putInt(ConstantMMVK.SHARE_MODE_TYPE, Integer.parseInt(jsonObject.getString("shareType")));
                            if (type == TYPE_ENTRY) {
//                        int entry_type = getIntent().getIntExtra("entry_type", 0);
//                        mViewModel.requestUpdateMembers((HashMap<String, String>) hashMap, AddProjectActivity.ID_CONTRACT, entry_type);
                                mViewModel.requestContractShareInfo(customer_id, contract_uuid);
                            } else if (type == TYPE_SEPARATION) {
                                mViewModel.requestMembersDepart((HashMap<String, String>) hashMap);
                            }
                        }
                    }

                }


            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    @Override
    protected ViewGroup getAgentWebParent() {
        return mDatabind.flLayout;
    }

    @Override
    protected String getUrl() {
        return url + "&shareType=" + shareWX;
    }
}