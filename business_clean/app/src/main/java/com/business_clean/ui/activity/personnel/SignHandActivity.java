package com.business_clean.ui.activity.personnel;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.View;

import com.blankj.utilcode.util.TimeUtils;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseActivity;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.ImageCompressor;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.UploadFileHelper;
import com.business_clean.data.mode.members.IDCardMemberEntity;
import com.business_clean.databinding.ActivitySignHandBinding;
import com.business_clean.ui.activity.main.MainActivity;
import com.business_clean.viewmodel.request.ProjectMembersViewModel;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

import me.hgj.mvvmhelper.ext.AppExtKt;

public class SignHandActivity extends BaseActivity<ProjectMembersViewModel, ActivitySignHandBinding> {

    private String uuid;

    @Override
    public boolean showToolBar() {
        return false;
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        if (getIntent() != null && getIntent().getExtras() != null) {
            uuid = getIntent().getExtras().getString("uuid");
        }
    }

    @Override
    public void onBindViewClick() {
        //完成
        mDatabind.tvSignHandFinish.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                XXPermissions.with(SignHandActivity.this)
                        .permission(AppExtKt.getExternalStorage())
                        .request(new OnPermissionCallback() {
                            @Override
                            public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                                if (allGranted) {
                                    uploadHandBitmap();
                                }
                            }
                        });

            }
        });
        //关闭当前界面
        mDatabind.tvSignHandClean.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mDatabind.hand.redo();
            }
        });
        //清空界面
        mDatabind.tvSignHandClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    private void uploadHandBitmap() {
        if (mDatabind.hand.getBitmap() == null) {
            ToastUtil.show("请签名");
            return;
        }
        File handFile = saveBitmapToFile(mDatabind.hand.getBitmap(), "sign_hand_" + TimeUtils.getNowMills());//存储到内部路径  每次都覆盖
        if (handFile == null) {
            ToastUtil.show("图片保存异常，请重新尝试");
            return;
        }
        ToastUtil.show("上传中，请稍等");
        ImageCompressor.compressBitmap(this, handFile.getAbsolutePath(), 10, new OnKeyValueResultCallbackListener() {
            @Override
            public void onCallback(String srcPath, String resultPath) {
                UploadFileHelper.getInstance().uploadPictures(SignHandActivity.this, resultPath, UploadFileHelper.PATH_HEADER_PERSONNEL, new UploadFileHelper.UploadListener() {
                    @Override
                    public void onUploadSuccess(String response) {
                        HashMap<String, String> hashMap = new HashMap<>();
                        hashMap.put("op_type", "approve");
                        hashMap.put("uuid", uuid);
                        hashMap.put("sign_pic", "" + response);
                        mViewModel.requestUpdateMembers(hashMap, 1);
                    }

                    @Override
                    public void onUploadFailed(String error) {
                        ToastUtil.show("上传失败，请重试");
                    }

                    @Override
                    public void onUploadProgress(int progress) {

                    }
                });
            }
        });
    }

    @Override
    public void onRequestSuccess() {
        mViewModel.getUpdateSignHandStatus().observe(this, new Observer<IDCardMemberEntity>() {
            @Override
            public void onChanged(IDCardMemberEntity idCardMemberEntity) {
                App.getAppViewModelInstance().getRefreshMember().setValue(true);
                ToastUtil.show("操作成功");
                MMKVHelper.putBoolean(ConstantMMVK.JUMP_TODO_PAGE, true);
                ActivityForwardUtil.startActivityAndClearOther(MainActivity.class, new Bundle());
            }
        });
    }

    /**
     * 存储图片到本地  路径是内部路径 不对外开放
     *
     * @param bitmap
     * @param fileName
     * @return
     */
    private File saveBitmapToFile(Bitmap bitmap, String fileName) {
        // 获取外部存储目录
        File externalFilesDir = new File(Constant.INTERNAL_SIGN_HAND_PATH);

        File signHandDir = new File(externalFilesDir, "sign-hand");
        if (!signHandDir.exists()) {
            signHandDir.mkdirs();
        }

        // 创建目标文件
        File file = new File(signHandDir, fileName + ".png");

        try {
            // 创建输出流
            FileOutputStream fos = new FileOutputStream(file);

            // 将Bitmap压缩为PNG格式并写入文件
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos);

            // 关闭流
            fos.close();

            return file; // 返回保存的文件对象
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

}