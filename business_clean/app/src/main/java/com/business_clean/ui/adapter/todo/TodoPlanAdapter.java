package com.business_clean.ui.adapter.todo;

import android.view.MotionEvent;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.data.mode.todo.PlanListEntity;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.luck.picture.lib.decoration.GridSpacingItemDecoration;

import org.jetbrains.annotations.NotNull;

public class TodoPlanAdapter extends BaseQuickAdapter<PlanListEntity, BaseViewHolder> {

    private int type;

    public TodoPlanAdapter() {
        super(R.layout.item_todo_item_plan);
        addChildClickViewIds(R.id.tv_item_take_photo, R.id.ll_item_layout);
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    protected void convert(@NotNull BaseViewHolder holder, PlanListEntity item) {
        holder.setText(R.id.tv_item_todo_item_plan_title, item.getWork_content())
                .setText(R.id.tv_item_plan_desc, item.getProject_short_name() + "   " + item.getEnd_date() + "截止");
        RecyclerView rv_work = holder.getView(R.id.recycler_item_work_photos);

        rv_work.setLayoutManager(new GridLayoutManager(rv_work.getContext(), 4));
        // 移除之前添加的ItemDecoration
        if (rv_work.getItemDecorationCount() > 0) {
            rv_work.removeItemDecorationAt(0);
        }
        rv_work.addItemDecoration(new GridSpacingItemDecoration(4, SizeUtils.dp2px(10f), false));

        // Set adapter data
        TaskImageAdapter mAdapter = new TaskImageAdapter();
        rv_work.setAdapter(mAdapter);
        mAdapter.setList(item.getMedia_list());

        rv_work.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (v instanceof RecyclerView && v.getId() != 0 && event.getAction() == MotionEvent.ACTION_DOWN) {
                    CommonToFlutter.gotoFlutterTaskOnePage(item.getUuid(), String.valueOf(type), false);
                }
                return false;
            }
        });
    }


}