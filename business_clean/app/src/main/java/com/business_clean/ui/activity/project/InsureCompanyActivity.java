package com.business_clean.ui.activity.project;

import androidx.appcompat.app.AppCompatActivity;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import com.business_clean.app.base.BaseAgentWebActivity;
import com.business_clean.app.config.OnJsBridge;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.databinding.ActivityInsureCompanyBinding;
import com.business_clean.viewmodel.request.ContractViewModel;
import com.just.agentweb.AgentWeb;

import org.json.JSONObject;

public class InsureCompanyActivity extends BaseAgentWebActivity<ContractViewModel, ActivityInsureCompanyBinding> {


    private String url;

    @Override
    public void initView(Bundle savedInstanceState) {
        mToolbar.setTitle("项目参保方案");
        setCustomTitle(false);
        if (getIntent() != null && getIntent().getExtras() != null) {
            url = getIntent().getExtras().getString("url");
            String projectName = getIntent().getExtras().getString("project_name");
            mToolbar.setSubTitle(projectName);
        }
    }

    @Override
    protected ViewGroup getAgentWebParent() {
        return mDatabind.flLayout;
    }

    @Override
    protected String getUrl() {
        return url;
    }

    @Override
    protected OnJsBridge getOnJsBridge() {
        return new JsBridge(mAgentWeb.get());
    }


    class JsBridge extends OnJsBridge {
        JsBridge(AgentWeb mAgentWeb) {
            super(mAgentWeb);
        }

        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void backPage() {
            finish();
        }


        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void backCustomerDetail(String json) {
            finish();
        }

        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void openAgreenTotal(String json) {
            try {
                JSONObject jsonObject = new JSONObject(json);
                String num = jsonObject.getString("num");
                switch (num) {
                    case "1":
                        CommonUtils.gotoBaseWebActivity("https://m2.jiazhengye.cn/help/listDetail?number=6724111934745828");
                        break;
                    case "2":
                        CommonUtils.gotoBaseWebActivity("https://m2.jiazhengye.cn/help/listDetail?number=6724111955438709");
                        break;
                    case "3":
                        CommonUtils.gotoBaseWebActivity("https://m2.jiazhengye.cn/help/listDetail?number=6724111951244788");
                        break;
                    case "4":
                        CommonUtils.gotoBaseWebActivity("https://m2.jiazhengye.cn/help/listDetail?number=6724111955502564");
                        break;
                    case "5":
                        CommonUtils.gotoBaseWebActivity("https://m2.jiazhengye.cn/help/listDetail?number=6724111955518693");
                        break;
                    case "6":
                        CommonUtils.gotoBaseWebActivity("https://m2.jiazhengye.cn/help/listDetail?number=6724111951326572");
                        break;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }
}