package com.business_clean.ui.adapter.todo;

import android.graphics.Color;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.business_clean.R;
import com.business_clean.data.mode.todo.TodoItemList;
import com.business_clean.data.mode.todo.TodoSummaryList;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;

import org.jetbrains.annotations.NotNull;

public class TodoItemAdapter extends BaseQuickAdapter<TodoSummaryList, BaseViewHolder> {

    public TodoItemAdapter() {
        super(R.layout.item_todo_item);
    }

    @Override
    protected void convert(@NotNull BaseViewHolder holder, TodoSummaryList item) {
        holder.setText(R.id.tv_item_todo_item_title, item.getItem())
                .setText(R.id.tv_item_todo_item_value, item.getContent());
    }
}