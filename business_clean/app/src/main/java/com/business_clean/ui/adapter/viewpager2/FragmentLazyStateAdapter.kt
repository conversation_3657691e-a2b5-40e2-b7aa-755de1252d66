package com.business_clean.ui.adapter.viewpager2

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter


/**
 * viewpage2 才可以用这个
 * 优化版本，避免FragmentManager事务冲突
 */
class FragmentLazyStateAdapter(
    fragmentActivity: FragmentActivity,
    private val fragments: MutableList<Fragment>
) :
    FragmentStateAdapter(fragmentActivity) {

    override fun getItemCount() = fragments.size

    override fun createFragment(position: Int): Fragment {
        // 确保position在有效范围内
        return if (position >= 0 && position < fragments.size) {
            fragments[position]
        } else {
            // 如果position无效，返回第一个Fragment或创建一个空Fragment
            if (fragments.isNotEmpty()) {
                fragments[0]
            } else {
                Fragment() // 返回空Fragment作为fallback
            }
        }
    }

    /**
     * 获取Fragment的唯一ID，避免Fragment重复创建
     */
    override fun getItemId(position: Int): Long {
        return if (position >= 0 && position < fragments.size) {
            fragments[position].hashCode().toLong()
        } else {
            super.getItemId(position)
        }
    }

    /**
     * 判断Fragment是否包含特定的item
     */
    override fun containsItem(itemId: Long): Boolean {
        return fragments.any { it.hashCode().toLong() == itemId }
    }
}