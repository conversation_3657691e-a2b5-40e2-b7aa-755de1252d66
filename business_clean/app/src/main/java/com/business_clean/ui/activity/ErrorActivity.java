package com.business_clean.ui.activity;


import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseActivity;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.databinding.ActivityErrorBinding;
import com.business_clean.ui.activity.main.MainActivity;
import com.google.gson.Gson;

import org.jetbrains.annotations.Nullable;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cat.ereza.customactivityoncrash.CustomActivityOnCrash;
import cat.ereza.customactivityoncrash.config.CaocConfig;
import me.hgj.mvvmhelper.base.BaseViewModel;
import me.hgj.mvvmhelper.ext.AppExtKt;

public class ErrorActivity extends BaseActivity<BaseViewModel, ActivityErrorBinding> {

    CaocConfig config;//配置对象

    StringBuilder stringBuilder = new StringBuilder();

    @Override
    public boolean showToolBar() {
        return false;
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        config = CustomActivityOnCrash.getConfigFromIntent(getIntent());//获得配置信息,比如设置的程序崩溃显示的页面和重新启动显示的页面等等信息

        //将堆栈跟踪作为字符串获取。
        String stackString = CustomActivityOnCrash.getStackTraceFromIntent(getIntent());
        stringBuilder.append(stackString);
        //获取错误报告的Log信息
        String logString = CustomActivityOnCrash.getActivityLogFromIntent(getIntent());
        stringBuilder.append("获取错误报告的Log信息==>" + logString);
        // 获取所有的信息
        String allString = CustomActivityOnCrash.getAllErrorDetailsFromIntent(this, getIntent());
        stringBuilder.append("获取所有的信息==>" + allString);

//        LogUtils.e("将堆栈跟踪作为字符串获取==>" + stringBuilder);

        getLocalInfo(logString);
    }


    private void getLocalInfo(String stackString) {
        //设置当前的信息
        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append("版本号：V").append(AppExtKt.getAppVersion(this)).append("\n");
        contentBuilder.append("型号：").append(DeviceUtils.getManufacturer()).append(" - ").append(DeviceUtils.getModel()).append("\n");
        NetUrl.defaultUrl = MMKVHelper.getString(ConstantMMVK.ENVIRONMENT_BASE_URL, NetUrl.SERVER_URL_RELEASE);


        String userInfoJson = MMKVHelper.getString(ConstantMMVK.USER_INFO);
        if (!TextUtils.isEmpty(userInfoJson)) {
            UserInfo userInfo = new Gson().fromJson(userInfoJson, UserInfo.class);
            if (userInfo != null) {
                if (userInfo.getUser() != null) {
                    contentBuilder.append("用户：").append(userInfo.getUser().getUser_name()).append("\n");
                    contentBuilder.append("手机号：").append(userInfo.getUser().getMobile()).append("\n");
                    contentBuilder.append("角色：").append(checkRoleName(userInfo.getUser().getRole_id()));
                }
                if (userInfo.getCompany() != null) {
                    if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_RELEASE)) {
                    } else {
                        contentBuilder.append("\n").append("企业：").append(userInfo.getCompany().getCompany_name());
                    }
                }

            }
        }
//        contentBuilder.append("\n").append("页面：").append(filterErrorText(stackString));
        contentBuilder.append("\n").append("页面：").append(extractLastFiveLines(stackString));
        mDatabind.tvContent.setText(contentBuilder);
    }

    public String extractLastFiveLines(String logString) {
        // 将日志字符串按行分割成列表
        List<String> lines = Arrays.asList(logString.trim().split("\n"));

        // 如果行数大于等于5，则只保留最后5行；否则保留全部
        int fromIndex = Math.max(0, lines.size() - 5);
        List<String> lastFiveLines = lines.subList(fromIndex, lines.size());

        // 将提取的行重新组合成一个字符串并返回
        return String.join("\n", lastFiveLines);
    }

    //查询有Activity或者Fragment的字符串
    private String filterErrorText(String stackString) {
        return extractActivityOrFragmentClass(stackString);
    }

    public String extractActivityOrFragmentClass(String stackTrace) {
        // 正则表达式模式：匹配以大写字母开头的类名（如 WatermarkCameraActivity）
        Pattern pattern = Pattern.compile("(\\b[A-Z][a-zA-Z]*Activity|\\b[A-Z][a-zA-Z]*Fragment)\\b");
        Matcher matcher = pattern.matcher(stackTrace);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return ""; // 如果没有找到匹配项，则返回 null
    }

    private String checkRoleName(String roleId) {
        if ("-1".equals(roleId)) {
            return "超级管理员";
        } else if ("1".equals(roleId)) {
            return "管理员";
        } else if ("2".equals(roleId)) {
            return "项目负责人";
        } else if ("3".equals(roleId)) {
            return "人事";
        } else if ("4".equals(roleId)) {
            return "领班";
        } else if ("5".equals(roleId)) {
            return "保洁";
        } else if ("6".equals(roleId)) {
            return "大区经理";
        }
        return "未知";
    }

    @Override
    public void onBindViewClick() {
        mDatabind.butRestart.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CustomActivityOnCrash.restartApplication(ErrorActivity.this, config);
            }
        });
        mDatabind.butCopy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData clip = ClipData.newPlainText("Copied Text", stringBuilder.toString());
                clipboard.setPrimaryClip(clip);
                ToastUtil.show("复制成功");
            }
        });
    }
}