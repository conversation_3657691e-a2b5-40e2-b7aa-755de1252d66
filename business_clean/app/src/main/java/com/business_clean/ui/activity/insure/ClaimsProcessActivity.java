package com.business_clean.ui.activity.insure;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.base.BaseAgentWebActivity;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.OnJsBridge;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.ext.LoadingDialogExtKt;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.ChoosePicUtil;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.UploadFileHelper;
import com.business_clean.databinding.ActivityClaimsProcessBinding;
import com.business_clean.viewmodel.request.ContractViewModel;
import com.just.agentweb.AgentWeb;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.lxj.xpopup.interfaces.OnSelectListener;

import org.json.JSONObject;

import java.util.ArrayList;

import me.hgj.mvvmhelper.ext.LogExtKt;

public class ClaimsProcessActivity extends BaseAgentWebActivity<ContractViewModel, ActivityClaimsProcessBinding> {

    private String url;
    private String order_number;
    private String guarantee;
    private String[] strings = {"拍照", "相册"};


    @Override
    public void initView(Bundle savedInstanceState) {
        mToolbar.setTitle("理赔流程");
        setOnResumeReload(true);
        if (getIntent() != null && getIntent().getExtras() != null) {
            order_number = getIntent().getExtras().getString("order_number");
            guarantee = getIntent().getExtras().getString("guarantee");
            url = getIntent().getExtras().getString("url");
            if (TextUtils.isEmpty(url)) {
                if (TextUtils.isEmpty(order_number) || TextUtils.isEmpty(guarantee)) {
                    url = "/syqj/insurance/claimsrecords";//理赔记录
                } else {
                    url = "/syqj/insurance/appeal?order_number=" + order_number + "&guarantee=" + guarantee + "&claims_status=1";
                }
            }
        }
    }

    @Override
    public void onBindViewClick() {

    }

    @Override
    public void initObserver() {

    }

    @Override
    protected ViewGroup getAgentWebParent() {
        return mDatabind.flLayout;
    }

    @Override
    protected String getUrl() {
        return CommonUtils.getWebHostM2() + url;
    }


    @Override
    protected OnJsBridge getOnJsBridge() {
        return new JsBridge(mAgentWeb.get());
    }

    class JsBridge extends OnJsBridge {
        JsBridge(AgentWeb mAgentWeb) {
            super(mAgentWeb);
        }

        @JavascriptInterface
        public void insuranceBack(String json) {
            LogUtils.e("json --> " + json);
            finish();
        }

        @JavascriptInterface
        public void insuranceExample(String json) {
            LogUtils.e("json --> " + json);
            try {
                CommonUtils.gotoBaseWebActivity("https://m2.jiazhengye.cn/help/listDetail?number=6724120566054404");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @JavascriptInterface
        public void goAppeal(String json) {
            LogUtils.e("json --> " + json);
            try {
                JSONObject jsonObject = new JSONObject(json);
                String url = jsonObject.getString("url");
                Bundle bundle = new Bundle();
                bundle.putString("url", "" + url);
                ActivityForwardUtil.startActivity(ClaimsProcessDetailActivity.class, bundle);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @JavascriptInterface
        public void uploadMedia(String json) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    CommonUtils.showBottomListWith(ClaimsProcessActivity.this, 0, "", strings, new OnSelectListener() {
                        @Override
                        public void onSelect(int position, String text) {
                            switch (position) {
                                case 0:
                                    ChoosePicUtil.openCameraTake(ClaimsProcessActivity.this, new OnResultCallbackListener<LocalMedia>() {
                                        @Override
                                        public void onResult(ArrayList<LocalMedia> result) {
                                            if (!TextUtils.isEmpty(result.get(0).getCompressPath())) {
                                                uploadPic(result.get(0).getCompressPath());
                                            } else {
                                                uploadPic(result.get(0).getRealPath());
                                            }
                                        }

                                        @Override
                                        public void onCancel() {

                                        }
                                    });
                                    break;
                                case 1:
                                    ChoosePicUtil.openAlbum(ClaimsProcessActivity.this, 1, new OnResultCallbackListener<LocalMedia>() {
                                        @Override
                                        public void onResult(ArrayList<LocalMedia> result) {
                                            if (!TextUtils.isEmpty(result.get(0).getCompressPath())) {
                                                uploadPic(result.get(0).getCompressPath());
                                            } else {
                                                uploadPic(result.get(0).getRealPath());
                                            }
                                        }

                                        @Override
                                        public void onCancel() {

                                        }
                                    });
                                    break;
                            }
                        }
                    });
                }
            });
        }
    }


    /**
     * 上传图片
     */
    private void uploadPic(String path) {
        LoadingDialogExtKt.showLoadingExt(this, "上传中");
        UploadFileHelper.getInstance().uploadPictures(ClaimsProcessActivity.this, path, UploadFileHelper.PATH_HEADER_PERSONNEL, new UploadFileHelper.UploadListener() {
            @Override
            public void onUploadSuccess(String response) {
                LogExtKt.logE("最后回调到的图片---> " + response, "");
                if (mAgentWeb != null) {
                    mAgentWeb.get().getJsAccessEntrace().quickCallJs(Constant.JS_SPACE_NAME + "uploadMedia" + Constant.JS_SPACE_CALLBACK, response);
                }
                LoadingDialogExtKt.dismissLoadingExt(ClaimsProcessActivity.this);
            }

            @Override
            public void onUploadFailed(String error) {
                ToastUtil.show("上传失败");
                LoadingDialogExtKt.dismissLoadingExt(ClaimsProcessActivity.this);
            }

            @Override
            public void onUploadProgress(int progress) {

            }

        });
    }
}