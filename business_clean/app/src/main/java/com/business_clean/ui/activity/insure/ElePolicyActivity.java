package com.business_clean.ui.activity.insure;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import com.blankj.utilcode.util.LogUtils;
import com.business_clean.R;
import com.business_clean.app.base.BaseAgentWebActivity;
import com.business_clean.app.config.OnJsBridge;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.share.ShareHelperTools;
import com.business_clean.app.util.share.ShareParams;
import com.business_clean.app.util.share.ShareType;
import com.business_clean.databinding.ActivityElePolicyBinding;
import com.business_clean.ui.activity.BaseH5Activity;
import com.business_clean.ui.activity.custom.ContractPreviewActivity;
import com.business_clean.viewmodel.request.ContractViewModel;
import com.just.agentweb.AgentWeb;

import java.net.URLEncoder;

/**
 * 电子保单
 */
public class ElePolicyActivity extends BaseAgentWebActivity<ContractViewModel, ActivityElePolicyBinding> {

    private String url;
    private String userName;
    private String order_number;

    @Override
    public void initView(Bundle savedInstanceState) {
        mToolbar.setTitle("电子保单");
        mToolbar.setRightIcon(R.mipmap.icon_base_export);
        if (getIntent() != null && getIntent().getExtras() != null) {
            url = getIntent().getExtras().getString("url");
            order_number = getIntent().getExtras().getString("order_number");
            userName = getIntent().getExtras().getString("user_name");
        }
    }

    @Override
    public void onBindViewClick() {
        mToolbar.setOnImageRightTextClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShareParams params = new ShareParams();
                params.setShareType(ShareType.WEIXIN);
                if (!TextUtils.isEmpty(userName)) {
                    params.setTitle(userName + "的" + mToolbar.getCenterTitleView().getText().toString());
                } else {
                    params.setTitle("" + mToolbar.getCenterTitleView().getText().toString());
                }
                params.setLinkUrl(getUrl());
                ShareHelperTools.getInstance().shareCardLink(params, ElePolicyActivity.this);
            }
        });
    }

    @Override
    public void initObserver() {

    }

    @Override
    protected ViewGroup getAgentWebParent() {
        return mDatabind.flLayout;
    }

    @Override
    protected String getUrl() {
        return CommonUtils.getWebHostM2() + "/syqj/insurance/pdf?url=" + toURLEncoded(url + "&order_number=" + order_number);
    }

    public static String toURLEncoded(String paramString) {
        if (paramString == null || paramString.equals("")) {
            return "";
        }
        try {
            String str = new String(paramString.getBytes(), "UTF-8");
            str = URLEncoder.encode(str, "UTF-8");
            return str;
        } catch (Exception localException) {
        }
        return "";
    }


    @Override
    protected OnJsBridge getOnJsBridge() {
        return new JsBridge(mAgentWeb.get());
    }


    class JsBridge extends OnJsBridge {
        JsBridge(AgentWeb mAgentWeb) {
            super(mAgentWeb);
        }

        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void insuranceBack(String json) {
            ElePolicyActivity.this.finish();
        }
    }
}