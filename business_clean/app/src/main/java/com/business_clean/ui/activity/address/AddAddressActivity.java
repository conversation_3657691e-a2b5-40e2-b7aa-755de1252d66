package com.business_clean.ui.activity.address;


import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.graphics.BitmapFactory;
import android.location.Location;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.LinearLayout;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.maps.AMap;
import com.amap.api.maps.AMapOptions;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.MapsInitializer;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItem;
import com.amap.api.services.core.PoiItemV2;
import com.amap.api.services.poisearch.PoiResult;
import com.amap.api.services.poisearch.PoiResultV2;
import com.amap.api.services.poisearch.PoiSearch;
import com.amap.api.services.poisearch.PoiSearchV2;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseActivity;
import com.business_clean.app.callback.OnDialogCityIdPickerListener;
import com.business_clean.app.callback.OnDialogCityPickerListener;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.service.LocService;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.weight.dialog.CustomCityPickerPopup;
import com.business_clean.data.mode.address.CustomPoiEntity;
import com.business_clean.data.mode.address.RegeoEntity;
import com.business_clean.databinding.ActivityAddAddressBinding;
import com.business_clean.ui.activity.camera.WatermarkCameraActivity;
import com.business_clean.ui.adapter.AddressChooseAdapter;
import com.business_clean.viewmodel.request.AddressViewModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.idlefish.flutterboost.FlutterBoost;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import me.hgj.mvvmhelper.ext.LogExtKt;

public class AddAddressActivity extends BaseActivity<AddressViewModel, ActivityAddAddressBinding> {


    private AddressChooseAdapter mAdapter;

    private String project_uuid;

    private String mCurrentCity = "";

    private String homeTown;

    private MapView mMapView = null;
    //初始化地图控制器对象
    private AMap aMap;
    //当前的定位成功回来的对象
    private Location mCurrentLocation;
    ///定位的控制器

    //声明AMapLocationClientOption对象
    public AMapLocationClientOption mLocationOption = null;
    //声明AMapLocationClient类对象
    public AMapLocationClient mLocationClient = null;
    //搜索
    public PoiSearch.Query query = null;
    public PoiSearch poiSearch = null;
    //当前地点的经纬度
    public LatLng mLatLng;


    @Override
    protected void onCreate(@androidx.annotation.Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        MapsInitializer.updatePrivacyShow(this, true, true);
        MapsInitializer.updatePrivacyAgree(this, true);

        mMapView = mDatabind.map;
        mMapView.onCreate(savedInstanceState);
        aMap = mMapView.getMap();
        LogUtils.e("当前设备--" + AMapLocationClient.getDeviceId(this));
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        mToolbar.setTitle("添加打卡地点");
        mAdapter = new AddressChooseAdapter();
        mDatabind.recycler.setAdapter(mAdapter);
        mDatabind.recycler.setLayoutManager(new LinearLayoutManager(this));

        if (getIntent() != null && getIntent().getExtras() != null) {
            project_uuid = getIntent().getExtras().getString("project_uuid");
        }

        aMap.getUiSettings().setMyLocationButtonEnabled(false);//设置默认定位按钮是否显示，非必需设置。
        aMap.getUiSettings().setZoomControlsEnabled(false);//缩放按钮
        aMap.getUiSettings().setCompassEnabled(false);//指南针
        aMap.setMyLocationEnabled(false);// 设置为true表示启动显示定位蓝点，false表示隐藏定位蓝点并不进行定位，默认是false。
        aMap.getUiSettings().setScaleControlsEnabled(false);//控制比例尺控件是否显示
        aMap.getUiSettings().setLogoPosition(AMapOptions.LOGO_POSITION_BOTTOM_RIGHT);//地图Logo显示位置
        //缩放的级别
        aMap.moveCamera(CameraUpdateFactory.zoomTo(18));

        //拖拽监听
        aMap.setOnCameraChangeListener(new AMap.OnCameraChangeListener() {
            @Override
            public void onCameraChange(CameraPosition cameraPosition) {
            }

            @Override
            public void onCameraChangeFinish(CameraPosition cameraPosition) {
                LogUtils.e("onCameraChangeFinish " + cameraPosition);
                ///拖拽停止
                mLatLng = cameraPosition.target;
                //来从接口获取这个具体位置的信息
                mViewModel.getRegeo(mLatLng.latitude, mLatLng.longitude);
            }
        });
        //开始定位
        startLocation();
    }


    @Override
    public void onBindViewClick() {

        mToolbar.setOnImageRightTextClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtils.showDialogCityPickerView(AddAddressActivity.this, "请选择城市", CustomCityPickerPopup.Mode.PC, homeTown, mCurrentCity, "", new OnDialogCityPickerListener() {
                    @Override
                    public void onCityConfirm(String province, String city0, String area, int options1, int options2, int options3, View v) {
                        homeTown = province;
                        mCurrentCity = city0;
                        mToolbar.setSubTitle(homeTown + "/" + mCurrentCity);
                    }

                    @Override
                    public void onCityChange(String province, String city, String area) {

                    }
                }, new OnDialogCityIdPickerListener() {
                    @Override
                    public void onCityConfirm(String provinceId, String cityId0, String areaId) {
                    }
                });
            }
        });

        //回到当前的位置
        mDatabind.ivNowLocation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startMoveCurrentLocation();
            }
        });
        mDatabind.mask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                switchSearchStatus(false);
                //回归到当前的定位
                startMoveCurrentLocation();
            }
        });
    }

    /**
     * 根据城市以及当前的经纬度搜索周边Poi
     */
    private void initPoiBoundSearch() {
        try {
            query = new PoiSearch.Query(mDatabind.etSearchAddress.getText().toString(), "", mCurrentCity);
            //keyWord表示搜索字符串，
            //第二个参数表示POI搜索类型，二者选填其一，选用POI搜索类型时建议填写类型代码，码表可以参考下方（而非文字）
            //cityCode表示POI搜索区域，可以是城市编码也可以是城市名称，也可以传空字符串，空字符串代表全国在全国范围内进行搜索

            query.setPageSize(10);// 设置每页最多返回多少条poiitem
            query.setPageNum(1);//设置查询页码
            poiSearch = new PoiSearch(this, query);
            poiSearch.setOnPoiSearchListener(new PoiSearch.OnPoiSearchListener() {

                @Override
                public void onPoiSearched(PoiResult poiResult, int code) {
                    LogUtils.e("onPoiSearched 查询的内容-- " + poiResult.getPois().size());
                    setPoiList(poiResult.getPois());
                }

                @Override
                public void onPoiItemSearched(PoiItem poiItem, int i) {
                }
            });

            if (mLatLng != null && TextUtils.isEmpty(mDatabind.etSearchAddress.getText().toString())) {
                //搜索指定半径
                poiSearch.setBound(new PoiSearch.SearchBound(new LatLonPoint(mLatLng.latitude, mLatLng.longitude), 1000));//设置周边搜索的中心点以及半径
            }

            poiSearch.searchPOIAsyn();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setPoiList(ArrayList<PoiItem> pois) {
        List<CustomPoiEntity> lists = new ArrayList<>();
        for (int i = 0; i < pois.size(); i++) {
            PoiItem poiItem = pois.get(i);
            LogUtils.e("setPoiList getProvinceName" + poiItem.getProvinceName());
            LogUtils.e("setPoiList getCityName " + poiItem.getCityName());
            LogUtils.e("setPoiList getAdName " + poiItem.getAdName());
            LogUtils.e("setPoiList getTitle " + poiItem.getTitle());
            LogUtils.e("setPoiList getSnippet " + poiItem.getSnippet());
            LogUtils.e("setPoiList getTypeDes " + poiItem.getTypeDes());
            lists.add(new CustomPoiEntity(poiItem.getDistance(), poiItem.getPoiId(), poiItem.getTitle(), poiItem.getAdCode(), poiItem.getSnippet(),
                    poiItem.getLatLonPoint().getLatitude(), poiItem.getLatLonPoint().getLongitude()));
        }
        mAdapter.setList(lists);
    }

    /**
     * 开始定位
     */
    private void startLocation() {
        try {
            //初始化定位
            mLocationClient = new AMapLocationClient(this);
            //初始化AMapLocationClientOption对象
            mLocationOption = LocService.getInstance(this).getDefaultLocationClientOption();
            mLocationClient.setLocationOption(mLocationOption);
            //设置定位回调监听
            mLocationClient.setLocationListener(mLocationListener);
            //设置场景模式后最好调用一次stop，再调用start以保证场景模式生效
            mLocationClient.stopLocation();
            mLocationClient.startLocation();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    //声明定位回调监听器
    public AMapLocationListener mLocationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(AMapLocation aMapLocation) {
            LogUtils.e("高德的经纬度 onLocationChanged --- " + aMapLocation.getLatitude() + " ; " + aMapLocation.getLongitude());
            LogUtils.e("高德的经纬度 onLocationChanged --- " + aMapLocation.getLocationDetail());
            mCurrentLocation = aMapLocation;
            //定位到内容了，停止定位
            if (mCurrentLocation.getLatitude() > 0.0 && mCurrentLocation.getLongitude() > 0.0) {
                LogUtils.e("获取到定位了当前的城市 -- " + aMapLocation.getCity());
                LogUtils.e("获取到定位了当前的城市 -- " + aMapLocation.getAdCode());
                LogUtils.e("获取到定位了当前的城市 -- " + aMapLocation.getAoiName());
                LogUtils.e("获取到定位了当前的城市 -- " + aMapLocation.getAddress());
                LogUtils.e("获取到定位了当前的城市 -- " + aMapLocation.getDescription());
                LogUtils.e("获取到定位了当前的城市 -- " + aMapLocation.getPoiName());
                LogUtils.e("获取到定位了当前的城市 -- " + aMapLocation.getProvince());
                mCurrentCity = aMapLocation.getCity();
                mToolbar.setSubTitle(aMapLocation.getCity() + "/" + aMapLocation.getProvince());
                startMoveCurrentLocation();
                stopLocation();
            }
        }
    };

    /**
     * 停止定位
     *
     * @return
     */
    private void stopLocation() {
        try {
            // 停止定位
            mLocationClient.stopLocation();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 移动到当前的位置
     */
    private void startMoveCurrentLocation() {
        if (mCurrentLocation != null) {
            aMap.animateCamera(CameraUpdateFactory.changeLatLng(new LatLng(mCurrentLocation.getLatitude(), mCurrentLocation.getLongitude())));
            ToastUtil.show("已移动到当前位置");
        }
    }

    @Override
    public void onRequestSuccess() {
        mViewModel.getRegeo().observe(this, new Observer<RegeoEntity>() {
            @Override
            public void onChanged(RegeoEntity regeoEntity) {
                LogUtils.e("onRequestSuccess 获取到的地址信息 -- " + regeoEntity.getLocation());

                String city = !TextUtils.isEmpty(regeoEntity.getCity()) ? regeoEntity.getCity() : regeoEntity.getProvince();
                mCurrentCity = !TextUtils.isEmpty(city) ? city : mCurrentCity; // 保持原有值

                StringBuilder builder = new StringBuilder();
                if (!TextUtils.isEmpty(regeoEntity.getProvince())) {
                    builder.append(regeoEntity.getProvince()).append("/");
                }
                if (!TextUtils.isEmpty(regeoEntity.getCity())) {
                    builder.append(regeoEntity.getCity());
                } else {
                    if (!TextUtils.isEmpty(regeoEntity.getDistrict())) {
                        builder.append(regeoEntity.getDistrict());
                    }
                }

                mToolbar.setSubTitle(builder.toString());
                initPoiBoundSearch();
            }
        });

        mViewModel.getSaveAddress().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                FlutterBoost.instance().sendEventToFlutter("refresh", null);//刷新当前界面
                App.getAppViewModelInstance().getRefreshAddressManagerList().setValue(true);
                finish();
            }
        });
    }

    @Override
    public void initObserver() {
        mAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull @NotNull BaseQuickAdapter<?, ?> adapter, @NonNull @NotNull View view, int position) {
                HashMap<String, String> hashMap = new HashMap<>();
                if (TextUtils.isEmpty(project_uuid)) {
                    hashMap.put("project_uuid", "" + App.getAppViewModelInstance().getUserInfo().getValue().getProject().getUuid());
                } else {
                    hashMap.put("project_uuid", project_uuid);
                }
                hashMap.put("address", "" + mAdapter.getData().get(position).getName());
                hashMap.put("address_desc", "" + mAdapter.getData().get(position).getAddr());
                hashMap.put("lnt", "" + mAdapter.getData().get(position).getLnt());
                hashMap.put("lat", "" + mAdapter.getData().get(position).getLat());
                hashMap.put("map_code", "gao_de");
                mViewModel.requestSaveAddress(hashMap);
            }
        });


        mDatabind.etSearchAddress.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 0) {
                    LogExtKt.logE("来查询了：" + s, "");
                    initPoiBoundSearch();
                }
            }
        });

        mDatabind.flSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                switchSearchStatus(true);
            }
        });

        //取消搜索
        mDatabind.tvSearchCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                switchSearchStatus(false);
                mDatabind.etSearchAddress.setText("");
                ///重新搜索当前位置
                initPoiBoundSearch();
            }
        });


        //监听下，是否获取焦点改变布局
        mDatabind.etSearchAddress.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) mDatabind.flMapLayout.getLayoutParams();
                if (hasFocus) {
                    params.weight = 0.3F; // 设置新的高度
                } else {
                    params.weight = 1f; // 恢复原始高度
                }
                mDatabind.flMapLayout.setLayoutParams(params);
            }
        });

    }


    private void switchSearchStatus(boolean show) {
        mDatabind.mask.setVisibility(show ? View.VISIBLE : View.GONE);
        mDatabind.llSearchEt.setVisibility(show ? View.VISIBLE : View.GONE);
        mDatabind.tvSearchTitle.setVisibility(show ? View.GONE : View.VISIBLE);
        if (show) {
            KeyboardUtils.showSoftInput(mDatabind.etSearchAddress);
        } else {
            KeyboardUtils.hideSoftInput(mDatabind.etSearchAddress);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        mMapView.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mMapView.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mMapView.onDestroy();
        if (null != mLocationClient) {
            mLocationClient.onDestroy();
            mLocationClient = null;
            mLocationOption = null;
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        //在activity执行onSaveInstanceState时执行mMapView.onSaveInstanceState (outState)，保存地图当前的状态
        mMapView.onSaveInstanceState(outState);
    }

}