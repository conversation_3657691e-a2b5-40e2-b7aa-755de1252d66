package com.business_clean.ui.fragment.todo;

import static com.business_clean.app.config.Constant.TYPE_INIT_DATA;
import static com.business_clean.app.config.Constant.TYPE_INIT_LOAD_MORE;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseFragment;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.app.util.CheckListDataUtil;
import com.business_clean.data.mode.todo.PlanEntity;
import com.business_clean.databinding.FragmentTodoPlanWordChildBinding;
import com.business_clean.ui.adapter.todo.TodoPlanAdapter;
import com.business_clean.viewmodel.request.TodoViewModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemChildClickListener;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 工单详情
 */
public class TodoPlanWorkChildFragment extends BaseFragment<TodoViewModel, FragmentTodoPlanWordChildBinding> {

    private int requestType;

    private int page = 1;

    private int status = 3;

    private TodoPlanAdapter mAdapter = new TodoPlanAdapter();


    public TodoPlanWorkChildFragment() {
    }


    public TodoPlanWorkChildFragment(int status) {
        this.status = status;
    }


    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        getMDatabind().list.recyclerView.setLayoutManager(new LinearLayoutManager(getMActivity()));
        getMDatabind().list.recyclerView.setAdapter(mAdapter);
        mAdapter.setType(20);
        getMDatabind().list.refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull @NotNull RefreshLayout refreshLayout) {
                requestMore();
            }

            @Override
            public void onRefresh(@NonNull @NotNull RefreshLayout refreshLayout) {
                requestOne();
            }
        });
    }

    @Override
    public void onBindViewClick() {
        //去拍照
        mAdapter.setOnItemChildClickListener(new OnItemChildClickListener() {
            @Override
            public void onItemChildClick(@NonNull BaseQuickAdapter adapter, @NonNull View view, int position) {
                if (view.getId() == R.id.tv_item_take_photo) {//拍照
                    CommonToFlutter.gotoFlutterTaskOnePage(mAdapter.getData().get(position).getUuid(), "20", true);
                } else {
                    gotoPlanDetail(mAdapter.getData().get(position).getUuid());
                }
            }
        });


        mAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                gotoPlanDetail(mAdapter.getData().get(position).getUuid());
            }
        });
    }


    @Override
    public void initObserver() {
    }

    @Override
    public void lazyLoadData() {
    }

    @Override
    public void onRequestSuccess() {
        mViewModel.getPlanEntity().observe(this, new Observer<PlanEntity>() {
            @Override
            public void onChanged(PlanEntity planEntity) {
                App.getAppViewModelInstance().getRefreshTodoChild().setValue(planEntity);
                switch (requestType) {
                    case TYPE_INIT_DATA:
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkInitData(planEntity.getList(), mAdapter, getMDatabind().list.refreshLayout)) {
                            mAdapter.setList(planEntity.getList());
                        }
                        break;
                    case TYPE_INIT_LOAD_MORE:
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkLoadMoreData(planEntity.getList(), mAdapter, getMDatabind().list.refreshLayout)) {
                            mAdapter.addData(planEntity.getList());
                        }
                        break;
                }
            }
        });
    }


    @Override
    public void onResume() {
        super.onResume();
        requestOne();
    }

    private void requestOne() {
        requestType = TYPE_INIT_DATA;
        page = 1;
        mViewModel.requestPlanList(page, status, 20);
        mViewModel.requestTodoTotal();
    }

    private void requestMore() {
        requestType = TYPE_INIT_LOAD_MORE;
        page++;
        mViewModel.requestPlanList(page, status, 20);
    }


    private void gotoPlanDetail(String uuid) {
        CommonToFlutter.gotoFlutterTaskOnePage(uuid, "20", false);
    }

}
