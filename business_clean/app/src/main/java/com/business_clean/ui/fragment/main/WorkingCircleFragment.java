package com.business_clean.ui.fragment.main;

import static com.business_clean.app.config.Constant.TYPE_INIT_DATA;
import static com.business_clean.app.config.Constant.TYPE_INIT_LOAD_MORE;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseFragment;
import com.business_clean.app.config.Constant;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.CheckListDataUtil;
import com.business_clean.app.util.DividerItemDecoration;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.weight.SmoothScrollLayoutManager;
import com.business_clean.app.weight.dialog.CustomImageViewPagerPopup;
import com.business_clean.app.weight.dialog.PagerDrawerPopup;
import com.business_clean.data.dao.WaterPhotoData;
import com.business_clean.data.dao.WatermarkPhotoDatabaseManager;
import com.business_clean.data.mode.ImageEntity;
import com.business_clean.data.mode.circle.ChatMessage;
import com.business_clean.data.mode.circle.ChatMessageCompact;
import com.business_clean.data.mode.circle.ChatMessageCompactLIst;
import com.business_clean.data.mode.circle.ChatMessageLIst;
import com.business_clean.data.mode.circle.ChatStatEntity;
import com.business_clean.data.mode.circle.CircleTagEntity;
import com.business_clean.data.mode.circle.StatEntity;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.business_clean.databinding.FragmentWorkCircleBinding;
import com.business_clean.ui.activity.clean.CleanReportActivity;
import com.business_clean.ui.activity.me.MyPhotosActivity;
import com.business_clean.ui.adapter.circle.ChatAdapter;
import com.business_clean.ui.adapter.circle.ChatCompactAdapter;
import com.business_clean.ui.adapter.circle.CircleTagAdapter;
import com.business_clean.viewmodel.request.CircleViewModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemChildClickListener;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.gyf.immersionbar.ImmersionBar;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.luck.picture.lib.decoration.MyGridSpacingItemDecoration;
import com.lxj.xpopup.XPopup;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import me.hgj.mvvmhelper.ext.LogExtKt;

public class WorkingCircleFragment extends BaseFragment<CircleViewModel, FragmentWorkCircleBinding> {

    private ChatCompactAdapter mCompactAdapter; //紧凑型布局

    private int requestType;

    private int page;

    private String latest_record_uuid = "";

    private int layout_type = 2;//列表的布局方式 1是常规布局，2是紧凑布局  默认值是紧凑布局

    private boolean isLazyLoad = false; //是否已经懒加载了

    private CircleTagAdapter stringAdapter = new CircleTagAdapter();


    private String act_type;
    private SmoothScrollLayoutManager layoutManager;

    private boolean isExpanded;//记录是否展开

    private String project_uuid = "";
    private String project_name = "全部项目";

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        ImmersionBar.with(this).titleBar(getMDatabind().llTop).statusBarColor(R.color.base_primary).navigationBarColor(R.color.white).statusBarDarkFont(true).init();

        mCompactAdapter = new ChatCompactAdapter();
        layoutManager = new SmoothScrollLayoutManager(getMActivity());
        getMDatabind().recycler.addItemDecoration(new DividerItemDecoration(getMActivity(), SizeUtils.dp2px(0f), SizeUtils.dp2px(0f)));

        getMDatabind().recycler.setLayoutManager(layoutManager);
        getMDatabind().recycler.setAdapter(mCompactAdapter);

        //设置当前日期 ，如果数据有变动的话，肯定也需要进行更换
        getMDatabind().tvCircleDate.setText(TimeUtils.millis2String(TimeUtils.getNowMills(), "yyyy\nMM/dd"));

        //针对权限做处理 只有管理员跟人事可以切
        if (Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER || Constant.ROLE_HR || Constant.ROLE_REGIONAL_MANAGER) {
            getMDatabind().tvChange.setVisibility(View.VISIBLE);
        } else {
            getMDatabind().tvChange.setVisibility(View.GONE);
        }


        //针对保洁员 隐藏某些按钮 如果是保洁员，那么隐藏按钮
        if (Constant.ROLE_ID.equals(Constant.ROLE_CLEAN_ID)) {
            getMDatabind().flDeport.setVisibility(View.GONE);
        } else {
            getMDatabind().flDeport.setVisibility(View.VISIBLE);
        }

        //刷新的设置
        getMDatabind().refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                requestLoad();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                requestData();
            }
        });

        getMDatabind().recyclerTag.setLayoutManager(new GridLayoutManager(getMActivity(), 5));
        getMDatabind().recyclerTag.addItemDecoration(new MyGridSpacingItemDecoration(5, SizeUtils.dp2px(4), false));
        getMDatabind().recyclerTag.setAdapter(stringAdapter);

        getTagList(null);

    }

    private void getTagList(ChatStatEntity chatStatEntity) {
        List<CircleTagEntity> tags = new ArrayList<>();
        if (chatStatEntity == null) {
            tags.add(new CircleTagEntity("0", "清洁"));
            tags.add(new CircleTagEntity("0", "培训"));
            tags.add(new CircleTagEntity("0", "工单"));
            tags.add(new CircleTagEntity("0", "打卡"));
            tags.add(new CircleTagEntity("0", "其他"));
        } else {
            tags.add(new CircleTagEntity("" + chatStatEntity.getCleaning_total(), "清洁"));
            tags.add(new CircleTagEntity("" + chatStatEntity.getTraining_total(), "培训"));
            tags.add(new CircleTagEntity("" + chatStatEntity.getWord_order_total(), "工单"));
            tags.add(new CircleTagEntity("" + chatStatEntity.getClock_in_total(), "打卡"));
            tags.add(new CircleTagEntity("" + chatStatEntity.getOther_total(), "其他"));
        }
        stringAdapter.setList(tags);
    }

    @Override
    public void lazyLoadData() {
        fillTopData();
        requestData();
        isLazyLoad = true;//记录是否已经懒加载了

    }


    @Override
    public void initObserver() {
        //监听刷新列表
        App.getAppViewModelInstance().getRefreshCircle().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                requestData();
            }
        });

        //在这里监听 数据库的变化 来更新View
        App.getAppViewModelInstance().getRefreshUploadPhoto().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                //先从本地数据库中获取内容
                List<WaterPhotoData> dataList = WatermarkPhotoDatabaseManager.getInstance(getMActivity()).queryWaterPhoto();
                LogUtils.e("拍照本地数据库 ---> " + dataList.size());
                LogUtils.e("拍照本地数据库 ---> " + dataList);
            }
        });

        //切换项目的同时，去更新朋友圈
        App.getAppViewModelInstance().getProjectInfo().observe(this, new Observer<ProjectMangerList>() {
            @Override
            public void onChanged(ProjectMangerList projectMangerList) {
                fillTopData();
                if (mCompactAdapter != null && mCompactAdapter.getData().size() > 0) {//清空
                    mCompactAdapter.getData().clear();
                    mCompactAdapter.notifyDataSetChanged();
                }
                requestData();
            }
        });

        //监听recyclerView 滑动的
        getMDatabind().recycler.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();
                // 判断是否超过1屏的距离
                if (firstVisibleItemPosition > 2) {
                    getMDatabind().floating.setVisibility(View.VISIBLE);
                } else {
                    getMDatabind().floating.setVisibility(View.GONE);
                }
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    // 当 RecyclerView 停止滑动时 获取当前可见 item 中的最后一位
                    LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();

                    if (layout_type == 2 && mCompactAdapter.getData().size() == 0) {
                        return;
                    }
                    // 判断第一条item是否可见，如果不可见则显示回顶部按钮
                    if (recyclerView.getLayoutManager().findViewByPosition(0) != null && getMDatabind().floating.getVisibility() == View.GONE && isExpanded) {
                        //平滑的展开Apptab
                        getMDatabind().appBar.setExpanded(true, true);
                        isExpanded = false;
                    }


                    int firstVisiblePosition = layoutManager.findFirstVisibleItemPosition();


                    ChatMessageCompact message = mCompactAdapter.getItem(firstVisiblePosition);
                    if (message != null) {
                        LogExtKt.logE("当前可见的 item --->" + message.getUser_name(), "");
                        LogExtKt.logE("当前可见的 item --->" + message.getCreate_time(), "");
                        if (!TextUtils.isEmpty(message.getCreate_time())) {

                            String time = message.getCreate_time().substring(0, message.getCreate_time().indexOf(" "));

                            LogExtKt.logE("当前可见的 item time  --->" + time.replaceFirst("-", "\n").replaceFirst("-", "/"), "");
                            if (getMDatabind() != null && getMDatabind().tvCircleDate != null) {
                                getMDatabind().tvCircleDate.setText(time.replaceFirst("-", "\n").replaceFirst("-", "/"));
                            }
                            requestWorkingStat();
                        }
                    } else {
                        // 处理中间位置无效的情况
                        LogExtKt.logE("中间位置无效", "");
                    }
                }
            }
        });

    }

    private void fillTopData() {
        //如果当前角色 是 保洁、项目负责人、领班 进入首页，顶部显示自己当前负责的项目
        if (Constant.ROLE_CLEANER || Constant.ROLE_LEADER || Constant.ROLE_PROJECT_OWNER) {
            UserInfo userInfo = App.getAppViewModelInstance().getUserInfo().getValue();
            if (userInfo != null && userInfo.getProject() != null) {
                project_uuid = userInfo.getProject().getUuid();
                getMDatabind().llChangeProject.setVisibility(View.VISIBLE);
                updateChangeName(userInfo.getProject().getProject_short_name());
            }
        } else {
            if (App.getAppViewModelInstance().getProjectInfo().getValue() != null) {
                ProjectMangerList mangerList = App.getAppViewModelInstance().getProjectInfo().getValue();
                if (mangerList != null) {
                    LogUtils.e("本地的UserInfo " + mangerList.toString());
                    getMDatabind().llChangeProject.setVisibility(View.VISIBLE);
                    project_uuid = mangerList.getUuid();
                    if (!TextUtils.isEmpty(mangerList.getProject_short_name())) {
                        project_name = (mangerList.getProject_short_name());
                    } else {
                        project_name = "全部项目";
                    }
                    updateChangeName(project_name);
                }
            }
        }
    }

    private void updateChangeName(String name) {
        if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_RELEASE)) {
            getMDatabind().tvProjectName.setText(name);
        } else if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_TEST)) {
            getMDatabind().tvProjectName.setText(name + "(测试)");
        } else {
            getMDatabind().tvProjectName.setText(name + "(开发)");
        }
    }

    @Override
    public void onRequestSuccess() {
        //这里是统计
        getMViewModel().getStat().observe(this, new Observer<StatEntity>() {
            @Override
            public void onChanged(StatEntity statEntity) {
                if (statEntity.getAttendance() != null) {
                    getMDatabind().tvStatInWorkNum.setText(statEntity.getAttendance().getAll_total());//全部
                    getMDatabind().tvStatAttendanceNum.setText(statEntity.getAttendance().getActual_attendance_total());//出勤
                    getMDatabind().tvStatAbnormalNum.setText(statEntity.getAttendance().getAbnormal_total());//异常
                    getMDatabind().tvStatWorkTimeNum.setText(statEntity.getAttendance().getOvertimes_total());//加班人数
                }
                //下方进度条
                if (statEntity.getWork() != null && !TextUtils.isEmpty(statEntity.getWork().getTotal()) && !TextUtils.isEmpty(statEntity.getWork().getFinished_total())) {
                    int total = Integer.parseInt(statEntity.getWork().getTotal());
                    int finishTotal = Integer.parseInt(statEntity.getWork().getFinished_total());

                    getMDatabind().tvStatWorkProgress.setText("共" + total + "个任务 | 完成" + finishTotal + "个");
                    getMDatabind().progress.setMax(total);
                    getMDatabind().progress.setProgress(finishTotal);
                }
            }
        });

        getMViewModel().getChatMessageListCompact().observe(this, new Observer<ChatMessageCompactLIst>() {
            @Override
            public void onChanged(ChatMessageCompactLIst chatMessageLIst) {

                switch (requestType) {
                    case TYPE_INIT_DATA:
                        if (chatMessageLIst.getList().size() == 0) {
                            getMDatabind().llEmpty.setVisibility(View.VISIBLE);
                        } else {
                            getMDatabind().llEmpty.setVisibility(View.GONE);
                        }
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkInitData(chatMessageLIst.getList(), mCompactAdapter, getMDatabind().refreshLayout)) {
                            mCompactAdapter.setList(chatMessageLIst.getList());
                            getMDatabind().refreshLayout.setEnableLoadMore(true);
                        }
                        /// 新增的时候干这个事
                        if (mCompactAdapter.getData().size() > 0) {
                            ChatMessageCompact message = mCompactAdapter.getItem(0);
                            if (message != null) {
                                LogExtKt.logE("当前可见的 item --->" + message.getUser_name(), "");
                                LogExtKt.logE("当前可见的 item --->" + message.getCreate_time(), "");
                                if (!TextUtils.isEmpty(message.getCreate_time())) {

                                    String time = message.getCreate_time().substring(0, message.getCreate_time().indexOf(" "));

                                    LogExtKt.logE("当前可见的 item time  --->" + time.replaceFirst("-", "\n").replaceFirst("-", "/"), "");
                                    if (getMDatabind() != null && getMDatabind().tvCircleDate != null) {
                                        getMDatabind().tvCircleDate.setText(time.replaceFirst("-", "\n").replaceFirst("-", "/"));
                                    }
                                    requestWorkingStat();
                                }
                            } else {
                                // 处理中间位置无效的情况
                                LogExtKt.logE("中间位置无效", "");
                            }
                        }
                        break;
                    case TYPE_INIT_LOAD_MORE:
                        getMDatabind().llEmpty.setVisibility(View.GONE);

                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkLoadMoreData(chatMessageLIst.getList(), mCompactAdapter, getMDatabind().refreshLayout)) {
                            mCompactAdapter.addData(chatMessageLIst.getList());
                        }
                        break;
                }


                if (mCompactAdapter.getData().size() == 0) {
                    //如果没数据，那么就当前天
                    getMDatabind().tvCircleDate.setText(TimeUtils.millis2String(TimeUtils.getNowMills(), "yyyy\nMM/dd"));
                    requestWorkingStat();
                }
                latest_record_uuid = chatMessageLIst.getLatest_record_uuid();
            }
        });

        getMViewModel().getChatStat().observe(this, new Observer<ChatStatEntity>() {
            @Override
            public void onChanged(ChatStatEntity chatStatEntity) {
                getTagList(chatStatEntity);
            }
        });
    }


    @Override
    public void onBindViewClick() {
        //监听
        getMDatabind().floating.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isExpanded = true;
                getMDatabind().recycler.stopScroll();
                getMDatabind().recycler.smoothScrollToPosition(0);
            }
        });


        //查看项目任务清单
        getMDatabind().llWorkCircleProgress.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_CLEANER) {
                    ToastUtil.show("暂无操作权限");
                    return;
                }
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("project_uuid", "" + project_uuid);
                hashMap.put("project_name", "" + project_name);
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("workPlanOrderListPage")
                        .arguments(hashMap)
                        .build());
            }
        });

        //考勤目标
        getMDatabind().llAttendant.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_CLEANER) {
                    ToastUtil.show("暂无操作权限");
                    return;
                }
                gotoAttendance();
            }
        });

        //项目管理
        getMDatabind().tvProjectManager.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                ActivityForwardUtil.startActivity(ProjectManagerActivity.class);
            }
        });

        //入职
        getMDatabind().llEmployment.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonToFlutter.gotoFlutterAddStaffPage("", "", 0);
            }
        });
        //离职
        getMDatabind().flDeport.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("StaffResignMainListPage")
                        .arguments(hashMap)
                        .build());
            }
        });

        //清洁报告
        getMDatabind().flCleanReport.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Bundle bundle = new Bundle();
                bundle.putString("stat_month", "" + TimeUtils.millis2String(TimeUtils.getNowMills(), "yyyy-MM"));
                ActivityForwardUtil.startActivity(CleanReportActivity.class, bundle);
            }
        });

        //拼图
        getMDatabind().flProjectDetail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                if (App.getAppViewModelInstance() != null && App.getAppViewModelInstance().getUserInfo().getValue() != null && App.getAppViewModelInstance().getUserInfo().getValue().getUser() != null) {
                    hashMap.put("reporter_name", "" + App.getAppViewModelInstance().getUserInfo().getValue().getUser().getUser_name());
                }
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("jigsawPuzzlePage")
                        .arguments(hashMap)
                        .build());
            }
        });

        stringAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                stringAdapter.updateItem(position);
                //操作类型 1打卡 2领班打卡 3日常工作 4所有打卡 11清洁计划 12巡检计划 13培训计划 20工单
                if (stringAdapter.getPosition() != -1) {
                    switch (position) {
                        case 0:
                            act_type = "11";
                            break;
                        case 1:
                            act_type = "13";
                            break;
                        case 2:
                            act_type = "20";
                            break;
                        case 3:
                            act_type = "4";
                            break;
                        case 4:
                            act_type = "99";
                            break;
                    }
                } else {
                    act_type = "";
                }

                lazyLoadData();
            }
        });

        //切换项目
        getMDatabind().llChangeProject.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ///是否需要总部
                boolean isHeadOffice = Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER || Constant.ROLE_HR || Constant.ROLE_REGIONAL_MANAGER;
                boolean isNeedAll = Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER || Constant.ROLE_HR || Constant.ROLE_REGIONAL_MANAGER;
                CommonUtils.showSelectProjectDialog(getMActivity(), project_uuid, true, isNeedAll, isHeadOffice, new PagerDrawerPopup.OnSelectProjectListener() {
                    @Override
                    public void onClick(String projectuuid, String projectName) {
                        LogUtils.e("选择项目 " + projectuuid + " ; name " + projectName);
                        project_uuid = projectuuid;
                        project_name = projectName;
                        updateChangeName(projectName);
                        requestOne();
                    }
                });
            }
        });


        mCompactAdapter.setOnItemChildClickListener(new OnItemChildClickListener() {
            @Override
            public void onItemChildClick(@NonNull BaseQuickAdapter adapter, @NonNull View view, int position) {
                if (view.getId() == R.id.iv_item_chat_avatar) {
                    Bundle bundle = new Bundle();
                    bundle.putString("uuid", mCompactAdapter.getData().get(position).getUser_uuid());
                    bundle.putString("user_name", mCompactAdapter.getData().get(position).getUser_name());
                    ActivityForwardUtil.startActivity(MyPhotosActivity.class, bundle);
                }
            }
        });

        //在岗点击
        getMDatabind().llWorkIn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_CLEANER) {
                    ToastUtil.show("暂无操作权限");
                    return;
                }
                gotoAttendance();
            }
        });
        //出勤点击
        getMDatabind().llWorkAttendance.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_CLEANER) {
                    ToastUtil.show("暂无操作权限");
                    return;
                }
                gotoAttendance();
            }
        });
        //异常点击
        getMDatabind().llWorkError.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_CLEANER) {
                    ToastUtil.show("暂无操作权限");
                    return;
                }
                gotoAttendance();
            }
        });
        //加班点击
        getMDatabind().llWorkOvertime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_CLEANER) {
                    ToastUtil.show("暂无操作权限");
                    return;
                }
                gotoAttendance();
            }
        });
    }

    /**
     * 做数据转换
     *
     * @param data
     * @return
     */
    private List<ImageEntity> getConvertData(List<ChatMessage> data) {
        List<ImageEntity> images = new ArrayList<>();
        for (ChatMessage message : data) {
            ImageEntity image = new ImageEntity();
            image.setPhotoUUid(message.getUuid());
            image.setResouceType(Integer.parseInt(message.getMessage_type()));//数据类型
            if ("2".equals(message.getMessage_type())) {
                image.setVideoUrl(message.getMedia_url());
                image.setVideoCoverUrl(message.getPic_thumb());
            } else {
                image.setPhotoUrl(message.getMedia_url());
                image.setOriginalUrl(message.getOrigin_media_url());
            }
            images.add(image);
        }
        return images;
    }


    @Override
    public void onResume() {
        LogExtKt.logE("onResume", "");
        super.onResume();
        if (isLazyLoad) {
            requestData();
        }
    }


    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }


    /**
     * 不用锁定某一个item了，只饥饿跳转过去
     */
    private void gotoAttendance() {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("show_project", 0);//默认是0 然后如果是0的话，就指定类型
        hashMap.put("project_uuid", "" + project_uuid);
        hashMap.put("project_name", "" + project_name);
        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                .pageName("AttendancePage")
                .arguments(hashMap)
                .build());
    }


    private void requestWorkingStat() {
        getMViewModel().requestChatStat(getMDatabind().tvCircleDate.getText().toString().replaceAll("\n", "-").replaceAll("/", "-"), project_uuid);
    }

    private void requestData() {
        requestOne();
        requestWorkingStat();
    }

    private void requestOne() {
        requestType = TYPE_INIT_DATA;
        page = 1;
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("page", "" + page);
        hashMap.put("size", "50");
        if (!TextUtils.isEmpty(project_uuid)) {
            hashMap.put("project_uuid", "" + project_uuid);
        }
        if (stringAdapter.getPosition() > -1) {
            hashMap.put("act_type", act_type);
        }
        hashMap.put("layout", "" + layout_type);
        //新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
        hashMap.put("is_head_office_project", CommonUtils.checkRoleHeadOffice() ? "1" : "2");
        if (layout_type == 1) {
            getMViewModel().requestChatList(hashMap);
        } else {
            getMViewModel().requestChatListCompact(hashMap);
        }
        getMViewModel().requestStat(project_uuid);
    }

    private void requestLoad() {
        requestType = TYPE_INIT_LOAD_MORE;
        page++;
        // 到达顶部，触发刷新操作
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("page", "" + page);
        hashMap.put("size", "50");
        if (!TextUtils.isEmpty(project_uuid)) {
            hashMap.put("project_uuid", "" + project_uuid);
        }
        hashMap.put("latest_record_uuid", latest_record_uuid);
        if (stringAdapter.getPosition() > -1) {
            hashMap.put("act_type", act_type);
        }
        hashMap.put("layout", "" + layout_type);
        //新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
        hashMap.put("is_head_office_project", CommonUtils.checkRoleHeadOffice() ? "1" : "2");
        if (layout_type == 1) {
            getMViewModel().requestChatList(hashMap);
        } else {
            getMViewModel().requestChatListCompact(hashMap);
        }
    }


}
