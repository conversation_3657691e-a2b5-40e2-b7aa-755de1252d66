package com.business_clean.ui.fragment.main;

import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.fastjson.JSON;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseFragment;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.weight.EqualSpacingItemDecoration;
import com.business_clean.app.weight.dialog.PagerApprovePopup;
import com.business_clean.app.weight.dialog.PagerDrawerPopup;
import com.business_clean.data.initconfig.MenuEntity;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.data.mode.project.ProjectManager;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.business_clean.data.mode.todo.TemplateEntity;
import com.business_clean.data.mode.todo.TemplateListEntity;
import com.business_clean.data.mode.workbench.BalanceEntity;
import com.business_clean.data.mode.workbench.HomeEntity;
import com.business_clean.data.mode.workbench.HomeGridItem;
import com.business_clean.data.mode.workbench.HomeGridList;
import com.business_clean.data.mode.workbench.WindStatistics;
import com.business_clean.data.mode.workbench.WorkBenchEntity;
import com.business_clean.databinding.FragmentStagingBinding;
import com.business_clean.databinding.HeaderHomeBinding;
import com.business_clean.databinding.HeaderHomeFooterBinding;
import com.business_clean.ui.adapter.BaseMenuAdapter;
import com.business_clean.ui.adapter.home.HomeCardAdapter;
import com.business_clean.viewmodel.request.HomeVideModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemChildClickListener;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.gyf.immersionbar.ImmersionBar;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.luck.picture.lib.decoration.MyGridSpacingItemDecoration;
import com.lxj.xpopup.XPopup;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;


/**
 * 这里是工作台
 */
public class WorkbenchFragment extends BaseFragment<HomeVideModel, FragmentStagingBinding> {

    private HomeCardAdapter mAdapter;

    private HeaderHomeBinding headerHomeBinding;
    private HeaderHomeFooterBinding headerHomeFooterBinding;

    private BaseMenuAdapter menuAdapter;


    private boolean isToDay = true;//记录是日考勤还是月考勤

    @Override
    public void onResume() {
        super.onResume();
        ImmersionBar.with(this).titleBar(getMDatabind().llTop).navigationBarColor(R.color.white).statusBarDarkFont(true).navigationBarColor(R.color.white).init();
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        mAdapter = new HomeCardAdapter();
        getMDatabind().list.recyclerView.setAdapter(mAdapter);
        getMDatabind().list.recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        getMDatabind().list.recyclerView.addItemDecoration(new EqualSpacingItemDecoration(SizeUtils.dp2px(10)));
        getMDatabind().list.recyclerView.setBackgroundResource(R.color.base_primary_bg_page);

        //列表头
        View headerView = requireActivity().getLayoutInflater().inflate(R.layout.header_home, null);
        headerHomeBinding = DataBindingUtil.bind(headerView);
        mAdapter.setHeaderView(headerView);
        //列表尾部
        View footerView = requireActivity().getLayoutInflater().inflate(R.layout.header_home_footer, null);
        headerHomeFooterBinding = DataBindingUtil.bind(footerView);
        mAdapter.setFooterView(footerView);

        if (headerHomeFooterBinding != null) {
            headerHomeFooterBinding.tvCompanyName.setVisibility(View.GONE);
        }

        //统计需要显示的内容
        menuAdapter = new BaseMenuAdapter();
        headerHomeBinding.recyclerRiskMonitoring.setAdapter(menuAdapter);
        headerHomeBinding.recyclerRiskMonitoring.setLayoutManager(new GridLayoutManager(getActivity(), 3));
        headerHomeBinding.recyclerRiskMonitoring.addItemDecoration(new MyGridSpacingItemDecoration(3, SizeUtils.dp2px(10), false));


        getAttendanceStat(null);

        getMDatabind().list.refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull @NotNull RefreshLayout refreshLayout) {
                request(false);
                getMDatabind().list.refreshLayout.finishRefresh(1000);
            }
        });
        getMDatabind().list.refreshLayout.setEnableLoadMore(false);


        // 处理首页的Home标签显示内容根据身份
        handleRoleVisibility(Constant.ROLE_ID, headerHomeBinding.llRiskAll, Constant.ROLE_SUPER_MANGER_ID,
                Constant.ROLE_MANGER_ID, Constant.ROLE_PROJECT_OWNER_ID, Constant.ROLE_HR_ID, Constant.ROLE_REGIONAL_MANAGER_ID);

        // 处理首页的用工统计权限判断
        handleRoleVisibility(Constant.ROLE_ID, headerHomeBinding.llStatisticsWork, Constant.ROLE_SUPER_MANGER_ID,
                Constant.ROLE_MANGER_ID, Constant.ROLE_PROJECT_OWNER_ID, Constant.ROLE_REGIONAL_MANAGER_ID);


        //处理余额 领班跟保洁不能看见余额
        if (Constant.ROLE_LEADER || Constant.ROLE_CLEANER) {
            getMDatabind().ivNotice.setVisibility(View.GONE);
            getMDatabind().tvBalance.setVisibility(View.GONE);
        } else {
            getMDatabind().ivNotice.setVisibility(View.VISIBLE);
            getMDatabind().tvBalance.setVisibility(View.VISIBLE);
        }

    }

    @Override
    public void lazyLoadData() {
        super.lazyLoadData();
        request(false);
    }


    // 封装权限处理逻辑的方法
    private void handleRoleVisibility(String roleId, View view, String... allowedRoles) {
        boolean show = Arrays.asList(allowedRoles).contains(roleId);
        if (show) {
            view.setVisibility(View.VISIBLE);
        } else {
            view.setVisibility(View.GONE);
        }
    }


    private void request(boolean hideDialog) {
        mViewModel.requestBalance(hideDialog);
        mViewModel.requestWorkbenchStat(hideDialog);
        mViewModel.requestWorkbenchGrid(hideDialog);
        if (Constant.ROLE_LEADER || Constant.ROLE_CLEANER || Constant.ROLE_PROJECT_OWNER) {
        } else {
            mViewModel.requestProjectAllList(hideDialog);
        }
    }

    @Override
    public void initObserver() {

        App.getAppViewModelInstance().getWorkbenchApprove().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                mViewModel.requestTemplateList();
            }
        });

        //监听选择项目
        App.getAppViewModelInstance().getProjectInfo().observe(this, new Observer<ProjectMangerList>() {
            @Override
            public void onChanged(ProjectMangerList projectMangerList) {
                request(true);
            }
        });


        //这里是底部grid
        getMViewModel().getHomeEntity().observe(this, new Observer<HomeEntity>() {
            @Override
            public void onChanged(HomeEntity homeEntity) {
                mAdapter.setList(homeEntity.getList());
            }
        });

    }


    @Override
    public void onBindViewClick() {

        //被投诉
        headerHomeBinding.llComplaint.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("position", "1");
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("qualityMonitoringPage")
                        .arguments(hashMap)
                        .build());
            }
        });

        //任务延误
        headerHomeBinding.llDelay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("position", "2");
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("qualityMonitoringPage")
                        .arguments(hashMap)
                        .build());
            }
        });

        //工单
        headerHomeBinding.llWorkOrder.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("position", "3");
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("qualityMonitoringPage")
                        .arguments(hashMap)
                        .build());
            }
        });

        //系统通知
        getMDatabind().ivNotice.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("noticeMessagePage")
                        .arguments(hashMap)
                        .build());
            }
        });

        //跳转 flutter 余额充值
        getMDatabind().tvBalance.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER || Constant.ROLE_SUPER_MANGER) {
                    HashMap<String, Object> hashMap = new HashMap<>();
                    FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                            .pageName("balanceMainPostPage")
                            .arguments(hashMap)
                            .build());
                }
            }
        });

        //菜单栏
        menuAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("position", "" + position);
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("riskPage")
                        .arguments(hashMap)
                        .build());
                ///判断全部
//                if (App.getAppViewModelInstance().getProjectInfo().getValue() != null) {
//                    ProjectMangerList projectMangerList = App.getAppViewModelInstance().getProjectInfo().getValue();
//                    if (projectMangerList != null) {
//                        if (TextUtils.isEmpty(projectMangerList.getUuid())) {
//                            CommonUtils.showSelectProjectDialog(getMActivity(), "", true, false, CommonUtils.checkRoleHeadOffice(), new PagerDrawerPopup.OnSelectProjectListener() {
//                                @Override
//                                public void onClick(String project_uuid, String project_name) {
//                                    //点击后，判断是那种类型的风险监控 传递个给flutter
//                                    HashMap<String, Object> hashMap = new HashMap<>();
//                                    hashMap.put("position", "" + position);
//                                    FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
//                                            .pageName("riskPage")
//                                            .arguments(hashMap)
//                                            .build());
//                                }
//                            });
//                        } else {
//                            //点击后，判断是那种类型的风险监控 传递个给flutter
//                            HashMap<String, Object> hashMap = new HashMap<>();
//                            hashMap.put("position", "" + position);
//                            FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
//                                    .pageName("riskPage")
//                                    .arguments(hashMap)
//                                    .build());
//                        }
//                    }
//                }
            }
        });

        mAdapter.setOnItemChildClickListener(new OnItemChildClickListener() {
            @Override
            public void onItemChildClick(@NonNull BaseQuickAdapter adapter, @NonNull View view, int position) {
                switch (view.getId()) {
                    case R.id.tv_item_home_card_approve_set:
                        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                                .pageName("appRoveMainPage")
                                .arguments(new HashMap<>())
                                .build());
                        break;
                    case R.id.tv_item_home_card_approve_look:
                        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                                .pageName("ApproveRecordPage")
                                .arguments(new HashMap<>())
                                .build());
                        break;
                    case R.id.tv_item_home_card_approve_me:
                        App.getAppViewModelInstance().getTodoMeCreate().setValue(true);
                        break;
                }
            }
        });
    }


    @Override
    public void onRequestSuccess() {
        //余额
        mViewModel.getBalanceEntity().observe(this, new Observer<BalanceEntity>() {
            @Override
            public void onChanged(BalanceEntity balanceEntity) {
                if (!TextUtils.isEmpty(balanceEntity.getBalance())) {
                    String textString = "余额:" + balanceEntity.getBalance() + "元";

                    SpannableString spannableString = new SpannableString(textString);
                    //设置文字颜色
                    ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(ContextCompat.getColor(getMActivity(), R.color.base_primary));
                    spannableString.setSpan(foregroundColorSpan, textString.indexOf(":") + 1, textString.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);

                    getMDatabind().tvBalance.setText(spannableString);
                }
            }
        });
        //全部的项目
        mViewModel.getAllProjectManager().observe(this, new Observer<ProjectManager>() {
            @Override
            public void onChanged(ProjectManager projectManager) {
                App.getAppViewModelInstance().getAllProjectManager().setValue(projectManager);
                MMKVHelper.putString(ConstantMMVK.ALL_PROJECT_LIST, JSON.toJSONString(projectManager));
            }
        });
        //统计
        mViewModel.getWorkBenchEntity().observe(this, new Observer<WorkBenchEntity>() {
            @Override
            public void onChanged(WorkBenchEntity workBenchEntity) {
                //顶部统计
                if (workBenchEntity.getWind() != null) {
                    getAttendanceStat(workBenchEntity.getWind());
                }
                //消息通知
                if (workBenchEntity.getNotice() != null && !TextUtils.isEmpty(workBenchEntity.getNotice().getUn_read_total())) {
                    int readTotal = Integer.parseInt(workBenchEntity.getNotice().getUn_read_total());
                    if (readTotal > 0) {
                        getMDatabind().ivNotice.setImageResource(R.mipmap.icon_base_notice);
                    } else {
                        getMDatabind().ivNotice.setImageResource(R.mipmap.icon_base_notice_un);
                    }
                }

                //品质监测
                if (workBenchEntity.getQuality() != null) {
                    if (!TextUtils.isEmpty(workBenchEntity.getQuality().getComplaint_project_total())) {//被投诉
                        headerHomeBinding.tvComplaintNum.setText(workBenchEntity.getQuality().getComplaint_project_total());
                    }
                    if (!TextUtils.isEmpty(workBenchEntity.getQuality().getPostpone_task_project_total())) {//延迟
                        headerHomeBinding.tvDelayNum.setText(workBenchEntity.getQuality().getPostpone_task_project_total());
                    }
                    if (!TextUtils.isEmpty(workBenchEntity.getQuality().getNo_work_order_project_total())) {//无工单
                        headerHomeBinding.tvWorkOrderNum.setText(workBenchEntity.getQuality().getNo_work_order_project_total());
                    }
                }

            }
        });
        //工作台
        mViewModel.getHomeEntity().observe(this, new Observer<HomeEntity>() {
            @Override
            public void onChanged(HomeEntity homeEntity) {
                mAdapter.setList(homeEntity.getList());
            }
        });
        //审批列表
        mViewModel.getTemplateEntity().observe(this, new Observer<TemplateEntity>() {
            @Override
            public void onChanged(TemplateEntity templateEntity) {
                if (templateEntity != null) {
                    new XPopup.Builder(getMActivity())
                            .asCustom(new PagerApprovePopup(getMActivity(), templateEntity.getList()))
                            .show();
                }
            }
        });
    }

    private void getAttendanceStat(WindStatistics data) {
        List<MenuEntity> datas = new ArrayList<>();
        boolean hasError = false;
        if (data != null) {
            datas.add(new MenuEntity("" + data.getInsurance_total(), "", "缺保险", "#000000"));
            datas.add(new MenuEntity("" + data.getExpired_project_total(), "", "到期项目", "#A671CF"));
            datas.add(new MenuEntity("" + data.getCredit_total(), "", "信用异常", "#1890FF"));
            datas.add(new MenuEntity("" + data.getEntry_contract_total(), "", "缺劳动协议", "#FF801A"));
            datas.add(new MenuEntity("" + data.getLeave_contract_total(), "", "缺离职协议", "#FF4040"));
            if (data.getInsurance_total() > 0 || data.getExpired_project_total() > 0 || data.getCredit_total() > 0 ||
                    data.getLeave_contract_total() > 0 || data.getEntry_contract_total() > 0) {
                hasError = true;
            }
        } else {
            datas.add(new MenuEntity("0", "", "缺保险", "#000000"));
            datas.add(new MenuEntity("0", "", "到期项目", "#A671CF"));
            datas.add(new MenuEntity("0", "", "信用异常", "#1890FF"));
            datas.add(new MenuEntity("0", "", "缺劳动协议", "#FF801A"));
            datas.add(new MenuEntity("0", "", "缺离职协议", "#FF4040"));
        }

        if (hasError) {
            updateHeaderErrorView();
        } else {
            updateHeaderOkView();
        }

        menuAdapter.setList(datas);
    }


    private void updateHeaderErrorView() {
        headerHomeBinding.tvRiskBlur.setVisibility(View.VISIBLE);
        headerHomeBinding.tvRiskBlurOk.setVisibility(View.GONE);
        headerHomeBinding.ivRisk.setImageResource(R.mipmap.icon_work_error);
    }

    private void updateHeaderOkView() {
        headerHomeBinding.tvRiskBlur.setVisibility(View.GONE);
        headerHomeBinding.tvRiskBlurOk.setVisibility(View.VISIBLE);
        headerHomeBinding.ivRisk.setImageResource(R.mipmap.icon_workbench_ok);
    }
}
