package com.business_clean.ui.activity.personnel;

import androidx.lifecycle.Observer;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseAgentWebActivity;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.OnJsBridge;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.data.mode.members.MembersDetailEntity;
import com.business_clean.databinding.ActivityPersonnelDetailBinding;
import com.business_clean.ui.activity.me.MyPhotosActivity;
import com.business_clean.viewmodel.request.ProjectMembersViewModel;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.just.agentweb.AgentWeb;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class PersonnelDetailActivity extends BaseAgentWebActivity<ProjectMembersViewModel, ActivityPersonnelDetailBinding> {
    private String uuid;
    private String user_name;
    private String id_number;
    private String project_name;

    @Override
    public void initView(Bundle savedInstanceState) {
        mToolbar.setTitle("档案详情");
        setOnResumeReload(true);
        if (getIntent() != null && getIntent().getExtras() != null) {
            uuid = getIntent().getExtras().getString("uuid");
            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("uuid", uuid);
            hashMap.put("is_identity", "1");
            hashMap.put("is_work_info", "1");
            mViewModel.requestStaffDetail(hashMap);
        }
        if (Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER) {
            mToolbar.setRightText("删除");
        }

    }

    @Override
    public void onBindViewClick() {
        //考勤统计
        mDatabind.tvAttendanceStat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                Bundle bundle = new Bundle();
//                bundle.putString("user_uuid", uuid);
//                bundle.putString("user_name", user_name);
//                bundle.putString("project_name", project_name);
//                ActivityForwardUtil.startActivity(MyAttendanceActivity.class, bundle);
                LogUtils.e(uuid);
                CommonToFlutter.gotoFlutterAttendanceStatPage(uuid, user_name);
            }
        });
        //再次入职
        mDatabind.tvAgainEntry.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                App.getAppViewModelInstance().getAgainEntry().setValue(uuid);
//                ActivityForwardUtil.startActivity(AddProjectActivity.class);
                CommonToFlutter.gotoFlutterAgainAddStaffPage(uuid);

            }
        });
        //离职
        mDatabind.tvQuick.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonToFlutter.gotoFlutterQuickDepartPage(uuid, "");
            }
        });
        //删除员工
        mToolbar.setOnRightTextClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CommonUtils.showGeneralDialog(PersonnelDetailActivity.this, "删除档案", "是否删除该档案？",
                        "取消", "确定", null, new OnDialogConfirmListener() {
                            @Override
                            public void onConfirm() {
                                if (!TextUtils.isEmpty(uuid)) {
                                    mViewModel.requestDeleteStaff(uuid);
                                }
                            }
                        });

            }
        });
        //信用查询
        mDatabind.tvAttendanceCredit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextUtils.isEmpty(id_number)) {
                    ToastUtil.show("无身份证号码，无法查询");
                    return;
                }
                CommonToFlutter.gotoCredit(uuid, id_number, user_name);
            }
        });
        //合同记录
        mDatabind.tvAttendanceContract.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("uuid", uuid);
                hashMap.put("user_name", user_name);
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("contractRecordPage")
                        .arguments(hashMap)
                        .build());
            }
        });

        //个人相册
        mDatabind.tvPhotos.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Bundle bundle = new Bundle();
                bundle.putString("uuid", uuid);
                bundle.putString("user_name", user_name);
                ActivityForwardUtil.startActivity(MyPhotosActivity.class, bundle);
            }
        });
    }


    @Override
    public void onRequestSuccess() {
        mViewModel.getDeleteStaff().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                ToastUtil.show("删除成功");
                PersonnelDetailActivity.this.finish();
                App.getAppViewModelInstance().getRefreshMember().postValue(true);
            }
        });
    }

    @Override
    public void initObserver() {
        mViewModel.getMembersDetailEntity().observe(this, new Observer<MembersDetailEntity>() {
            @Override
            public void onChanged(MembersDetailEntity membersDetailEntity) {
                if (membersDetailEntity != null && membersDetailEntity.getIdentity() != null) {
                    id_number = membersDetailEntity.getIdentity().getId_number();
                    user_name = membersDetailEntity.getIdentity().getUser_name();
                    project_name = membersDetailEntity.getProject_short_name();
                    mToolbar.setSubTitle(user_name);
                }
                if (membersDetailEntity != null && membersDetailEntity.getWork_info() != null) {
                    //如果权限相同，并且如果当前这个档案是超管，那么久隐藏
                    if (Constant.ROLE_ID.equals(membersDetailEntity.getWork_info().getRole_id()) || "-1".equals(membersDetailEntity.getWork_info().getRole_id())) {
                        mToolbar.setRightText("");//如果相等，那么就隐藏
                    }
                }
            }
        });

    }


    @Override
    protected OnJsBridge getOnJsBridge() {
        return new JsBridge(mAgentWeb.get());
    }

    class JsBridge extends OnJsBridge {
        JsBridge(AgentWeb mAgentWeb) {
            super(mAgentWeb);
        }

        @JavascriptInterface
        public void sendStatus(String json) {
            LogUtils.e("json --> " + json);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        if (!TextUtils.isEmpty(json)) {
                            JSONObject jsonObject = new JSONObject(json);
                            // status 1 在职  2 离职
                            String status = jsonObject.getString("status");
                            if ("1".equals(status)) {
                                mDatabind.tvAgainEntry.setVisibility(View.GONE);
                                mDatabind.tvQuick.setVisibility(View.VISIBLE);
                            } else {
                                mDatabind.tvAgainEntry.setVisibility(View.VISIBLE);
                                mDatabind.tvQuick.setVisibility(View.GONE);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
        }

        @JavascriptInterface
        public void editModule(String json) {
            LogUtils.e("json --> " + json);
            try {
                if (!TextUtils.isEmpty(json)) {
                    JSONObject jsonObject = new JSONObject(json);
                    // num 6详情 0.身份信息  1联系信息  2银行卡 3任职安排 4薪酬安排 5材料附件 7合同
                    int num = jsonObject.getInt("num");
                    if (num == 7) {
                        return;
                    }
                    CommonToFlutter.gotoFlutterAddStaffPage(uuid, null, 3, num, user_name);
//                    Bundle bundle = new Bundle();
//                    bundle.putInt("type", 3);//档案的编辑都传递 3
//                    bundle.putInt("jump_index", num);
//                    bundle.putString("uuid", uuid);
//                    bundle.putString("user_name", user_name);
//                    ActivityForwardUtil.startActivity(AddProjectActivity.class, bundle);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


        @JavascriptInterface
        public void goPolicyList(String json) {
            LogUtils.e("json --> " + json);
            try {
                if (!TextUtils.isEmpty(json)) {
                    JSONObject jsonObject = new JSONObject(json);
                    // 获取保险订单列表
                    String user_name = jsonObject.getString("user_name");
                    HashMap<String, Object> hashMap = new HashMap<>();
                    hashMap.put("search_name", user_name);
                    FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                            .pageName("insurePage")
                            .arguments(hashMap)
                            .build());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    @Override
    protected ViewGroup getAgentWebParent() {
        return mDatabind.llLayout;
    }


    @Override
    protected String getUrl() {
        return CommonUtils.getWebUrlHost() + NetUrl.WEB_PROJECT_FILE_DETAIL_URL + "uuid=" + uuid;
    }
}