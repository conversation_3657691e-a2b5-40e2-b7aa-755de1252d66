package com.business_clean.ui.activity.balance;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;

import androidx.annotation.NonNull;

import com.business_clean.app.base.BaseAgentWebActivity;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.OnJsBridge;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.ext.LoadingDialogExtKt;
import com.business_clean.app.util.ChoosePicUtil;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.UploadFileHelper;
import com.business_clean.app.util.permission.PermissionInterceptor;
import com.business_clean.databinding.ActivityCorporateRechargeBinding;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.just.agentweb.AgentWeb;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.lxj.xpopup.interfaces.OnSelectListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import me.hgj.mvvmhelper.base.BaseViewModel;
import me.hgj.mvvmhelper.ext.AppExtKt;
import me.hgj.mvvmhelper.ext.LogExtKt;

public class CorporateRechargeActivity extends BaseAgentWebActivity<BaseViewModel, ActivityCorporateRechargeBinding> {

    private String url;

    private String[] strings = {"拍照", "相册"};


    @Override
    public void initView(Bundle savedInstanceState) {
        mToolbar.setTitle("对公充值");
        if (getIntent() != null && getIntent().getExtras() != null) {
            url = getIntent().getExtras().getString("url");
        }
        setOnResumeReload(false);
    }


    @Override
    public void onBindViewClick() {

    }

    @Override
    public void initObserver() {

    }

    @Override
    protected ViewGroup getAgentWebParent() {
        return mDatabind.llLayout;
    }

    @Override
    protected String getUrl() {
        return url;
    }

    @Override
    protected OnJsBridge getOnJsBridge() {
        return new JsBridge(mAgentWeb.get());
    }

    class JsBridge extends OnJsBridge {
        JsBridge(AgentWeb mAgentWeb) {
            super(mAgentWeb);
        }

        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void backPage(String json) {
            finish();
        }

        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void goPublicRechargeOrder(String json) {
            finish();
        }

        @SuppressLint("JavascriptInterface")
        @JavascriptInterface
        public void openNewPromptUrl(String json) {
            try {
                JSONObject jsonObject = new JSONObject(json);
                String url = jsonObject.getString("url");
                if (!TextUtils.isEmpty(url)) {
                    CommonUtils.gotoBaseWebActivity(url);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        @JavascriptInterface
        public void uploadMedia(String json) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    CommonUtils.showBottomListWith(CorporateRechargeActivity.this, 0, "", strings, new OnSelectListener() {
                        @Override
                        public void onSelect(int position, String text) {
                            switch (position) {
                                case 0:
                                    XXPermissions.with(CorporateRechargeActivity.this)
                                            .permission(Permission.CAMERA)
                                            .interceptor(new PermissionInterceptor("拍摄照片，需要“相机”的权限。"))
                                            .request(new OnPermissionCallback() {
                                                @Override
                                                public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                                                    if (allGranted) {
                                                        ChoosePicUtil.openCameraTake(CorporateRechargeActivity.this, new OnResultCallbackListener<LocalMedia>() {
                                                            @Override
                                                            public void onResult(ArrayList<LocalMedia> result) {
                                                                if (!TextUtils.isEmpty(result.get(0).getCompressPath())) {
                                                                    uploadPic(result.get(0).getCompressPath());
                                                                } else {
                                                                    uploadPic(result.get(0).getRealPath());
                                                                }
                                                            }

                                                            @Override
                                                            public void onCancel() {

                                                            }
                                                        });
                                                    }
                                                }
                                            });
                                    break;
                                case 1:
                                    XXPermissions.with(CorporateRechargeActivity.this)
                                            .permission(AppExtKt.getRequiredPermissions())
                                            .interceptor(new PermissionInterceptor("从相册中选取照片，需要“照片/视频”的权限。"))
                                            .request(new OnPermissionCallback() {
                                                @Override
                                                public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                                                    if (allGranted) {
                                                        ChoosePicUtil.openAlbum(CorporateRechargeActivity.this, 1, new OnResultCallbackListener<LocalMedia>() {
                                                            @Override
                                                            public void onResult(ArrayList<LocalMedia> result) {
                                                                if (!TextUtils.isEmpty(result.get(0).getCompressPath())){
                                                                    uploadPic(result.get(0).getCompressPath());
                                                                }else{
                                                                    uploadPic(result.get(0).getRealPath());
                                                                }
                                                            }

                                                            @Override
                                                            public void onCancel() {

                                                            }
                                                        });
                                                    }
                                                }
                                            });
                                    break;
                            }
                        }
                    });
                }
            });
        }
    }

    /**
     * 上传图片
     */
    private void uploadPic(String path) {
        LoadingDialogExtKt.showLoadingExt(this, "上传中");
        UploadFileHelper.getInstance().uploadPictures(CorporateRechargeActivity.this, path, UploadFileHelper.PATH_HEADER_PERSONNEL, new UploadFileHelper.UploadListener() {
            @Override
            public void onUploadSuccess(String response) {
                LogExtKt.logE("最后回调到的图片---> " + response, "");
                if (mAgentWeb != null) {
                    mAgentWeb.get().getJsAccessEntrace().quickCallJs(Constant.JS_SPACE_NAME + "uploadMedia" + Constant.JS_SPACE_CALLBACK, response);
                }
                LoadingDialogExtKt.dismissLoadingExt(CorporateRechargeActivity.this);
            }

            @Override
            public void onUploadFailed(String error) {
                ToastUtil.show("上传失败");
                LoadingDialogExtKt.dismissLoadingExt(CorporateRechargeActivity.this);
            }

            @Override
            public void onUploadProgress(int progress) {

            }


        });
    }

}