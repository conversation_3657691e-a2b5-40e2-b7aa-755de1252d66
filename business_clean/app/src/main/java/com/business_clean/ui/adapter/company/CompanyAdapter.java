package com.business_clean.ui.adapter.company;

import androidx.annotation.NonNull;

import com.business_clean.R;
import com.business_clean.data.mode.login.CompanyEntity;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;

public class CompanyAdapter extends BaseQuickAdapter<CompanyEntity, BaseViewHolder> {
    public CompanyAdapter() {
        super(R.layout.item_layout_company);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder holder, CompanyEntity companyEntity) {
        holder.setText(R.id.tv_item_company_name, companyEntity.getFull_name())
                .setText(R.id.tv_item_company_sub_name, companyEntity.getCompany_name());
    }
}
