package com.business_clean.ui.adapter.custom;

import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import com.business_clean.R;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.business_clean.app.util.SafeAccessHelper;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class CustomMangerNoPaddingAllAdapter extends BaseQuickAdapter<ProjectMangerList, BaseViewHolder> {

    private String uuid = "";

    private List<ProjectMangerList> originalData; // 存储原始数据
    private List<ProjectMangerList> filteredData = new ArrayList<>(); // 存储过滤后的数据

    public CustomMangerNoPaddingAllAdapter() {
        super(R.layout.item_custom_no_padding);
        addChildClickViewIds(R.id.ck_item_choose);
    }

    @Override
    protected void convert(@NotNull BaseViewHolder baseDataBindingHolder, ProjectMangerList item) {
        if (item == null) {
            return;
        }

        // 使用工具类安全设置文本
        baseDataBindingHolder.setText(R.id.tv_item_custom, SafeAccessHelper.safeString(item.getProject_short_name()));
        baseDataBindingHolder.setText(R.id.tv_item_custom_sub, SafeAccessHelper.safeString(item.getProject_name()));

        // 设置项目全名称的可见性
        TextView tvProject = baseDataBindingHolder.getView(R.id.tv_item_custom_sub);
        if (tvProject != null) {
            String projectName = SafeAccessHelper.safeString(item.getProject_name());
            tvProject.setVisibility(TextUtils.isEmpty(projectName) ? View.GONE : View.VISIBLE);
        }

        // 设置选择状态
        CheckBox checkBox = baseDataBindingHolder.getView(R.id.ck_item_choose);
        if (checkBox != null) {
            String itemUuid = SafeAccessHelper.safeString(item.getUuid());
            checkBox.setChecked(SafeAccessHelper.safeString(uuid).equals(itemUuid));
        }
    }

    public void updateChoose(String uuid) {
        this.uuid = SafeAccessHelper.safeString(uuid);
        notifyDataSetChanged();
    }

    private void moveSelectedItemToTop() {
        List<ProjectMangerList> data = getData(); // 获取当前适配器的数据

        for (int i = 0; i < data.size(); i++) {
            ProjectMangerList item = data.get(i);
            if (item.getUuid().equals(uuid)) {
                // 移动选中的项到第一位
                ProjectMangerList selectedItem = data.remove(i);
                data.add(0, selectedItem);
                break; // 找到并移动后退出循环
            }
        }
        setList(data); // 更新适配器的数据
    }


    // 新增方法：根据关键字过滤数据
    public void filter(String query) {
        SafeAccessHelper.safeExecuteWithFallback(() -> {
            initializeFilterData();

            if (TextUtils.isEmpty(query)) {
                setList(originalData);
            } else {
                filterByQuery(query.toLowerCase());
            }
        }, () -> {
            // 异常时显示原始数据
            setList(originalData != null ? originalData : new ArrayList<>());
        });
    }

    /**
     * 初始化过滤数据
     */
    private void initializeFilterData() {
        if (originalData == null) {
            List<ProjectMangerList> currentData = getData();
            originalData = currentData != null ? new ArrayList<>(currentData) : new ArrayList<>();
        }

        if (filteredData == null) {
            filteredData = new ArrayList<>();
        }
        filteredData.clear();
    }

    /**
     * 根据查询条件过滤数据
     * @param lowerQuery 小写的查询字符串
     */
    private void filterByQuery(String lowerQuery) {
        for (ProjectMangerList item : originalData) {
            if (item == null) {
                continue;
            }

            String shortName = SafeAccessHelper.safeString(item.getProject_short_name()).toLowerCase();
            String projectName = SafeAccessHelper.safeString(item.getProject_name()).toLowerCase();

            if (shortName.contains(lowerQuery) || projectName.contains(lowerQuery)) {
                filteredData.add(item);
            }
        }
        setList(filteredData);
    }

}
