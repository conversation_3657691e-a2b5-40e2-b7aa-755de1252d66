package com.business_clean.ui.adapter.custom;

import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import com.business_clean.R;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class CustomMangerNoPaddingAllAdapter extends BaseQuickAdapter<ProjectMangerList, BaseViewHolder> {

    private String uuid = "";

    private List<ProjectMangerList> originalData; // 存储原始数据
    private List<ProjectMangerList> filteredData = new ArrayList<>(); // 存储过滤后的数据

    public CustomMangerNoPaddingAllAdapter() {
        super(R.layout.item_custom_no_padding);
        addChildClickViewIds(R.id.ck_item_choose);
    }

    @Override
    protected void convert(@NotNull BaseViewHolder baseDataBindingHolder, ProjectMangerList item) {
        if (item == null) {
            return;
        }

        // 安全设置项目短名称
        String shortName = item.getProject_short_name();
        baseDataBindingHolder.setText(R.id.tv_item_custom, shortName != null ? shortName : "");

        // 安全设置项目全名称
        String projectName = item.getProject_name();
        baseDataBindingHolder.setText(R.id.tv_item_custom_sub, projectName != null ? projectName : "");

        TextView tvProject = baseDataBindingHolder.getView(R.id.tv_item_custom_sub);
        if (tvProject != null) {
            if (!TextUtils.isEmpty(projectName)) {
                tvProject.setVisibility(View.VISIBLE);
            } else {
                tvProject.setVisibility(View.GONE);
            }
        }

        CheckBox checkBox = baseDataBindingHolder.getView(R.id.ck_item_choose);
        if (checkBox != null) {
            String itemUuid = item.getUuid();
            if (uuid != null && uuid.equals(itemUuid)) {
                checkBox.setChecked(true);
            } else {
                checkBox.setChecked(false);
            }
        }
    }

    public void updateChoose(String uuid) {
        this.uuid = uuid != null ? uuid : "";
        notifyDataSetChanged();
    }

    private void moveSelectedItemToTop() {
        List<ProjectMangerList> data = getData(); // 获取当前适配器的数据

        for (int i = 0; i < data.size(); i++) {
            ProjectMangerList item = data.get(i);
            if (item.getUuid().equals(uuid)) {
                // 移动选中的项到第一位
                ProjectMangerList selectedItem = data.remove(i);
                data.add(0, selectedItem);
                break; // 找到并移动后退出循环
            }
        }
        setList(data); // 更新适配器的数据
    }


    // 新增方法：根据关键字过滤数据
    public void filter(String query) {
        try {
            if (originalData == null) {
                List<ProjectMangerList> currentData = getData();
                originalData = currentData != null ? new ArrayList<>(currentData) : new ArrayList<>();
            }

            if (filteredData == null) {
                filteredData = new ArrayList<>();
            }
            filteredData.clear();

            if (TextUtils.isEmpty(query)) {
                // 如果查询为空，显示所有数据
                setList(originalData);
            } else {
                // 根据关键字过滤
                String lowerQuery = query.toLowerCase();
                for (ProjectMangerList item : originalData) {
                    if (item == null) {
                        continue;
                    }

                    String shortName = item.getProject_short_name();
                    String projectName = item.getProject_name();

                    // 检查短名称和项目名称是否为空，并进行过滤
                    if ((!TextUtils.isEmpty(shortName) && shortName.toLowerCase().contains(lowerQuery)) ||
                            (!TextUtils.isEmpty(projectName) && projectName.toLowerCase().contains(lowerQuery))) {
                        filteredData.add(item);
                    }
                }
                setList(filteredData); // 更新适配器的数据
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 发生异常时，显示原始数据或空列表
            if (originalData != null) {
                setList(originalData);
            } else {
                setList(new ArrayList<>());
            }
        }
    }

}
