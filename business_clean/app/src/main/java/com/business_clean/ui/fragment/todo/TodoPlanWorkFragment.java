package com.business_clean.ui.fragment.todo;

import static com.business_clean.app.config.Constant.TYPE_INIT_DATA;
import static com.business_clean.app.config.Constant.TYPE_INIT_LOAD_MORE;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseFragment;
import com.business_clean.app.config.Constant;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.CheckListDataUtil;
import com.business_clean.data.initconfig.BaseStringNumEntity;
import com.business_clean.data.mode.todo.PlanEntity;
import com.business_clean.data.mode.todo.TodoTotalEntity;
import com.business_clean.databinding.FragmentTodoPlanWordBinding;
import com.business_clean.ui.adapter.TodoStringNumAdapter;
import com.business_clean.ui.adapter.todo.TodoPlanAdapter;
import com.business_clean.viewmodel.request.TodoViewModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemChildClickListener;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.luck.picture.lib.decoration.MyGridSpacingItemDecoration;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 工单详情
 */
public class TodoPlanWorkFragment extends BaseFragment<TodoViewModel, FragmentTodoPlanWordBinding> {

    private int requestType;

    private int page = 1;

    private int status = 3;

    private TodoPlanAdapter mAdapter = new TodoPlanAdapter();

    private boolean isFirstRequest;//是否已经跳转过了，跳转过就不跳转了

    private TodoStringNumAdapter stringAdapter = new TodoStringNumAdapter(1);

    public TodoPlanWorkFragment() {
    }


    @Override
    public void initView(@Nullable Bundle savedInstanceState) {


        stringAdapter.setCustomSelectDrawable(CommonUtils.getBaseSelectedDrawable(getMActivity(), 50, R.color.base_primary_select, R.color.base_primary, 1));
        stringAdapter.setCustomUnSelectDrawable(CommonUtils.getBaseUnSelectedDrawable(getMActivity(), 50, R.color.white, R.color.base_primary_new_press, 1));

        getMDatabind().recycler.setLayoutManager(new GridLayoutManager(getActivity(), 4));
        getMDatabind().recycler.setAdapter(stringAdapter);
        getMDatabind().recycler.addItemDecoration(new MyGridSpacingItemDecoration(4, SizeUtils.dp2px(10), true));
        getTagList(null);
        stringAdapter.updateItem(0);

        getMDatabind().list.recyclerView.setLayoutManager(new LinearLayoutManager(getMActivity()));
        getMDatabind().list.recyclerView.setAdapter(mAdapter);
        mAdapter.setType(20);
        getMDatabind().list.refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull @NotNull RefreshLayout refreshLayout) {
                requestMore();
            }

            @Override
            public void onRefresh(@NonNull @NotNull RefreshLayout refreshLayout) {
                requestOne();
            }
        });


    }

    @Override
    public void onBindViewClick() {
        //筛选
        stringAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull @NotNull BaseQuickAdapter<?, ?> adapter, @NonNull @NotNull View view, int position) {
                clickChange(position);
            }
        });

        //增加任务
        getMDatabind().ivAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                if (Constant.ROLE_PROJECT_OWNER) {
                    hashMap.put("choose", "1");
                } else {
                    hashMap.put("choose", "2");
                }
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("workAddTaskPage")
                        .arguments(hashMap)
                        .build());
            }
        });

        //去拍照
        mAdapter.setOnItemChildClickListener(new OnItemChildClickListener() {
            @Override
            public void onItemChildClick(@NonNull BaseQuickAdapter adapter, @NonNull View view, int position) {
                if (view.getId() == R.id.tv_item_take_photo) {//拍照
                    CommonToFlutter.gotoFlutterTaskOnePage(mAdapter.getData().get(position).getUuid(), "20", true);
                } else {
                    gotoPlanDetail(mAdapter.getData().get(position).getUuid());
                }
            }
        });


        mAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                gotoPlanDetail(mAdapter.getData().get(position).getUuid());
            }
        });
    }

    private void clickChange(int position) {
        stringAdapter.updateItem(position);
        switch (position) {
            case 0:
                status = 3;
                break;
            case 1:
                status = 2;
                break;
            case 2:
                status = 1;
                break;
            case 3:
                status = 4;
                break;
        }
        requestOne();
    }

    @Override
    public void initObserver() {

        App.getAppViewModelInstance().getRefreshWorkOrder().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                clickChange(3);
                mViewModel.requestTodoTotal();
            }
        });
    }

    @Override
    public void lazyLoadData() {
        requestOne();
    }

    @Override
    public void onRequestSuccess() {
        mViewModel.getPlanEntity().observe(this, new Observer<PlanEntity>() {
            @Override
            public void onChanged(PlanEntity planEntity) {
                getTagList(planEntity);
                switch (requestType) {
                    case TYPE_INIT_DATA:
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkInitData(planEntity.getList(), mAdapter, getMDatabind().list.refreshLayout)) {
                            mAdapter.setList(planEntity.getList());
                        }
                        break;
                    case TYPE_INIT_LOAD_MORE:
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkLoadMoreData(planEntity.getList(), mAdapter, getMDatabind().list.refreshLayout)) {
                            mAdapter.addData(planEntity.getList());
                        }
                        break;
                }
            }
        });


        mViewModel.getTodoTotalEntity().observe(this, new Observer<TodoTotalEntity>() {
            @Override
            public void onChanged(TodoTotalEntity todoTotalEntity) {
                App.getAppViewModelInstance().getRefreshTodoTotal().setValue(todoTotalEntity);
            }
        });

    }


    private void getTagList(PlanEntity data) {
        List<BaseStringNumEntity> strings = new ArrayList<>();
        if (data != null) {

            strings.add(new BaseStringNumEntity("已超时", "" + data.getOver_time_total(), "#FF4040", "#FFFFFF"));
            strings.add(new BaseStringNumEntity("待处理", "" + data.getWait_total(), "#8D8E99", "#FFFFFF"));
            strings.add(new BaseStringNumEntity("已处理", "" + data.getOver_time_total(), "#F8F7FC", "#8D8E99", true));
            strings.add(new BaseStringNumEntity("我创建的", "" + data.getWait_total(), "#F8F7FC", "#8D8E99", true));

            if (data.getOver_time_total() > 0 && !isFirstRequest) {
                clickChange(0);
                isFirstRequest = true;
                return;
            }

            if (data.getWait_total() > 0 && !isFirstRequest) {
                clickChange(1);
                isFirstRequest = true;
                return;
            }
        } else {
            strings.add(new BaseStringNumEntity("已超时", "0", "#FF4040", "#FFFFFF"));
            strings.add(new BaseStringNumEntity("待处理", "0", "#8D8E99", "#FFFFFF"));
            strings.add(new BaseStringNumEntity("已处理", "0", "#F8F7FC", "#8D8E99", true));
            strings.add(new BaseStringNumEntity("我创建的", "0", "#F8F7FC", "#8D8E99", true));
        }
        stringAdapter.setList(strings);
    }


    private void requestOne() {
        requestType = TYPE_INIT_DATA;
        page = 1;
        mViewModel.requestPlanList(page, status, 20);
        mViewModel.requestTodoTotal();
    }

    private void requestMore() {
        requestType = TYPE_INIT_LOAD_MORE;
        page++;
        mViewModel.requestPlanList(page, status, 20);
    }


    private void gotoPlanDetail(String uuid) {
        CommonToFlutter.gotoFlutterTaskOnePage(uuid, "20", false);

    }

}
