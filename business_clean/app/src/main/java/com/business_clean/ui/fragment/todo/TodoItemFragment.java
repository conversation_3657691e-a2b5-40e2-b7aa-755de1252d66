package com.business_clean.ui.fragment.todo;

import static com.business_clean.app.config.Constant.TYPE_INIT_DATA;
import static com.business_clean.app.config.Constant.TYPE_INIT_LOAD_MORE;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseFragment;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.CheckListDataUtil;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.weight.EqualSpacingItemDecoration;
import com.business_clean.app.weight.dialog.PagerApprovePopup;
import com.business_clean.data.initconfig.BaseStringNumEntity;
import com.business_clean.data.mode.todo.TemplateEntity;
import com.business_clean.data.mode.todo.TodoItemList;
import com.business_clean.data.mode.todo.TodoList;
import com.business_clean.data.mode.todo.TodoTotalEntity;
import com.business_clean.databinding.FragmentTodoItemBinding;
import com.business_clean.ui.activity.todo.TodoDetailActivity;
import com.business_clean.ui.adapter.TodoStringNumAdapter;
import com.business_clean.ui.adapter.todo.TodoAdapter;
import com.business_clean.viewmodel.request.TodoViewModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemChildClickListener;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.luck.picture.lib.decoration.MyGridSpacingItemDecoration;
import com.lxj.xpopup.XPopup;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class TodoItemFragment extends BaseFragment<TodoViewModel, FragmentTodoItemBinding> {

    private TodoStringNumAdapter stringAdapter = new TodoStringNumAdapter(1);

    private TodoAdapter mAdapter = new TodoAdapter();

    private int page = 1;
    private int requestType;
    private int type = 1;

    private boolean isFirstRequest;//是否已经跳转过了，跳转过就不跳转了

    private boolean JUMP_TODO_PAGE;

    private boolean isClickEnabled = true; // 点击状态标志


    private int todo_status = 0;

    public TodoItemFragment() {
    }


    @Override
    public void initView(@Nullable Bundle savedInstanceState) {

        stringAdapter.setCustomSelectDrawable(CommonUtils.getBaseSelectedDrawable(getMActivity(), 50, R.color.base_primary_select, R.color.base_primary, 1));
        stringAdapter.setCustomUnSelectDrawable(CommonUtils.getBaseUnSelectedDrawable(getMActivity(), 50, R.color.white, R.color.base_primary_new_press, 1));


        getMDatabind().recycler.setLayoutManager(new GridLayoutManager(getActivity(), 4));
        getMDatabind().recycler.setAdapter(stringAdapter);
        getMDatabind().recycler.addItemDecoration(new MyGridSpacingItemDecoration(4, SizeUtils.dp2px(10), true));
        getTagList(null);
        stringAdapter.updateItem(0);

        getMDatabind().list.recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        getMDatabind().list.recyclerView.setAdapter(mAdapter);
        getMDatabind().list.recyclerView.addItemDecoration(new EqualSpacingItemDecoration(SizeUtils.dp2px(10)));
        getMDatabind().list.refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull @NotNull RefreshLayout refreshLayout) {
                requestMore();
            }

            @Override
            public void onRefresh(@NonNull @NotNull RefreshLayout refreshLayout) {
                requestOne();
            }
        });
    }

    @Override
    public void onLoadRetry() {
        lazyLoadData();
    }

    @Override
    public void lazyLoadData() {
        requestOne();
    }

    @Override
    public void initObserver() {
        //监听创建成员成功
        App.getAppViewModelInstance().getRefreshMember().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                clickChange(3);
            }
        });

        //监听是否需要跳转我创建的
        App.getAppViewModelInstance().getTodoMeCreate().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                clickChange(3);
            }
        });
    }

    @Override
    public void onRequestSuccess() {
        mViewModel.getTodoTotalEntity().observe(this, new Observer<TodoTotalEntity>() {
            @Override
            public void onChanged(TodoTotalEntity todoTotalEntity) {
                App.getAppViewModelInstance().getRefreshTodoTotal().setValue(todoTotalEntity);
                getTagList(todoTotalEntity);
            }
        });

        App.getAppViewModelInstance().getRefreshCopyRead().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean value) {//更新未读消息
                if (value) {
                    clickChange(3);
                }
            }
        });

        mViewModel.getTemplateEntity().observe(this, new Observer<TemplateEntity>() {
            @Override
            public void onChanged(TemplateEntity templateEntity) {
                if (templateEntity != null) {
                    new XPopup.Builder(getMActivity())
                            .asCustom(new PagerApprovePopup(getMActivity(), templateEntity.getList()))
                            .show();
                }

            }
        });


        mViewModel.getTodoList().observe(this, new Observer<TodoList>() {
            @Override
            public void onChanged(TodoList todoList) {
                getMDatabind().list.refreshLayout.finishRefresh();
                mAdapter.setType(type);
                switch (requestType) {
                    case TYPE_INIT_DATA:
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkInitData(todoList.getList(), mAdapter, getMDatabind().list.refreshLayout)) {
                            mAdapter.setList(todoList.getList());
                        }
                        break;
                    case TYPE_INIT_LOAD_MORE:
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkLoadMoreData(todoList.getList(), mAdapter, getMDatabind().list.refreshLayout)) {
                            mAdapter.addData(todoList.getList());
                        }
                        break;
                }
            }
        });
        mViewModel.getAgree().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                App.getAppViewModelInstance().getRefreshTodo().setValue(true);
                requestOne();
            }
        });

        mViewModel.getCancel().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                App.getAppViewModelInstance().getRefreshTodo().setValue(true);
                requestOne();
            }
        });

        mViewModel.getReject().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                App.getAppViewModelInstance().getRefreshTodo().setValue(true);
                requestOne();
            }
        });

        mViewModel.getDel().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                App.getAppViewModelInstance().getRefreshTodo().setValue(true);
                requestOne();
            }
        });

        mViewModel.getCopyRead().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                App.getAppViewModelInstance().getRefreshTodo().setValue(true);
                requestOne();
            }
        });
    }

    @Override
    public void onBindViewClick() {

        stringAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                todo_status = position;
                mAdapter.setTodo_status(todo_status);
                clickChange(position);
            }
        });

        getMDatabind().ivSetting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("appRoveMainPage")
                        .arguments(new HashMap<>())
                        .build());
            }
        });

        getMDatabind().ivAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mViewModel.requestTemplateList();
            }
        });

        mAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                Bundle bundle = new Bundle();
                bundle.putString("application_type", "" + mAdapter.getData().get(position).getApplication_type());
                bundle.putString("application_no", "" + mAdapter.getData().get(position).getApplication_no());
                bundle.putString("application_status", "" + mAdapter.getData().get(position).getApplication_status());
                bundle.putString("task_id", "" + mAdapter.getData().get(position).getTask_id());
                bundle.putString("read_status", "" + mAdapter.getData().get(position).getRead_status());
                bundle.putString("type", "" + type);
                bundle.putInt("todo_status", todo_status);//是待处理、抄送、我已处理、我创建的
                ActivityForwardUtil.startActivity(TodoDetailActivity.class, bundle);
            }
        });

        mAdapter.setOnItemChildClickListener(new OnItemChildClickListener() {
            @Override
            public void onItemChildClick(@NonNull @NotNull BaseQuickAdapter adapter, @NonNull @NotNull View view, int position) {
                if (!isClickEnabled) return; // 如果不允许点击，直接返回

                isClickEnabled = false; // 禁用点击

                // 延时重启点击
                new Handler().postDelayed(() -> isClickEnabled = true, 1000); // 500毫秒防抖

                TodoItemList todoItemList = mAdapter.getData().get(position);
                switch (view.getId()) {
                    case R.id.but_item_refuse://拒绝
                        mViewModel.requestTodoReject(mAdapter.getData().get(position).getTask_id(), "");
                        break;
                    case R.id.but_item_agree://同意
                        String applicationType = todoItemList.getApplication_type();
                        String applicationStatus = todoItemList.getApplication_status();

                        if ("1".equals(applicationType)) { // 入职审批
                            handleEntryApplication(todoItemList, applicationStatus);
                        } else if ("2".equals(applicationType)) { // 离职审批
                            handleExitApplication(todoItemList, applicationStatus);
                        } else if ("3".equals(applicationType)) { // 自定义物料
                            handleCustomMaterialApplication(todoItemList, applicationStatus);
                        } else { // 自定义的审批
                            handleCustomApproval(todoItemList, applicationStatus);
                        }
                        break;
                    case R.id.but_item_withdraw://撤回
                        mViewModel.requestTodoCancel(mAdapter.getData().get(position).getApplication_no());
                        break;
                    case R.id.but_item_del://删除
                        mViewModel.requestDel(mAdapter.getData().get(position).getApplication_no());
                        break;
                    case R.id.but_item_read://已读
                        mViewModel.requestRead(mAdapter.getData().get(position).getApplication_no());
                        break;
                    case R.id.but_item_contract://合同

//                        HashMap<String, Object> hashMap = new HashMap<>();
//                        hashMap.put("uuid", uuid);
//                        hashMap.put("user_name", user_name);
//                        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
//                                .pageName("contractRecordPage")
//                                .arguments(hashMap)
//                                .build());
                        break;
                    case R.id.but_item_credit://信用
//                        HashMap<String, Object> hashMap = new HashMap<>();
//                        hashMap.put("uuid", uuid);
//                        hashMap.put("id_number", id_number);
//                        hashMap.put("user_name", user_name);
//                        hashMap.put("type", "2");//类型 1入职查询 2员工查询
//                        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
//                                .pageName("creditInquiryPage")
//                                .arguments(hashMap)
//                                .build());
                        break;
                }
            }
        });

    }

    private void clickChange(int position) {
        stringAdapter.updateItem(position);
        switch (position) {
            case 0:
                type = 1;
                break;
            case 1:
                type = 4;
                break;
            case 2:
                type = 2;
                break;
            case 3:
                type = 3;
                break;
        }
        requestOne();
        mViewModel.requestTodoTotal();
    }

    @Override
    public void onResume() {
        super.onResume();
        lazyLoadData();
        JUMP_TODO_PAGE = MMKVHelper.getBoolean(ConstantMMVK.JUMP_TODO_PAGE, false);
        if (JUMP_TODO_PAGE && isAdded() && !isDetached() && getActivity() != null && !getActivity().isFinishing()) {
            // 延迟执行，避免Fragment事务冲突
            getMDatabind().list.recyclerView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        if (isAdded()) {
                            clickChange(3);
                            MMKVHelper.putBoolean(ConstantMMVK.JUMP_TODO_PAGE, false);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }, 200);
        }
    }

    private void getTagList(TodoTotalEntity data) {


        List<BaseStringNumEntity> strings = new ArrayList<>();
        if (data != null) {
            LogUtils.e("来这里了 ， " + data.toString());

            strings.add(new BaseStringNumEntity("待处理", "" + data.getSp_wait_total(), "#FF4040", "#FFFFFF"));
            strings.add(new BaseStringNumEntity("抄送我的", "" + data.getSp_copy_total(), "#8D8E99", "#FFFFFF"));
            strings.add(new BaseStringNumEntity("我已处理", "" + data.getSp_copy_total(), "#FEF6F3", "#FE6B03", true));
            strings.add(new BaseStringNumEntity("我创建的", "" + data.getSp_copy_total(), "#FEF6F3", "#FE6B03", true));

            if (!TextUtils.isEmpty(data.getSp_wait_total()) && Integer.parseInt(data.getSp_wait_total()) > 0 && !isFirstRequest && !JUMP_TODO_PAGE) {
                clickChange(0);
                isFirstRequest = true;
                return;
            }
            if (!TextUtils.isEmpty(data.getSp_copy_total()) && Integer.parseInt(data.getSp_copy_total()) > 0 && !isFirstRequest && !JUMP_TODO_PAGE) {
                clickChange(1);
                isFirstRequest = true;
                return;
            }
        } else {
            strings.add(new BaseStringNumEntity("待处理", "0", "#FF4040", "#FFFFFF"));
            strings.add(new BaseStringNumEntity("抄送我的", "0", "#8D8E99", "#FFFFFF"));
            strings.add(new BaseStringNumEntity("我已处理", "0", "#FEF6F3", "#FE6B03", true));
            strings.add(new BaseStringNumEntity("我创建的", "0", "#FEF6F3", "#FE6B03", true));
        }
        stringAdapter.setList(strings);
    }


    private void handleEntryApplication(TodoItemList item, String applicationStatus) {
        Bundle bundle = new Bundle();
        if ("0".equals(applicationStatus)) { // 待完善
            bundle.putInt("type", 1);
            bundle.putString("application_no", item.getApplication_no());
//            ActivityForwardUtil.startActivity(AddProjectActivity.class, bundle);
            gotoAddProjectStaffPage(item.getApplication_no(), 1);
        } else if ("3".equals(applicationStatus) || "4".equals(applicationStatus)) { // 已拒绝或已撤回，重新提交
            bundle.putInt("type", 2); // 走新建的流程，重新提交
            bundle.putString("application_no", item.getApplication_no());
            gotoAddProjectStaffPage(item.getApplication_no(), 2);
//            ActivityForwardUtil.startActivity(AddProjectActivity.class, bundle);
        } else {
            mViewModel.requestTodoAgree(item.getApplication_no(), item.getTask_id(), "");
        }
    }

    private void gotoAddProjectStaffPage(String application_no, int channel) {
//        HashMap<String, Object> params = new HashMap<>();
//        params.put("application_no", application_no);
//        params.put("channel", channel);
//        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
//                .pageName("AddStaffPage")
//                .arguments(params)
//                .build());
        CommonToFlutter.gotoFlutterAddStaffPage("",application_no,channel);
    }

    private void handleExitApplication(TodoItemList item, String applicationStatus) {
        if ("3".equals(applicationStatus) || "4".equals(applicationStatus)) { // 已拒绝或已撤回，重新提交
//            Bundle bundle = new Bundle();
//            bundle.putString("application_no", item.getApplication_no());
//            ActivityForwardUtil.startActivity(QuickDepartActivity.class, bundle);
            CommonToFlutter.gotoFlutterQuickDepartPage("", item.getApplication_no());
        } else {
            mViewModel.requestTodoAgree(item.getApplication_no(), item.getTask_id(), "");
        }
    }

    private void handleCustomMaterialApplication(TodoItemList item, String applicationStatus) {
        if ("0".equals(applicationStatus) || ("3".equals(applicationStatus) || "4".equals(applicationStatus))) { // 待完善 已拒绝 已撤回
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("uuid", item.getTemplate_uuid());
            hashMap.put("application_no", item.getApplication_no());
            hashMap.put("title", item.getApplication_title());
            FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                    .pageName("materialTemplatePage")
                    .arguments(hashMap)
                    .build());
        } else {
            mViewModel.requestTodoAgree(item.getApplication_no(), item.getTask_id(), "");
        }
    }

    private void handleCustomApproval(TodoItemList item, String applicationStatus) {
        if ("0".equals(applicationStatus) || ("3".equals(applicationStatus) || "4".equals(applicationStatus))) { // 待完善 已拒绝 已撤回
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("uuid", item.getTemplate_uuid());
            hashMap.put("application_no", item.getApplication_no());
            hashMap.put("title", item.getApplication_title());
            hashMap.put("application_status", applicationStatus);
            FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                    .pageName("approveTemplatePage")
                    .arguments(hashMap)
                    .build());
        } else {
            mViewModel.requestTodoAgree(item.getApplication_no(), item.getTask_id(), "");
        }
    }


    private void requestOne() {
        requestType = TYPE_INIT_DATA;
        page = 1;
        mViewModel.requestTodoList(page, type);
        mViewModel.requestTodoTotal();
    }

    private void requestMore() {
        requestType = TYPE_INIT_LOAD_MORE;
        page++;
        mViewModel.requestTodoList(page, type);
    }
}
