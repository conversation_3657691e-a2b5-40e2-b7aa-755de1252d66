package com.business_clean.ui.fragment.todo;

import static com.business_clean.app.config.Constant.TYPE_INIT_DATA;
import static com.business_clean.app.config.Constant.TYPE_INIT_LOAD_MORE;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseFragment;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.CheckListDataUtil;
import com.business_clean.app.weight.EqualSpacingItemDecoration;
import com.business_clean.data.mode.todo.TodoItemList;
import com.business_clean.data.mode.todo.TodoList;
import com.business_clean.data.mode.todo.TodoTotalEntity;
import com.business_clean.databinding.FragmentTodoItemChildBinding;
import com.business_clean.ui.adapter.todo.TodoAdapter;
import com.business_clean.viewmodel.request.TodoViewModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemChildClickListener;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;

public class TodoItemChildFragment extends BaseFragment<TodoViewModel, FragmentTodoItemChildBinding> {

    private TodoAdapter mAdapter = null;

    private int page = 1;
    private int requestType;
    private int type = 1;

    public TodoItemChildFragment() {
    }

    public TodoItemChildFragment(int type) {
        this.type = type;
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {

        mAdapter = new TodoAdapter();

        getMDatabind().list.recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        getMDatabind().list.recyclerView.setAdapter(mAdapter);
        getMDatabind().list.recyclerView.addItemDecoration(new EqualSpacingItemDecoration(SizeUtils.dp2px(10)));
        getMDatabind().list.refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull @NotNull RefreshLayout refreshLayout) {
                requestMore();
            }

            @Override
            public void onRefresh(@NonNull @NotNull RefreshLayout refreshLayout) {
                requestOne();
            }
        });
    }

    @Override
    public void onLoadRetry() {
        lazyLoadData();
    }

    @Override
    public void lazyLoadData() {
        requestOne();
    }

    @Override
    public void initObserver() {
        //监听创建成员成功
        App.getAppViewModelInstance().getRefreshMember().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                requestOne();
            }
        });

        //监听数字
        App.getAppViewModelInstance().getRefreshTodoTotal().observe(this, new Observer<TodoTotalEntity>() {
            @Override
            public void onChanged(TodoTotalEntity entity) {
            }
        });
    }

    @Override
    public void onRequestSuccess() {
        App.getAppViewModelInstance().getRefreshCopyRead().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean value) {//更新未读消息
                if (value) {
                    requestOne();
                }
            }
        });

        mViewModel.getTodoList().observe(this, new Observer<TodoList>() {
            @Override
            public void onChanged(TodoList todoList) {
                getMDatabind().list.refreshLayout.finishRefresh();
                mAdapter.setType(type);
                switch (requestType) {
                    case TYPE_INIT_DATA:
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkInitData(todoList.getList(), mAdapter, getMDatabind().list.refreshLayout)) {
                            mAdapter.setList(todoList.getList());
                        }
                        break;
                    case TYPE_INIT_LOAD_MORE:
                        // 根据数据来决定要显示哪一个状态的View
                        if (CheckListDataUtil.checkLoadMoreData(todoList.getList(), mAdapter, getMDatabind().list.refreshLayout)) {
                            mAdapter.addData(todoList.getList());
                        }
                        break;
                }
            }
        });
        mViewModel.getAgree().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                App.getAppViewModelInstance().getRefreshTodo().setValue(true);
                requestOne();
            }
        });

        mViewModel.getCancel().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                App.getAppViewModelInstance().getRefreshTodo().setValue(true);
                requestOne();
            }
        });

        mViewModel.getReject().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                App.getAppViewModelInstance().getRefreshTodo().setValue(true);
                requestOne();
            }
        });

        mViewModel.getDel().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                App.getAppViewModelInstance().getRefreshTodo().setValue(true);
                requestOne();
            }
        });

        mViewModel.getCopyRead().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                App.getAppViewModelInstance().getRefreshTodo().setValue(true);
                requestOne();
            }
        });
    }

    @Override
    public void onBindViewClick() {
        mAdapter.setOnItemChildClickListener(new OnItemChildClickListener() {
            @Override
            public void onItemChildClick(@NonNull @NotNull BaseQuickAdapter adapter, @NonNull @NotNull View view, int position) {
                TodoItemList todoItemList = mAdapter.getData().get(position);
                switch (view.getId()) {
                    case R.id.but_item_refuse://拒绝
                        mViewModel.requestTodoReject(mAdapter.getData().get(position).getTask_id(), "");
                        break;
                    case R.id.but_item_agree://同意
                        String applicationType = todoItemList.getApplication_type();
                        String applicationStatus = todoItemList.getApplication_status();

                        if ("1".equals(applicationType)) { // 入职审批
                            handleEntryApplication(todoItemList, applicationStatus);
                        } else if ("2".equals(applicationType)) { // 离职审批
                            handleExitApplication(todoItemList, applicationStatus);
                        } else if ("3".equals(applicationType)) { // 自定义物料
                            handleCustomMaterialApplication(todoItemList, applicationStatus);
                        } else { // 自定义的审批
                            handleCustomApproval(todoItemList, applicationStatus);
                        }
                        break;
                    case R.id.but_item_withdraw://撤回
                        mViewModel.requestTodoCancel(mAdapter.getData().get(position).getApplication_no());
                        break;
                    case R.id.but_item_del://删除
                        mViewModel.requestDel(mAdapter.getData().get(position).getApplication_no());
                        break;
                    case R.id.but_item_read://已读
                        mViewModel.requestRead(mAdapter.getData().get(position).getApplication_no());
                        break;
                    case R.id.but_item_contract://合同

//                        HashMap<String, Object> hashMap = new HashMap<>();
//                        hashMap.put("uuid", uuid);
//                        hashMap.put("user_name", user_name);
//                        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
//                                .pageName("contractRecordPage")
//                                .arguments(hashMap)
//                                .build());
                        break;
                    case R.id.but_item_credit://信用
//                        HashMap<String, Object> hashMap = new HashMap<>();
//                        hashMap.put("uuid", uuid);
//                        hashMap.put("id_number", id_number);
//                        hashMap.put("user_name", user_name);
//                        hashMap.put("type", "2");//类型 1入职查询 2员工查询
//                        FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
//                                .pageName("creditInquiryPage")
//                                .arguments(hashMap)
//                                .build());
                        break;
                }
            }
        });
    }


    private void handleEntryApplication(TodoItemList item, String applicationStatus) {
        Bundle bundle = new Bundle();
        if ("0".equals(applicationStatus)) { // 待完善
            bundle.putInt("type", 1);
            bundle.putString("application_no", item.getApplication_no());
//            ActivityForwardUtil.startActivity(AddProjectActivity.class, bundle);
            CommonToFlutter.gotoFlutterAddStaffPage("", item.getApplication_no(), 1);
        } else if ("3".equals(applicationStatus) || "4".equals(applicationStatus)) { // 已拒绝或已撤回，重新提交
            bundle.putInt("type", 2); // 走新建的流程，重新提交
            bundle.putString("application_no", item.getApplication_no());
//            ActivityForwardUtil.startActivity(AddProjectActivity.class, bundle);
            CommonToFlutter.gotoFlutterAddStaffPage("", item.getApplication_no(), 2);
        } else {
            mViewModel.requestTodoAgree(item.getApplication_no(), item.getTask_id(), "");
        }
    }

    private void handleExitApplication(TodoItemList item, String applicationStatus) {
        if ("3".equals(applicationStatus) || "4".equals(applicationStatus)) { // 已拒绝或已撤回，重新提交
//            Bundle bundle = new Bundle();
//            bundle.putString("application_no", item.getApplication_no());
//            ActivityForwardUtil.startActivity(QuickDepartActivity.class, bundle);
            CommonToFlutter.gotoFlutterQuickDepartPage("", item.getApplication_no());
        } else {
            mViewModel.requestTodoAgree(item.getApplication_no(), item.getTask_id(), "");
        }
    }

    private void handleCustomMaterialApplication(TodoItemList item, String applicationStatus) {
        if ("0".equals(applicationStatus) || ("3".equals(applicationStatus) || "4".equals(applicationStatus))) { // 待完善 已拒绝 已撤回
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("uuid", item.getTemplate_uuid());
            hashMap.put("application_no", item.getApplication_no());
            hashMap.put("title", item.getApplication_title());
            FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                    .pageName("materialTemplatePage")
                    .arguments(hashMap)
                    .build());
        } else {
            mViewModel.requestTodoAgree(item.getApplication_no(), item.getTask_id(), "");
        }
    }

    private void handleCustomApproval(TodoItemList item, String applicationStatus) {
        if ("0".equals(applicationStatus) || ("3".equals(applicationStatus) || "4".equals(applicationStatus))) { // 待完善 已拒绝 已撤回
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("uuid", item.getTemplate_uuid());
            hashMap.put("application_no", item.getApplication_no());
            hashMap.put("title", item.getApplication_title());
            FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                    .pageName("approveTemplatePage")
                    .arguments(hashMap)
                    .build());
        } else {
            mViewModel.requestTodoAgree(item.getApplication_no(), item.getTask_id(), "");
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        requestOne();
    }


    private void requestOne() {
        requestType = TYPE_INIT_DATA;
        page = 1;
        mViewModel.requestTodoList(page, type);
        mViewModel.requestTodoTotal();
    }

    private void requestMore() {
        requestType = TYPE_INIT_LOAD_MORE;
        page++;
        mViewModel.requestTodoList(page, type);
    }
}
