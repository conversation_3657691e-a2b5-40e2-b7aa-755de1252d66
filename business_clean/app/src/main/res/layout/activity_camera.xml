<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:context=".ui.activity.camera.WatermarkCameraActivity">

        <com.otaliastudios.cameraview.CameraView
            android:id="@+id/camera_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:cameraAudio="off"
            app:cameraEngine="camera2"
            app:cameraExperimental="true"
            app:cameraFacing="back"
            app:cameraFlash="off"
            app:cameraMode="picture"
            app:cameraPictureFormat="jpeg"
            app:cameraWhiteBalance="auto"
            app:layout_constraintBottom_toTopOf="@+id/ll_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="RelativeOverlap">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_time"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                app:layout_drawOnPictureSnapshot="false"
                app:layout_drawOnPreview="true"
                app:layout_drawOnVideoSnapshot="false">

                <View
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:layout_marginEnd="5dp"
                    android:background="@drawable/bg_video_time"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_time"
                    app:layout_constraintEnd_toStartOf="@+id/tv_time"
                    app:layout_constraintTop_toTopOf="@+id/tv_time" />

                <TextView
                    android:id="@+id/tv_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:shadowColor="#66000000"
                    android:text=""
                    android:textColor="#FFFFFFFF"
                    android:textSize="17sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_drawOnPictureSnapshot="false"
                app:layout_drawOnPreview="false"
                app:layout_drawOnVideoSnapshot="true">


                <com.business_clean.app.weight.camera.WaterMaskView
                    android:id="@+id/view_water_mask1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <LinearLayout
                    android:id="@+id/view_water_mask1_true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center|right"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="熊猫清洁云"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_8"
                        android:textStyle="bold" />

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="12dp"
                        android:src="@mipmap/icon_camera_true_time" />

                    <TextView
                        android:id="@+id/tv_mash_security"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="right"
                        android:maxLength="14"
                        android:text="防伪码 1234567890"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_6"
                        android:textStyle="bold" />
                </LinearLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.otaliastudios.cameraview.CameraView>

        <com.business_clean.app.weight.camera.CaptureFocuseView
            android:id="@+id/cfv_view"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:visibility="gone"
            tools:ignore="MissingConstraints" />

        <!--水印-->
        <com.business_clean.app.weight.camera.WaterMaskView
            android:id="@+id/view_water_mask"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@+id/ll_bottom"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_goneMarginBottom="90dp" />


        <RelativeLayout
            android:id="@+id/rl_upload_result_layout"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/ll_bottom"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_goneMarginBottom="90dp">

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/base_primary" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:layout_marginRight="4dp"
                    android:src="@mipmap/icon_workcircle_upload" />

                <TextView
                    android:id="@+id/tv_upload_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="上传成功"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_16" />
            </LinearLayout>


        </RelativeLayout>

        <LinearLayout
            android:id="@+id/preImgLinear"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#DE000000"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/preImg"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="40dp"
                android:layout_marginTop="80dp"
                android:layout_marginEnd="40dp"
                android:layout_marginBottom="180dp"
                android:background="@drawable/shape_stock_white"
                android:scaleType="fitXY">

            </ImageView>
        </LinearLayout>


        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="20dp"
            android:src="@mipmap/icon_camera_back"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/ll_change_project"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="22dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingLeft="6dp"
            android:paddingTop="4dp"
            android:paddingRight="6dp"
            android:paddingBottom="4dp"
            app:bl_corners_radius="4dp"
            app:bl_solid_color="#50000000"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_project"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="未获取到项目"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_14" />

            <ImageView
                android:id="@+id/iv_project"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@mipmap/icon_change_white" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <ImageView
                android:id="@+id/iv_my_attendance"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:src="@mipmap/icon_camera_1"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent" />

            <ImageView
                android:id="@+id/iv_my_photo"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:src="@mipmap/icon_camera_2"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent" />


            <ImageView
                android:id="@+id/iv_puzzle"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:src="@mipmap/icon_camera_4"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent" />

            <ImageView
                android:id="@+id/iv_flash"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:src="@mipmap/icon_camera_flash"
                app:layout_constraintEnd_toEndOf="parent" />

            <ImageView
                android:id="@+id/iv_change_camera"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:src="@mipmap/icon_camera_5"
                app:layout_constraintEnd_toEndOf="parent" />


            <ImageView
                android:id="@+id/iv_setting"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentRight="true"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:src="@mipmap/icon_camera_6" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#000"
            android:orientation="vertical"
            android:paddingBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <ProgressBar
                android:id="@+id/progress"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:max="100"
                android:progress="0"
                android:progressDrawable="@drawable/progress_circle"
                android:visibility="gone" />

            <com.business_clean.app.weight.recycler.GalleryRecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tag_view"
                android:layout_width="7dp"
                android:layout_height="7dp"
                android:layout_gravity="center"
                android:layout_marginBottom="10dp"
                app:bl_corners_radius="50dp"
                app:bl_solid_color="@color/base_primary" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/ll_camera_work"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center|bottom"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@mipmap/icon_camera_work_unselected" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:text="现场"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_12" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/ll_camera_todo"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center|bottom"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@mipmap/icon_camera_todo_unselected" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:text="待办"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_12" />
                </LinearLayout>

                <FrameLayout
                    android:id="@+id/fl_capture"
                    android:layout_width="0dp"
                    android:layout_height="76dp"
                    android:layout_weight="1"
                    android:clipChildren="false"
                    android:clipToPadding="false">

                </FrameLayout>

                <!--                <com.business_clean.app.weight.camera.CaptureButton-->
                <!--                    android:id="@+id/but_circle_progress"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="80dp"-->
                <!--                    android:layout_gravity="center"-->
                <!--                    android:layout_weight="1"-->
                <!--                    android:visibility="gone" />-->


                <LinearLayout
                    android:id="@+id/ll_camera_roster"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center|bottom"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@mipmap/icon_camera_roster_unselected" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:text="花名册"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_12" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/ll_camera_circle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center|bottom"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@mipmap/icon_camera_circle_unselected" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:text="工作台"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_12" />
                </LinearLayout>


            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="@color/red"
            android:textSize="@dimen/font_size_14"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="MissingConstraints" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>