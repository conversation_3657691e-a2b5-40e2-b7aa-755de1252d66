<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <LinearLayout
            android:id="@+id/ll_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:bl_gradient_endColor="@color/base_primary"
            app:bl_gradient_startColor="@color/base_primary"
            app:layout_scrollFlags="scroll|enterAlways">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal"
                android:paddingLeft="14dp"
                android:paddingTop="10dp"
                android:paddingRight="14dp"
                android:paddingBottom="10dp">

                <LinearLayout
                    android:id="@+id/ll_change_project"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_project_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginBottom="2dp"
                        android:maxEms="10"
                        android:maxLength="10"
                        android:singleLine="true"
                        android:text="全部项目"
                        android:textColor="@color/white"
                        android:textSize="@dimen/font_size_18"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/tv_change"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:src="@mipmap/icon_change_white" />
                </LinearLayout>


                <ImageView
                    android:id="@+id/iv_qr_scan"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="@dimen/font_size_20"
                    android:layout_toLeftOf="@+id/iv_notice"
                    android:src="@mipmap/icon_base_qr_scan" />

                <ImageView
                    android:id="@+id/iv_notice"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:src="@mipmap/icon_base_notice_un" />
            </RelativeLayout>

        </LinearLayout>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srlAccentColor="@color/white"
            app:srlDragRate="0.75"
            app:srlEnableOverScrollBounce="true"
            app:srlEnableOverScrollDrag="true"
            app:srlPrimaryColor="@color/base_primary">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:id="@+id/main_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="true">

                <com.google.android.material.appbar.AppBarLayout
                    android:id="@+id/app_bar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:elevation="0dp"
                    app:layout_behavior=".app.util.MyBehavior">

                    <com.google.android.material.appbar.CollapsingToolbarLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:elevation="0dp"
                        app:layout_scrollFlags="scroll|exitUntilCollapsed">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            app:bl_gradient_endColor="#E0FFF1"
                            app:bl_gradient_startColor="@color/base_primary"
                            app:layout_scrollFlags="scroll|enterAlways|snap">

                            <!--今日考勤的内容-->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="14dp"
                                android:layout_marginRight="14dp"
                                android:layout_marginBottom="10dp"
                                android:orientation="vertical"
                                android:paddingLeft="10dp"
                                android:paddingTop="16dp"
                                android:paddingRight="10dp"
                                app:bl_corners_radius="10dp"
                                app:bl_solid_color="@color/white">

                                <TextView
                                    android:id="@+id/ll_attendant"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableRight="@mipmap/icon_black_arrow"
                                    android:text="今日考勤"
                                    android:textColor="@color/base_primary_text_title"
                                    android:textSize="@dimen/font_size_15"
                                    android:textStyle="bold" />

                                <!--考勤数据-->
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginBottom="10dp">

                                    <LinearLayout
                                        android:id="@+id/ll_work_in"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical"
                                        android:paddingTop="4dp"
                                        android:paddingBottom="4dp"
                                        app:bl_corners_bottomLeftRadius="4dp"
                                        app:bl_corners_topLeftRadius="4dp"
                                        app:bl_solid_color="#E6F7F0">

                                        <TextView
                                            android:id="@+id/tv_stat_in_work_num"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="0"
                                            android:textColor="#00B062"
                                            android:textSize="@dimen/font_size_18"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="全部"
                                            android:textColor="@color/base_primary_text_title"
                                            android:textSize="@dimen/font_size_12" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/ll_work_attendance"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical"
                                        android:paddingTop="4dp"
                                        android:paddingBottom="4dp"
                                        app:bl_solid_color="#EFFFFB">

                                        <TextView
                                            android:id="@+id/tv_stat_attendance_num"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="0"
                                            android:textColor="@color/base_primary"
                                            android:textSize="@dimen/font_size_18"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="出勤"
                                            android:textColor="@color/base_primary_text_title"
                                            android:textSize="@dimen/font_size_12" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/ll_work_error"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical"
                                        android:paddingTop="4dp"
                                        android:paddingBottom="4dp"
                                        app:bl_solid_color="#FFF4F4">

                                        <TextView
                                            android:id="@+id/tv_stat_abnormal_num"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="0"
                                            android:textColor="@color/red"
                                            android:textSize="@dimen/font_size_18"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="异常"
                                            android:textColor="@color/base_primary_text_title"
                                            android:textSize="@dimen/font_size_12" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/ll_work_overtime"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical"
                                        android:paddingTop="4dp"
                                        android:paddingBottom="4dp"
                                        app:bl_corners_bottomRightRadius="4dp"
                                        app:bl_corners_topRightRadius="4dp"
                                        app:bl_solid_color="#ECF6FF">

                                        <TextView
                                            android:id="@+id/tv_stat_work_time_num"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="0"
                                            android:textColor="@color/base_primary_blue"
                                            android:textSize="@dimen/font_size_18"
                                            android:textStyle="bold" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="加班"
                                            android:textColor="@color/base_primary_text_title"
                                            android:textSize="@dimen/font_size_12" />
                                    </LinearLayout>
                                </LinearLayout>


                                <!--工作进度-->
                                <LinearLayout
                                    android:id="@+id/ll_work_circle_progress"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp">

                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="工作进度"
                                        android:textColor="@color/base_primary_text_title"
                                        android:textSize="@dimen/font_size_15"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/tv_stat_work_progress"
                                        android:layout_width="wrap_content"
                                        android:layout_height="22dp"
                                        android:drawableRight="@mipmap/icon_black_arrow"
                                        android:drawablePadding="6dp"
                                        android:gravity="center"
                                        android:text="共0个｜完成0个"
                                        android:textColor="@color/base_primary_text_title"
                                        android:textSize="@dimen/font_size_14" />

                                </LinearLayout>

                                <ProgressBar
                                    android:id="@+id/progress"
                                    style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                                    android:layout_width="match_parent"
                                    android:layout_height="10dp"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginBottom="10dp"
                                    android:max="100"
                                    android:progress="0"
                                    android:progressDrawable="@drawable/progress_circle" />

                                <!--入职情况-->
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:orientation="horizontal">

                                    <LinearLayout
                                        android:id="@+id/ll_employment"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:paddingBottom="16dp">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:drawableLeft="@mipmap/icon_project_add_project"
                                            android:drawableRight="@mipmap/icon_black_small_arrow"
                                            android:drawablePadding="6dp"
                                            android:singleLine="true"
                                            android:text="入职"
                                            android:textColor="@color/base_primary_text_title"
                                            android:textSize="@dimen/font_size_13" />

                                    </LinearLayout>


                                    <LinearLayout
                                        android:id="@+id/fl_deport"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:paddingBottom="16dp">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center"
                                            android:drawableLeft="@mipmap/icon_circle_deport"
                                            android:drawableRight="@mipmap/icon_black_small_arrow"
                                            android:drawablePadding="6dp"
                                            android:singleLine="true"
                                            android:text="离职"
                                            android:textColor="@color/base_primary_text_title"
                                            android:textSize="@dimen/font_size_13" />
                                    </LinearLayout>


                                    <LinearLayout
                                        android:id="@+id/fl_clean_report"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:paddingBottom="16dp">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center"
                                            android:drawableLeft="@mipmap/icon_circle_report"
                                            android:drawableRight="@mipmap/icon_black_small_arrow"
                                            android:drawablePadding="6dp"
                                            android:singleLine="true"
                                            android:text="报告"
                                            android:textColor="@color/base_primary_text_title"
                                            android:textSize="@dimen/font_size_13" />
                                    </LinearLayout>


                                    <FrameLayout
                                        android:id="@+id/fl_project_detail"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:paddingBottom="16dp">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center"
                                            android:drawableLeft="@mipmap/icon_circle_one"
                                            android:drawableRight="@mipmap/icon_black_small_arrow"
                                            android:drawablePadding="6dp"
                                            android:singleLine="true"
                                            android:text="拼图"
                                            android:textColor="@color/base_primary_text_title"
                                            android:textSize="@dimen/font_size_13" />
                                    </FrameLayout>

                                </LinearLayout>
                            </LinearLayout>

                        </LinearLayout>
                    </com.google.android.material.appbar.CollapsingToolbarLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/ll_chat_statistics"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:orientation="horizontal"
                            android:paddingLeft="12dp"
                            android:paddingRight="12dp">

                            <TextView
                                android:id="@+id/tv_circle_date"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_centerInParent="true"
                                android:layout_gravity="center"
                                android:background="@color/white"
                                android:gravity="center"
                                android:src="@mipmap/icon_base_add"
                                android:text=""
                                android:textColor="@color/base_primary"
                                android:textSize="@dimen/font_size_15"
                                android:visibility="visible" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycler_tag"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="10dp"
                                android:layout_marginRight="4dp"
                                android:layout_weight="1"
                                android:overScrollMode="never"
                                android:scrollbars="none" />

                        </LinearLayout>

                        <include layout="@layout/view_line" />

                        <RelativeLayout
                            android:id="@+id/rl_upload"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:orientation="horizontal"
                            android:visibility="gone">

                            <ProgressBar
                                android:id="@+id/progress_upload"
                                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:max="100"
                                android:progress="80"
                                android:progressDrawable="@drawable/progress_upload" />

                            <TextView
                                android:id="@+id/tv_upload_tips"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:drawableLeft="@mipmap/icon_workcircle_upload"
                                android:drawableRight="@mipmap/icon_green_small_arrow"
                                android:drawablePadding="6dp"
                                android:gravity="center"
                                android:singleLine="true"
                                android:text="正在上传0张照片/视频"
                                android:textColor="@color/base_primary"
                                android:textSize="@dimen/font_size_14" />


                        </RelativeLayout>

                    </LinearLayout>


                </com.google.android.material.appbar.AppBarLayout>


                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/white"
                        android:divider="@android:color/transparent"
                        android:overScrollMode="never"
                        android:paddingLeft="4dp"
                        android:paddingRight="4dp"
                        android:scrollbars="none" />

                    <LinearLayout
                        android:id="@+id/ll_empty"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingTop="150dp"
                        android:paddingBottom="150dp"
                        android:visibility="gone">

                        <ImageView
                            android:id="@+id/loading_emptyimg"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/icon_empty" />

                        <TextView
                            android:id="@+id/tv_empty_data"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="暂无数据"
                            android:textColor="#333333"
                            android:textSize="@dimen/font_size_15" />

                    </LinearLayout>

                    <ImageView
                        android:id="@+id/floating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginRight="20dp"
                        android:layout_marginBottom="30dp"
                        android:src="@mipmap/icon_back_top" />
                </RelativeLayout>
                <!--            </com.scwang.smart.refresh.layout.SmartRefreshLayout>-->

            </androidx.coordinatorlayout.widget.CoordinatorLayout>


        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </LinearLayout>
</layout>