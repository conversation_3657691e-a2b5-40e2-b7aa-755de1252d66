<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:orientation="horizontal"
        android:paddingLeft="16dp"
        android:paddingTop="10dp"
        android:paddingRight="16dp"
        android:paddingBottom="10dp"
        app:bl_corners_radius="4dp"
        app:bl_solid_color="@color/white">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center|left"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_item_custom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center|left"
                android:layout_marginRight="20dp"
                android:layout_weight="1"
                android:gravity="center"
                android:singleLine="true"
                android:text="北京万达"
                android:textColor="@color/base_primary_text_title"
                android:textSize="@dimen/font_size_16"/>

            <TextView
                android:id="@+id/tv_item_custom_sub"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tv_item_custom"
                android:layout_gravity="center|left"
                android:layout_marginRight="20dp"
                android:layout_weight="1"
                android:gravity="center"
                android:singleLine="true"
                android:text="北京万达"
                android:textColor="@color/base_primary_text_caption"
                android:textSize="@dimen/font_size_14" />
        </LinearLayout>

        <CheckBox
            android:id="@+id/ck_item_choose"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:background="@drawable/base_checkbox"
            android:button="@null" />


    </LinearLayout>
</layout>