<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="com.obiscr.chatgpt.settings.EasyCodeState">
    <option name="projectFiles" value="$PROJECT_DIR$/app/release/output-metadata.json;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/androidTest/java/com/business_clean/ExampleInstrumentedTest.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/base/BaseActivity.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/base/BaseAgentWebActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/base/BaseFragment.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/FetchAccessTokenCallBackImpl.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/GetUrlCacheBitmapListenerWithFail.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogCancelListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogCityIdPickerListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogCityPickerListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogClockDateSelect.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogConfirmListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogCreateFinish.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogDoubleDateConfirmListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogISelectTimeCallback.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogRestDayListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogSelectListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogTimePickerListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnDialogViewClickListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/OnNationListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/ProgressListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/callback/ProviceCityAreaDataCallBack.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/config/AppTaskFactory.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/config/AsyncRequestUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/config/Constant.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/config/ConstantMMVK.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/config/OnJsBridge.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/event/AppViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/ext/CommonUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/ext/CustomViewExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/ext/LoadingDialogExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/fdd/FddWebActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/fdd/H5FaceWebChromeClient.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/fdd/WBH5FaceVerifySDK.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/flutter/bean/BaseSerializableFlutterMap.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/flutter/BoostDelegate.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/flutter/BoostEventListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/flutter/FlutterManager.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/flutter/XmFlutterBoostActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/network/MyHeadInterceptor.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/network/NetHttpClient.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/network/NetUrl.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/network/NetworkApi.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/network/ResponseParser.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/network/TokenOutInterceptor.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/service/LocService.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/service/MarkUploadService.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/service/ServerReporter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/AllDialogShowStrategy.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/CustomApkFileCreator.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/MyDownloadWorker.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/MyFileChecker.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/MyInstallNotifier.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/MyUpdateChecker.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/MyUpdateNotifier.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/NotificationDownloadCreator.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/NotificationInstallCreator.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/NotificationUpdateCreator.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/OkhttpCheckWorker.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/OkhttpDownloadWorker.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/SetingDialogShowStrategy.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/update/ToastCallback.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/appupdate/AppCheckUpdateUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/pay/AliPayTool.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/pay/PayResult.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/pay/WxPayTool.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/share/ShareHelperTools.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/share/ShareMiniWeChatParams.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/share/ShareParams.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/share/ShareType.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/share/SocialBitmapUtils.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ActivityForwardUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/AnimationUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/CheckListDataUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ChoosePicUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/Contacts.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/DateUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/DecimalDigitsInputFilter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/DividerItemDecoration.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/DownLoadHelper.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/DownloadUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/FragmentShowHideUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/GlideEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/GlideRoundTransform.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/GlideUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ImageCompressor.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ImageLoaderUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/MMKVHelper.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/Mp4Utils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/MyBehavior.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/PhotoBitmapUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ResourceUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ResultDouble.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ResultFour.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ResultSingle.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ResultThree.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/RxHelper.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ScanFileUtil.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ShareUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/Sign.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/SoftHideKeyBoardUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/ToastUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/util/UploadFileHelper.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/calendar/FullMonthView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/calendar/FullWeekView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/camera/CameraView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/camera/CaptureButton.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/camera/CaptureFocuseView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/camera/CaptureListener.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/camera/CircleProgressButtonView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/camera/VideoControlView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/camera/WaterMaskView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/picker/CustomMinutesNumericWheelAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/picker/DayWheelAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/picker/XmMinutesWheelAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/picker/XmWheelTime.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CameraSettingDialog.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/ContractSharePopup.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomAttendancePartShadowPopup.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomBottomListPopup.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomCityPickerPopup.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomClockDate.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomClockInDialog.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomConfirmPopupView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomContentHighlightPop.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomDialogSlideSingleList.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomDoubleTimePickerPopup.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomImageViewerPopup.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomImageViewPagerPopup.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomRestDayWheelPopup.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomSystemAlertPop.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/CustomTimePickerPopup.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/InsureSharePopup.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/dialog/PagerDrawerPopup.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/loadCallBack/EmptyCallback.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/loadCallBack/ErrorCallback.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/loadCallBack/LoadingCallback.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/recycler/GalleryLayoutManager.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/recycler/HorizontalDecoration.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/ChrysanthemumView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/CustomAvatarView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/CustomPhotoView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/CustomTabBar.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/DrawingBitmapView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/EqualSpacingItemDecoration.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/FullPortConfig.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/HandwritingBoardView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/MyGridSpacingItemDecoration.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/SearchEditText.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/weight/WithBaseItemView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/app/App.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/dao/DaoManager.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/dao/WatermarkPhotoDatabaseManager.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/dao/WaterPhotoData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/database/greenDao/db/DaoMaster.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/database/greenDao/db/DaoSession.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/database/greenDao/db/WaterPhotoDataDao.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/city/ProviceCityAreaData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/city/ProviceCityAreaListData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/popup/BaseBottomListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/BankInfoEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/BaseStringNumEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/MenuEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/NationEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/PayDataEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/PayDataInfo.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/PayWxDataInfo.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/QiniuInfo.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/SuperiorsLevelEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/TimestampEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/initconfig/WaterMarkBitmapData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/address/AddressDetailEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/address/AddressEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/address/AddressListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/address/CustomPoiEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/address/LeaderListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/appupdate/DialogAlertNoticeInfo.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/appupdate/NewVersionData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/appupdate/NewVersionInfo.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/attendance/AttendanceEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/attendance/AttendanceListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/attendance/FilterAttendanceData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/attendance/MyAttendanceDetailClockEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/attendance/MyAttendanceDetailEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/attendance/MyAttendanceDetailListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/attendance/MyAttendanceDetailListItemEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/attendance/MyAttendanceEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/attendance/MyAttendanceItemEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/baseapi/ApiResponse.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/camera/LeaderClockEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/camera/LeaderClockListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/camera/LeaderClockUserListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/camera/LeaderWorkAddressEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/camera/TaskSection.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/camera/TodayTaskEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/camera/TodayTaskEntityList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/camera/TodoTitleEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/circle/ChatMessage.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/circle/ChatMessageCompact.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/circle/ChatMessageCompactLIst.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/circle/ChatMessageLIst.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/circle/ChatStatEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/circle/CircleTagEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/circle/StatAttendanceEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/circle/StatEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/circle/StatWorkEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/classes/ClassesDetailEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/classes/ClassesEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/classes/ClassesListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/contact/CompanyContactEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/contact/ContactListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/contact/ContactMangerEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/contact/ContractPaperEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/contract/ChooseFileEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/contract/ContractShareInfoEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/contract/ContraRegisterEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/custom/CompanyBaseEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/custom/CustomDetailsEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/custom/CustomMangerDetailEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/custom/CustomMangerEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/custom/CustomMangerList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/custom/WorkPostData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/format/FormatChildItemList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/format/FormatEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/format/FormatItemList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/group/GroupMangerEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/group/GroupMangerListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/init/BaseIDNameEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/init/ClassTime.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/init/InitDataEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/init/OutTime.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/job/JobEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/job/JobListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/leader/CustomMarkLeaderData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/login/ALiAuth.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/login/CodeEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/login/CompanyEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/login/UserEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/login/UserInfo.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/BankEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContactEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractCreateEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractInfoEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractInfoSignSettingEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractInfoSignSettingLabelCodeEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractInfoSignSettingStatusEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractTagEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractTagSubLabelListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractTemplateEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractTemplateListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/ContractTemplateListSettingEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/CreditListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/HealthyEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/IDCardMemberEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/IdCardPicEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/IdentityEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/InsureEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/MembersDetailEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/OCRBankInfoEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/OCRIDCardInfoEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/members/WorkInfoEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/permission/PermissionData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/permission/PermissionList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/project/CheckClockInEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/project/ProjectCompanyDetailEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/project/ProjectManager.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/project/ProjectMangerList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/project/StringProjectEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/project/WorkRulesEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/roster/RosterEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/roster/RosterFilterData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/roster/RosterList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/todo/PlanEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/todo/PlanListEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/todo/TodoItemList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/todo/TodoList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/todo/TodoSummaryList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/todo/TodoTotalEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/workbench/BalanceEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/workbench/HomeEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/workbench/HomeGridItem.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/workbench/HomeGridList.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/workbench/NoticeStatistics.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/workbench/QualityStatistics.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/workbench/WindStatistics.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/workbench/WorkBenchEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/BaseDownLoadEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/BaseMediaEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/data/mode/ImageEntity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/address/AddAddressActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/address/AddressManagerActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/address/PreviewAddressActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/attendance/MyAttendanceActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/attendance/ProjectAttendanceActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/balance/CorporateRechargeActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/camera/CameraSettingActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/camera/EditPhotoActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/camera/LeaderClockActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/camera/WatermarkCameraActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/camera/WatermarkCameraSimpleActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/classes/AddClassesActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/classes/ClassesMangerActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/clean/CleanReportActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/company/SelectCompanyActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/contract/AddContractAgreementActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/contract/ChooseNativeWorkFileActivity.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/custom/AddCompanyActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/custom/AddCustomActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/custom/AddProjectActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/custom/CommonContactManagementActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/custom/ContractPreviewActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/custom/ContractSignWebActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/custom/CustomDetailActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/custom/CustomManagerActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/depart/DepartActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/depart/QuickDepartActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/format/FormatActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/group/AddGroupActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/group/GroupManagerActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/insure/ClaimsProcessActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/insure/ElePolicyActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/login/LoginActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/login/TestActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/main/MainActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/me/MyCenterActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/permissions/PermissionsActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/personnel/PersonnelDetailActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/personnel/SelectPersonnelActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/personnel/SignHandActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/project/AddCompanyProjectActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/project/CompanyProjectDetailActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/project/ProjectManagerActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/roster/RosterFilterActivity.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/todo/TodoDetailActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/todo/WorkTaskDetailActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/BaseH5Activity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/ErrorActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/PreviewPhotoActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/SplashActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/activity/StartActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/addproject/CreditAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/address/AddressMangerAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/attendance/CustomMenuAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/attendance/DateAttendanceAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/attendance/DateAttendanceMonthAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/attendance/MyAttendanceAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/camera/LeaderAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/camera/LeaderItemAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/camera/TaskAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/circle/ChatAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/circle/ChatCompactAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/circle/ChatCompactChildAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/circle/CircleTagAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/classes/ClassesManageAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/company/CompanyAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/custom/contact/ContactManagerAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/custom/permission/PermissionAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/custom/CustomMangerAdapter.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/custom/CustomMangerAllAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/custom/CustomMangerNoPaddingAllAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/custom/ProjectWorkAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/depart/DepartAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/dialog/BaseBottomAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/format/FormatAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/format/FormatRowAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/group/GroupMangerAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/home/<USER>/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/home/<USER>/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/personnel/AddProjectAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/personnel/SelectPersonnelAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/project/CompanyProjectClassesAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/project/CompanyProjectContactsAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/project/CompanyProjectGroupAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/project/ProjectAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/roster/RosterAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/roster/RosterRowAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/todo/TaskImageAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/todo/TodoAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/todo/TodoItemAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/todo/TodoPlanAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/viewpager2/FragmentLazyPagerAdapter.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/viewpager2/FragmentLazyStateAdapter.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/watermark/WaterClockAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/AddressChooseAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/BaseChooseSingAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/BaseMediaGridImageAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/BaseMenuAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/BaseStringAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/BaseStringHorizontalAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/BaseStringNumAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/ChooseFileAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/GridImageAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/adapter/TodoStringNumAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/addproject/BankCardFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/addproject/ContactInformationFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/addproject/ContractFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/addproject/EmploymentInformationFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/addproject/IdCardFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/addproject/OtherFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/addproject/RiskFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/addproject/SalaryFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/attendance/DateAttendanceFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/attendance/DateAttendanceMonthFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/main/CameraFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/main/RosterFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/main/TodoFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/main/WorkbenchFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/main/WorkingCircleFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/todo/TodoItemChildFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/todo/TodoItemFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/todo/TodoPlanChildFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/todo/TodoPlanFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/todo/TodoPlanWorkChildFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/ui/fragment/todo/TodoPlanWorkFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/AddressViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/AttendanceViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/CameraViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/CircleViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/ClassexViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/ContactViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/ContractViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/CustomViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/DepartViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/FormatViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/GroupViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/HomeVideModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/LoginVideModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/MainViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/MyCenterViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/PermissionsViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/ProjectManagerViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/ProjectMembersViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/ProjectPersonnelViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/RosterViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/viewmodel/request/TodoViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/wxapi/WXEntryActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/java/com/business_clean/wxapi/WXPayEntryActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/anim/fade_in.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/anim/fade_out.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/drawable/base_checkbox.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/drawable/bg_video_time.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/drawable/ic_launcher_background.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/drawable/line_divider.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/drawable/login_btn_bg.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/drawable/progress_circle.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/drawable/progress_upload.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/drawable/search_edit_text_bg.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/drawable/shape_stock_white.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/drawable-v24/ic_launcher_foreground.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_add_address.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_add_classes.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_add_company.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_add_contract_agreement.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_add_custom.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_add_group.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_add_project.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_add_project_compan.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_address_manager.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_base_h5.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_camera.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_camera_setting.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_camera_smiple.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_choose_native_wordfile.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_claims_process.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_classes_manger.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_clean_report.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_common_contact_management.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_company_project.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_contract_preview.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_contract_sign_web.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_corporate_recharge.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_custom_detail.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_custom_manager.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_depart.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_edit_photo.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_ele_policy.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_error.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_fdd.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_format.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_group_manager.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_leader_clock.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_login.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_main.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_my_attendance.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_my_center.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_oactivity.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_permissions.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_personnel_detail.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_preview_address.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_preview_photo.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_project_attendance.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_project_manager.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_quick_depart.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_roster_filter.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_select_company.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_select_personnel.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_sign_hand.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_splash.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_start.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_test.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_todo_detail.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/activity_work_task_detail.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/base_remark_layout.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/base_upload_pic_layout.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/bottom_button_layout.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/bottom_double_button_layout.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/bottom_double_short_long_button_layout.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/common_dialog_custom_content_highlight.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/common_dialog_has_top_navigation.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/common_dialog_slide_list.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/common_popup_city_picker.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/common_popup_double_time_picker.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/common_popup_time_picker.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/custom_avatar.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/custom_tab_bar.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_attendance_part_shadow_popup.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_base_bottom_string.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_base_meterial.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_base_meterial_content_left_gravity.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_base_meterial_no_hign_light.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_camera_setting.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_clock_date.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_clock_date_more.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_clock_in.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_contract_share.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_image_preview_photo.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_image_view_pager_popup.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_image_viewer_popup.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_insure_share.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/dialog_system_alert_pop.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/empty_view.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/form_input_view.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_add_project_contract.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_bank_card.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_camera.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_contact.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_date_attendance.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_employ.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_id_card.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_other.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_risk.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_roster.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_salary.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_staging.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_todo.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_todo_item.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_todo_item_child.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_todo_plan.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_todo_plan_child.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_todo_plan_word.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_todo_plan_word_child.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/fragment_work_circle.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/gray_line.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/header_date_attendance.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/header_empty.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/header_home.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/header_home_footer.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/header_my_attendance.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/home_drawer.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_add_project_string_tag.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_address_manager.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_base_bottom_string.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_base_string_num_tag.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_base_string_tag.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_base_string_tag_horizontal.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_base_upload_pic.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_chat_message_compact.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_chat_message_compact_child.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_chat_message_right.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_choose_address.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_choose_file.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_choose_string.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_choose_water_clock.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_circle_tag.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_classes_manager.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_company_project_classes.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_company_project_contacts.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_company_project_group.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_contact_manager.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_credit.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_custom.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_custom_all.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_custom_no_padding.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_date_attendance.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_depart.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_edit_photo_task.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_edit_photo_task_title.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_empty_view.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_format.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_format_row.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_group_manager.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_home_card.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_home_item.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_layout_company.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_leader.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_leader_clock_footview.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_leader_item.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_menu_grid.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_my_attendance.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_permission.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_project.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_project_work.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_roster.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_roster_row.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_select_personnel.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_task_image.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_todo.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_todo_item.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_todo_item_plan.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/item_todo_string_num_tag.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/layou_titilebar_view.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/layout_base_list.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/layout_custom_photo.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/layout_custom_progress_dialog_view.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/layout_empty.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/layout_error.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/layout_list_footer.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/layout_loading.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/view_line.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/view_line_12.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/view_water_mask.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/layout/view_water_mask_security.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/values/atts.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/values/colors.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/values/dimens.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/values/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/values/styles.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/values/themes.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/values-v29/themes.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/xml/file_paths.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/res/xml/network_security_config.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/main/AndroidManifest.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/app/src/test/java/com/business_clean/ExampleUnitTest.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/androidTest/java/com/king/drawboard/ExampleInstrumentedTest.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/action/MotionAction.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/draw/Draw.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/draw/DrawBitmap.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/draw/DrawCircle.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/draw/DrawLine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/draw/DrawOval.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/draw/DrawPath.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/draw/DrawPoint.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/draw/DrawRect.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/draw/DrawText.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/java/com/king/drawboard/view/DrawBoardView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/res/values/attrs.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/main/AndroidManifest.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/drawboard/src/test/java/com/king/drawboard/ExampleUnitTest.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/androidTest/java/me/hgj/mvvmhelper/ExampleInstrumentedTest.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/BaseDbActivity.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/BaseDbFragment.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/BaseInitActivity.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/BaseInitFragment.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/BaseIView.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/BaseVBActivity.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/BaseVbFragment.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/BaseViewModel.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/BaseVmActivity.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/BaseVmFragment.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/base/Ktx.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/databinding/BooleanObservableField.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/databinding/ByteObservableField.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/databinding/DoubleObservableField.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/databinding/FloatObservableField.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/databinding/IntObservableField.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/databinding/ShortObservableField.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/databinding/StringObservableField.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/livedata/BooleanLiveData.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/livedata/ByteLiveData.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/livedata/DoubleLiveData.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/livedata/DownloadResultState.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/livedata/FloatLiveData.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/livedata/IntLiveData.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/livedata/ShortLiveData.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/core/livedata/StringLiveData.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/entity/PagerResponse.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/AdapterExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/AppExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/ClickExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/CommExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/DensityExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/DialogExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/ErrorExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/FuncationExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/GetViewModelExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/LogExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/NetCallbackExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/PhoneExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/RecyclerViewExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/TextViewExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/ViewBindUtil.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/ext/ViewExt.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/livedata/EventLiveData.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/interception/logging/util/CharacterHandler.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/interception/logging/util/LogUtils.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/interception/logging/util/UrlEncoderUtils.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/interception/logging/util/ZipHelper.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/interception/logging/DefaultFormatPrinter.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/interception/logging/FormatPrinter.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/interception/LogInterceptor.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/manager/NetState.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/manager/NetworkStateManager.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/manager/NetworkStateReceive.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/BaseNetConstant.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/LoadingDialogEntity.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/LoadingType.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/net/LoadStatusEntity.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/util/decoration/DefaultDecoration.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/util/decoration/DividerOrientation.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/util/decoration/ItemExpand.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/util/decoration/SpaceItemDecoration.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/util/DisplayUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/util/KtxActivityLifecycleCallbacks.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/util/NetworkUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/util/XLog.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/widget/state/BaseEmptyCallback.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/widget/state/BaseErrorCallback.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/java/me/hgj/mvvmhelper/widget/state/BaseLoadingCallback.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/res/layout/activity_base.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/res/layout/layout_base_empty.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/res/layout/layout_error.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/res/layout/layout_loading.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/res/layout/layout_loading_view.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/res/values/colors.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/res/values/dimens.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/res/values/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/res/values/styles.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/main/AndroidManifest.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/helper/src/test/java/me/hgj/mvvmhelper/ExampleUnitTest.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/calendar/ICalendar.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/calendar/NCalendar.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/calendar/NCalendarView.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/calendar/NPagerAdapter.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/calendar/NViewPager.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/calendar/NWeekBar.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/drawable/TextDrawable.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/enumeration/CalendarState.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/enumeration/CheckModel.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/enumeration/DateChangeBehavior.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/enumeration/MultipleCountModel.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/listener/OnCalendarChangedListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/listener/OnCalendarMultipleChangedListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/listener/OnCalendarScrollingListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/listener/OnCalendarStateChangedListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/listener/OnClickDisableDateListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/listener/OnEndAnimatorListener.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/painter/CalendarPainter.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/painter/InnerPainter.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/utils/hutool/ChineseDate.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/utils/hutool/GanZhi.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/utils/hutool/LunarFestival.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/utils/hutool/LunarInfo.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/utils/hutool/SolarFestival.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/utils/hutool/SolarTerms.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/utils/NAttrs.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/utils/NAttrsUtil.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/java/com/necer/utils/NDateUtil.kt;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/drawable/n_bg_checked_default.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/drawable/n_bg_checked_today.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/drawable/n_point_checked_default.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/drawable/n_point_checked_today.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/drawable/n_point_unchecked_default.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/drawable/n_point_unchecked_today.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/values/attrs.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/values/bools.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/values/colors.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/values/dimens.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/values/ids.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/values/integers.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/res/values/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/ncalendar/src/main/AndroidManifest.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/holder/AudioViewHolder.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/holder/BasePreviewHolder.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/holder/BaseRecyclerMediaHolder.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/holder/CameraViewHolder.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/holder/ImageViewHolder.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/holder/PreviewAudioHolder.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/holder/PreviewGalleryAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/holder/PreviewImageHolder.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/holder/PreviewVideoHolder.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/holder/VideoViewHolder.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/PictureAlbumAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/PictureImageGridAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/adapter/PicturePreviewAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/animators/AlphaInAnimationAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/animators/AnimationType.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/animators/BaseAnimationAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/animators/SlideInBottomAnimationAdapter.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/animators/ViewHelper.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/app/IApp.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/app/PictureAppMaster.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/FragmentInjectManager.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/IBridgeLoaderFactory.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/IBridgePictureBehavior.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/IBridgeViewLifecycle.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/InterpolatorFactory.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/IPictureSelectorCommonEvent.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/IPictureSelectorEvent.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureCommonFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureContentResolver.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureContextWrapper.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureFileProvider.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureMediaScannerConnection.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureSelectionCameraModel.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureSelectionModel.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureSelectionPreviewModel.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureSelectionQueryModel.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureSelectionSystemModel.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureSelector.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureSelectorSupporterActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/basic/PictureSelectorTransparentActivity.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/Crop.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/CustomIntentKey.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/FileSizeUnit.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/InjectResourceSource.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/PermissionEvent.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/PictureConfig.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/PictureMimeType.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/SelectLimitType.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/SelectMimeType.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/SelectModeConfig.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/SelectorConfig.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/SelectorProviders.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/config/VideoQuality.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/decoration/GridSpacingItemDecoration.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/decoration/HorizontalItemDecoration.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/decoration/ViewPage2ItemDecoration.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/decoration/WrapContentLinearLayoutManager.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/dialog/AlbumListPopWindow.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/dialog/PhotoItemSelectedDialog.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/dialog/PictureCommonDialog.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/dialog/PictureLoadingDialog.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/dialog/RemindDialog.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/CompressEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/CompressFileEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/CropEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/CropFileEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/ExtendLoaderEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/ImageEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/MediaPlayerEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/PictureSelectorEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/SandboxFileEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/UriToFileTransformEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/engine/VideoPlayerEngine.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/entity/LocalMedia.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/entity/LocalMediaFolder.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/entity/MediaData.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/entity/MediaExtraInfo.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/immersive/ImmersiveManager.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/immersive/LightStatusBarUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/immersive/RomUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnAlbumItemClickListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnBitmapWatermarkEventListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnCallbackIndexListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnCallbackListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnCameraInterceptListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnCustomLoadingListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnExternalPreviewEventListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnGridItemSelectAnimListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnInjectActivityPreviewListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnInjectLayoutResourceListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnItemClickListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnKeyValueResultCallbackListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnMediaEditInterceptListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnPermissionDeniedListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnPermissionDescriptionListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnPermissionsInterceptListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnPlayerListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnPreviewInterceptListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnQueryAlbumListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnQueryAllAlbumListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnQueryDataResultListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnQueryDataSourceListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnQueryFilterListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnRecordAudioInterceptListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnRecyclerViewPreloadMoreListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnRecyclerViewScrollListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnRecyclerViewScrollStateListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnRequestPermissionListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnResultCallbackListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnSelectAnimListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnSelectFilterListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnSelectLimitTipsListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/interfaces/OnVideoThumbnailEventListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/language/LanguageConfig.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/language/LocaleTransform.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/language/PictureLanguageUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/loader/IBridgeMediaLoader.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/loader/LocalMediaLoader.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/loader/LocalMediaPageLoader.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/loader/SandboxFileLoader.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/magical/BuildRecycleItemViewParams.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/magical/MagicalView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/magical/MagicalViewWrapper.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/magical/OnMagicalViewCallback.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/magical/ViewParams.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/manager/PictureCacheManager.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/manager/SelectedManager.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/obj/pool/ObjectPools.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/permissions/PermissionChecker.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/permissions/PermissionConfig.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/permissions/PermissionResultCallback.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/permissions/PermissionUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/Compat.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/CustomGestureDetector.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/OnGestureListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/OnMatrixChangedListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/OnOutsidePhotoTapListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/OnPhotoTapListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/OnScaleChangedListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/OnSingleFlingListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/OnViewDragListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/OnViewTapListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/PhotoView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/PhotoViewAttacher.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/photoview/Util.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/service/ForegroundService.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/style/AlbumWindowStyle.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/style/BottomNavBarStyle.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/style/PictureSelectorStyle.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/style/PictureWindowAnimationStyle.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/style/SelectMainStyle.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/style/TitleBarStyle.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/thread/PictureThreadUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/ActivityCompatHelper.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/AnimUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/BitmapUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/DateUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/DensityUtil.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/DoubleUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/DownloadFileUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/FileDirMap.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/FileUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/IntentUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/MediaStoreUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/MediaUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/PictureFileUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/PSEglUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/SandboxTransformUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/SdkVersionUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/SortUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/SpUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/StyleUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/ToastUtils.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/utils/ValueOf.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/BottomNavBar.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/CompleteSelectView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/MarqueeTextView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/MediaPlayerView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/MediumBoldTextView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/PreviewBottomNavBar.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/PreviewTitleBar.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/RecyclerPreloadView.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/RoundCornerRelativeLayout.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/SlideSelectionHandler.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/SlideSelectTouchListener.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/SquareRelativeLayout.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/widget/TitleBar.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/PictureOnlyCameraFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/PictureSelectorFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/PictureSelectorPreviewFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/java/com/luck/picture/lib/PictureSelectorSystemFragment.java;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_album_dismiss.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_album_show.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_alpha_enter.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_alpha_exit.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_anticipate_interpolator.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_down_out.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_enter.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_exit.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_fade_in.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_fade_out.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_fall_enter.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_layout_fall_enter.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_modal_in.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_modal_out.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_overshoot_interpolator.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/anim/ps_anim_up_in.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_album_bg.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_anim_progress.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_audio_placeholder.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_btn_left_bottom_selector.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_btn_left_normal.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_btn_left_select.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_btn_right_bottom_selector.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_btn_right_normal.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_btn_right_select.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_btn_selector.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_cancel_default_bg.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_checkbox_selector.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_default_num_oval_normal.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_default_num_oval_selected.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_default_num_selector.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_dialog_loading_bg.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_dialog_shadow.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_gif_tag.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_image_placeholder.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_item_select_bg.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_layer_progress.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_num_oval.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_orange_oval.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_original_checkbox.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_original_wechat_normal.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_original_wechat_selected.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_preview_checkbox_selector.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_preview_gallery_bg.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_preview_gallery_frame.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_seek_bar_thumb_normal.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_seek_bar_thumb_pressed.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_select_complete_bg.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_select_complete_normal_bg.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_transparent_space.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_view_normal.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/drawable/ps_view_press.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_activity_container.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_album_folder_item.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_alert_dialog.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_bottom_nav_bar.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_common_dialog.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_complete_selected_layout.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_custom_preview_image.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_dialog_camera_selected.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_empty.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_fragment_preview.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_fragment_selector.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_item_grid_audio.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_item_grid_camera.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_item_grid_image.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_item_grid_video.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_preview_audio.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_preview_gallery_item.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_preview_image.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_preview_video.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_remind_dialog.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_title_bar.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/layout/ps_window_folder.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values/attrs.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values/colors.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values/styles.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-ar-rAE/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-cs-rCZ/string.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-de-rDE/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-en-rUS/string.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-es-rES/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-fr-rFR/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-ja-rJP/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-kk-rKZ/string.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-ko-rKR/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-pt-rPT/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-ru-rRU/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-vi-rVN/string.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-zh-rCN/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/values-zh-rTW/strings.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/res/xml/ps_file_paths.xml;/Users/<USER>/AndroidStudioProjects/xiaoxiaong/panda/businesscleanandroidpro/business_clean/selector/src/main/AndroidManifest.xml" />
  </component>
</project>