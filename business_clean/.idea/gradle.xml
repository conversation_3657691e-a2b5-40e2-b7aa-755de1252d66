<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="testRunner" value="GRADLE" />
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="jbr-17" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/app" />
            <option value="$PROJECT_DIR$/cameraview" />
            <option value="$PROJECT_DIR$/drawboard" />
            <option value="$PROJECT_DIR$/helper" />
            <option value="$PROJECT_DIR$/selector" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
    <option name="offlineMode" value="true" />
  </component>
</project>